import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import Link from 'next/link'

interface DeclarationSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  declaration: any // Replace 'any' with a proper type if you have one
}

export function DeclarationSummaryModal({ isOpen, onClose, declaration }: DeclarationSummaryModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] logo-background-content enhanced-card">
        <DialogHeader>
          <DialogTitle>Résumé de la déclaration</DialogTitle>
          <DialogDescription>
            {declaration ? `Détails du séjour pour ${declaration.prenom} ${declaration.nom}` : 'Chargement des détails...'}
          </DialogDescription>
        </DialogHeader>
        {declaration ? (
          <ScrollArea className="mt-4 h-[60vh] rounded-md border p-4">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">Logement</h3>
                <p>{declaration.nomLogement}</p>
              </div>
              <div>
                <h3 className="font-semibold">Ville</h3>
                <p>{declaration.ville}</p>
              </div>
              <div>
                <h3 className="font-semibold">Nom complet</h3>
                <p>{declaration.prenom} {declaration.nom}</p>
              </div>
              <div>
                <h3 className="font-semibold">Nombre d'enfants</h3>
                <p>{declaration.enfants}</p>
              </div>
              <div>
                <h3 className="font-semibold">Date d'arrivée</h3>
                <p>{declaration.dateArrivee}</p>
              </div>
              <div>
                <h3 className="font-semibold">Date de départ</h3>
                <p>{declaration.dateDepart}</p>
              </div>
              <div>
                <h3 className="font-semibold">Date de traitement</h3>
                <p>{declaration.dateTraitement}</p>
              </div>
              <div className="pt-4">
                <Link href={`/declaration/${declaration.id}`} passHref>
                  <Button as="a" onClick={onClose}>
                    Voir le résumé complet
                  </Button>
                </Link>
              </div>
            </div>
          </ScrollArea>
        ) : (
          <div className="mt-4 text-center">Chargement des données...</div>
        )}
      </DialogContent>
    </Dialog>
  )
}

