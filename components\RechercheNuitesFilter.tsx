'use client'

import { useState } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const filterSchema = z.object({
  nomLogement: z.string().optional(),
  numeroDocument: z.string().optional(),
  typeDocument: z.string().optional(),
  nom: z.string().optional(),
  prenom: z.string().optional(),
  dateArriveeDebut: z.string().optional(),
  dateArriveeFin: z.string().optional(),
})

type FilterValues = z.infer<typeof filterSchema>

interface RechercheNuitesFilterProps {
  onFilter: (values: FilterValues) => void
}

export function RechercheNuitesFilter({ onFilter }: RechercheNuitesFilterProps) {
  const form = useForm<FilterValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      nomLogement: "",
      numeroDocument: "",
      typeDocument: "",
      nom: "",
      prenom: "",
      dateArriveeDebut: "",
      dateArriveeFin: "",
    },
  })

  const onSubmit = (values: FilterValues) => {
    onFilter(values)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="nomLogement"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom du logement</FormLabel>
                <FormControl>
                  <Input placeholder="Nom du logement" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="numeroDocument"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Numéro de document</FormLabel>
                <FormControl>
                  <Input placeholder="Numéro de document" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="typeDocument"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type de document</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner le type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="passeport">Passeport</SelectItem>
                    <SelectItem value="carte_identite">Carte d'identité</SelectItem>
                    <SelectItem value="permis_conduire">Permis de conduire</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="nom"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom</FormLabel>
                <FormControl>
                  <Input placeholder="Nom" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="prenom"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Prénom</FormLabel>
                <FormControl>
                  <Input placeholder="Prénom" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="dateArriveeDebut"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date d'arrivée (début)</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="dateArriveeFin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date d'arrivée (fin)</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <Button type="submit">Filtrer</Button>
      </form>
    </Form>
  )
}

