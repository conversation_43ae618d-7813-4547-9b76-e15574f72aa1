'use client'

import { useParams, useRouter } from 'next/navigation'
import { FullDeclarationSummary } from '@/components/FullDeclarationSummary'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { mockData } from '@/utils/mockData'
import { useEffect } from 'react'

export default function DeclarationPage() {
  const params = useParams()
  const router = useRouter()
  const id = params.id as string
  
  // In a real application, you would fetch the declaration data based on the ID
  // For this example, we'll use mock data
  const declaration = mockData.find(d => d.id.toString() === id)

  useEffect(() => {
    if (!declaration) {
      router.push('/recherche-nuites')
    }
  }, [declaration, router])

  if (!declaration) {
    return null // or a loading indicator
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <FullDeclarationSummary declaration={declaration} />
    </div>
  )
}

