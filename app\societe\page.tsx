'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  Building2, MapPin, Phone, Mail, Globe, FileText, Users,
  Edit, Save, X, CheckCircle, AlertTriangle, Calendar
} from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

// Mock company data
const companyData = {
  name: "Société de Gestion Immobilière",
  siret: "12345678901234",
  address: "123 Rue de la République",
  city: "Paris",
  postalCode: "75001",
  country: "France",
  phone: "+33 1 23 45 67 89",
  email: "<EMAIL>",
  website: "www.societe-gestion.fr",
  description: "Spécialisée dans la gestion de locations saisonnières et la déclaration de locataires.",
  founded: "2020",
  employees: "5-10",
  status: "active"
}

// Mock team members
const teamMembers = [
  {
    id: 1,
    name: "Marie Dubois",
    role: "Directrice",
    email: "<EMAIL>",
    phone: "+33 6 12 34 56 78",
    status: "active"
  },
  {
    id: 2,
    name: "Pierre Martin",
    role: "Gestionnaire",
    email: "<EMAIL>",
    phone: "+33 6 98 76 54 32",
    status: "active"
  },
  {
    id: 3,
    name: "Sophie Laurent",
    role: "Assistante",
    email: "<EMAIL>",
    phone: "+33 6 45 67 89 01",
    status: "inactive"
  }
]

export default function SocietePage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('info')
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(companyData)

  const handleSave = () => {
    // Here you would typically save to your backend
    setIsEditing(false)
    // Show success message
  }

  const handleCancel = () => {
    setFormData(companyData)
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Informations Société
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez les informations de votre société
                  </p>
                </div>
                <div className="flex gap-2">
                  {isEditing ? (
                    <>
                      <Button variant="outline" onClick={handleCancel}>
                        <X className="w-4 h-4 mr-2" />
                        Annuler
                      </Button>
                      <Button 
                        className="bg-institutional-primary hover:bg-green-600 text-white"
                        onClick={handleSave}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        Sauvegarder
                      </Button>
                    </>
                  ) : (
                    <Button 
                      className="bg-institutional-primary hover:bg-green-600 text-white"
                      onClick={() => setIsEditing(true)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Modifier
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              {/* Status Card */}
              <Card className="enhanced-card">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-green-600" />
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold text-gray-900">{formData.name}</h2>
                        <p className="text-sm text-gray-600">SIRET: {formData.siret}</p>
                      </div>
                    </div>
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Actif
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Main Content */}
              <Card className="enhanced-card">
                <CardContent className="p-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <div className="border-b border-gray-200 px-6 pt-6">
                      <TabsList className="grid w-full grid-cols-3 bg-gray-100 rounded-lg p-1">
                        <TabsTrigger value="info">Informations</TabsTrigger>
                        <TabsTrigger value="team">Équipe</TabsTrigger>
                        <TabsTrigger value="settings">Paramètres</TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="info" className="p-6 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Company Details */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-semibold text-gray-900">Détails de la Société</h3>
                          
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="name">Nom de la société</Label>
                              <Input
                                id="name"
                                value={formData.name}
                                onChange={(e) => setFormData({...formData, name: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                            
                            <div>
                              <Label htmlFor="siret">SIRET</Label>
                              <Input
                                id="siret"
                                value={formData.siret}
                                onChange={(e) => setFormData({...formData, siret: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                            
                            <div>
                              <Label htmlFor="description">Description</Label>
                              <Textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => setFormData({...formData, description: e.target.value})}
                                disabled={!isEditing}
                                rows={3}
                                className="mt-1"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Contact Information */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-semibold text-gray-900">Informations de Contact</h3>
                          
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="address">Adresse</Label>
                              <Input
                                id="address"
                                value={formData.address}
                                onChange={(e) => setFormData({...formData, address: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="city">Ville</Label>
                                <Input
                                  id="city"
                                  value={formData.city}
                                  onChange={(e) => setFormData({...formData, city: e.target.value})}
                                  disabled={!isEditing}
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label htmlFor="postalCode">Code postal</Label>
                                <Input
                                  id="postalCode"
                                  value={formData.postalCode}
                                  onChange={(e) => setFormData({...formData, postalCode: e.target.value})}
                                  disabled={!isEditing}
                                  className="mt-1"
                                />
                              </div>
                            </div>
                            
                            <div>
                              <Label htmlFor="phone">Téléphone</Label>
                              <Input
                                id="phone"
                                value={formData.phone}
                                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                            
                            <div>
                              <Label htmlFor="email">Email</Label>
                              <Input
                                id="email"
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({...formData, email: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                            
                            <div>
                              <Label htmlFor="website">Site web</Label>
                              <Input
                                id="website"
                                value={formData.website}
                                onChange={(e) => setFormData({...formData, website: e.target.value})}
                                disabled={!isEditing}
                                className="mt-1"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Company Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-gray-200">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{formData.founded}</p>
                          <p className="text-sm text-gray-600">Année de création</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{formData.employees}</p>
                          <p className="text-sm text-gray-600">Employés</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">12</p>
                          <p className="text-sm text-gray-600">Propriétés</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">156</p>
                          <p className="text-sm text-gray-600">Locataires</p>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="team" className="p-6 space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold text-gray-900">Membres de l'Équipe</h3>
                        <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                          <Users className="w-4 h-4 mr-2" />
                          Ajouter Membre
                        </Button>
                      </div>
                      
                      <div className="grid gap-4">
                        {teamMembers.map((member) => (
                          <Card key={member.id} className="border border-gray-200">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span className="text-sm font-semibold text-blue-700">
                                      {member.name.split(' ').map(n => n[0]).join('')}
                                    </span>
                                  </div>
                                  <div>
                                    <p className="font-medium text-gray-900">{member.name}</p>
                                    <p className="text-sm text-gray-600">{member.role}</p>
                                    <div className="flex items-center gap-4 mt-1">
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <Mail className="w-3 h-3" />
                                        {member.email}
                                      </span>
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <Phone className="w-3 h-3" />
                                        {member.phone}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    className={member.status === 'active' 
                                      ? "bg-green-100 text-green-800 border-green-200" 
                                      : "bg-gray-100 text-gray-800 border-gray-200"
                                    }
                                  >
                                    {member.status === 'active' ? 'Actif' : 'Inactif'}
                                  </Badge>
                                  <Button variant="ghost" size="sm">
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="settings" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Paramètres de Notification</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Notifications par email</p>
                                <p className="text-sm text-gray-600">Recevoir les notifications importantes par email</p>
                              </div>
                              <Switch defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Rapports automatiques</p>
                                <p className="text-sm text-gray-600">Générer des rapports mensuels automatiquement</p>
                              </div>
                              <Switch defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Sauvegarde automatique</p>
                                <p className="text-sm text-gray-600">Sauvegarder les données quotidiennement</p>
                              </div>
                              <Switch defaultChecked />
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Paramètres de Sécurité</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Authentification à deux facteurs</p>
                                <p className="text-sm text-gray-600">Sécuriser l'accès avec 2FA</p>
                              </div>
                              <Switch />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Connexions multiples</p>
                                <p className="text-sm text-gray-600">Autoriser plusieurs connexions simultanées</p>
                              </div>
                              <Switch defaultChecked />
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Zone de Danger</h3>
                          <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                            <div className="flex items-center gap-2 mb-2">
                              <AlertTriangle className="w-5 h-5 text-red-600" />
                              <p className="font-medium text-red-900">Supprimer la société</p>
                            </div>
                            <p className="text-sm text-red-700 mb-4">
                              Cette action est irréversible. Toutes les données seront définitivement supprimées.
                            </p>
                            <Button variant="destructive" size="sm">
                              Supprimer la société
                            </Button>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </div>
  )
}
