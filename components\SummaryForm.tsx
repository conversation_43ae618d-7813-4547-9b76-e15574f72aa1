'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import Image from 'next/image'
import {
  FileDown,
  LayoutDashboard,
  UserPlus,
  CheckCircle,
  User,
  Calendar,
  CreditCard,
  Camera,
  Pen,
  Shield,
  ArrowLeft,
  Download,
  Eye,
  Home
} from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { useIsMobile } from '@/hooks/use-mobile'

// Mock data - replace this with actual data passed from previous steps
const mockData = {
  nom: "Doe",
  prenom: "John",
  dateNaissance: "1990-01-01",
  numeroPieceIdentite: "AB123456",
  typePieceIdentite: "Passport",
  dateArrivee: "2023-07-01",
  dateDepart: "2023-07-15",
  logement: "Appartement 123",
  motifSejour: "Tourisme",
  idRectoUrl: "/placeholder.svg?height=200&width=320",
  idVersoUrl: "/placeholder.svg?height=200&width=320",
  signature: "/placeholder.svg?height=100&width=300",
  selfie: "/placeholder.svg?height=200&width=200"
}

export function SummaryForm() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const handlePrevious = () => {
    router.back()
  }

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true)
    try {
      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mockData),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = 'declaration_locataire.pdf'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
      } else {
        console.error('Failed to generate PDF')
      }
    } catch (error) {
      console.error('Error generating PDF:', error)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleReturnToDashboard = () => {
    router.push('/dashboard')
  }

  const handleDeclareAnotherTenant = () => {
    router.push('/renseignement-nuites')
  }

  return (
    <Card className="max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold">
          Résumé de la Déclaration
        </CardTitle>
      </CardHeader>
      <div className="px-6 py-4 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 justify-between">
        <Button
          variant="default"
          onClick={handleReturnToDashboard}
          className="w-full sm:w-auto"
        >
          <LayoutDashboard className="mr-2 h-4 w-4" />
          Retour au tableau de bord
        </Button>
        <Button
          variant="default"
          onClick={handleDeclareAnotherTenant}
          className="w-full sm:w-auto"
        >
          <UserPlus className="mr-2 h-4 w-4" />
          Déclarer un autre locataire
        </Button>
      </div>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold">Nom</h3>
            <p>{mockData.nom}</p>
          </div>
          <div>
            <h3 className="font-semibold">Prénom</h3>
            <p>{mockData.prenom}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date de naissance</h3>
            <p>{mockData.dateNaissance}</p>
          </div>
          <div>
            <h3 className="font-semibold">Numéro de pièce d'identité</h3>
            <p>{mockData.numeroPieceIdentite}</p>
          </div>
          <div>
            <h3 className="font-semibold">Type de pièce d'identité</h3>
            <p>{mockData.typePieceIdentite}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date d'arrivée</h3>
            <p>{mockData.dateArrivee}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date de départ</h3>
            <p>{mockData.dateDepart}</p>
          </div>
          <div>
            <h3 className="font-semibold">Logement</h3>
            <p>{mockData.logement}</p>
          </div>
          <div>
            <h3 className="font-semibold">Motif du séjour</h3>
            <p>{mockData.motifSejour}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold">Photos d'identité</h3>
          <div className="flex flex-wrap gap-4">
            <div>
              <p className="mb-2">Recto</p>
              <Image src={mockData.idRectoUrl} alt="ID Recto" width={320} height={200} className="rounded-lg" />
            </div>
            <div>
              <p className="mb-2">Verso</p>
              <Image src={mockData.idVersoUrl} alt="ID Verso" width={320} height={200} className="rounded-lg" />
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Signature du voyageur</h3>
          <Image src={mockData.signature} alt="Signature" width={300} height={100} className="rounded-lg" />
        </div>

        <div>
          <h3 className="font-semibold mb-2">Selfie du voyageur</h3>
          <Image src={mockData.selfie} alt="Selfie" width={200} height={200} className="rounded-lg" />
        </div>

        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 justify-between mt-6">
          <Button
            variant="outline"
            onClick={handlePrevious}
          >
            Précédent
          </Button>
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/creer-contrat')}
            >
              Créer un Contrat
            </Button>
            <Button
              variant="outline"
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
            >
              <FileDown className="mr-2 h-4 w-4" />
              {isGeneratingPDF ? 'Génération...' : 'Télécharger la déclaration (PDF)'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

