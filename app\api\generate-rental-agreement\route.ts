import { NextRequest, NextResponse } from 'next/server';
import { generateSimplePDFContent } from '@/utils/pdfGenerator';

export async function POST(req: NextRequest) {
  try {
    // Validate request
    if (!req.body) {
      return new NextResponse('Request body is required', { status: 400 });
    }

    const data = await req.json();

    // Validate data
    if (!data || typeof data !== 'object') {
      return new NextResponse('Invalid data format', { status: 400 });
    }

    // Try PDFKit first, fallback to simple PDF generation
    let pdfBuffer: Buffer;

    try {
      // Use dynamic import to avoid bundling issues
      const PDFDocument = (await import('pdfkit')).default;

      // Create a new PDF document with minimal configuration
      const doc = new PDFDocument({
        margin: 50,
        bufferPages: true
      });
      let buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {});

      // Header
      doc.fontSize(20).text('CONTRAT DE LOCATION SAISONNIÈRE', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12).text(`Généré le: ${new Date().toLocaleDateString('fr-FR')}`, { align: 'right' });
      doc.moveDown(2);

      // Parties
      doc.fontSize(14).text('ENTRE LES SOUSSIGNÉS :', { underline: true });
      doc.moveDown();

      doc.fontSize(12);
      doc.text('LE BAILLEUR :', { underline: true });
      doc.text('Nom: [Nom du propriétaire]');
      doc.text('Adresse: [Adresse du propriétaire]');
      doc.text('Téléphone: [Téléphone du propriétaire]');
      doc.moveDown();

      doc.text('LE LOCATAIRE :', { underline: true });
      doc.text(`Nom: ${data.nom || 'Non renseigné'} ${data.prenom || ''}`);
      doc.text(`Date de naissance: ${data.dateNaissance || 'Non renseignée'}`);
      doc.text(`Pièce d'identité: ${data.typePieceIdentite || 'Non renseignée'} n° ${data.numeroPieceIdentite || 'Non renseigné'}`);
      doc.text(`Domicile: ${data.domicileHabituel || 'Non renseigné'}`);
      doc.text(`Pays: ${data.pays || 'Non renseigné'}`);
      doc.moveDown(2);

    // Objet du contrat
    doc.fontSize(14).text('OBJET DU CONTRAT :', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text('Le présent contrat a pour objet la location saisonnière du logement suivant :');
    doc.text(`Adresse: [Adresse du logement]`);
    doc.text(`Type: [Type de logement]`);
    doc.text(`Superficie: [Superficie]`);
    doc.moveDown();

    // Durée
    doc.fontSize(14).text('DURÉE DE LA LOCATION :', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Date d'arrivée: ${data.dateArrivee || 'Non renseignée'}`);
    doc.text(`Date de départ: ${data.dateDepart || 'Non renseignée'}`);
    doc.text(`Motif du séjour: ${data.motifSejour || 'Non renseigné'}`);
    doc.moveDown(2);

    // Conditions
    doc.fontSize(14).text('CONDITIONS GÉNÉRALES :', { underline: true });
    doc.moveDown();
    doc.fontSize(10);
    
    const conditions = [
      '1. Le locataire s\'engage à occuper personnellement les lieux loués.',
      '2. Le locataire s\'engage à restituer les lieux en bon état.',
      '3. Le locataire s\'engage à respecter le règlement intérieur.',
      '4. Toute sous-location est interdite.',
      '5. Les animaux ne sont pas autorisés sauf accord préalable.',
      '6. Le locataire s\'engage à respecter la tranquillité du voisinage.',
      '7. Le bailleur s\'engage à mettre à disposition un logement conforme.',
      '8. En cas de litige, les tribunaux français seront compétents.'
    ];

    conditions.forEach(condition => {
      doc.text(condition, { indent: 20 });
      doc.moveDown(0.5);
    });

    doc.moveDown(2);

    // Signatures
    doc.fontSize(12);
    doc.text('Fait en double exemplaire', { align: 'center' });
    doc.moveDown();
    doc.text(`Le ${new Date().toLocaleDateString('fr-FR')}`, { align: 'center' });
    doc.moveDown(3);

    // Signature boxes
    const pageWidth = doc.page.width - 100;
    const signatureWidth = pageWidth / 2 - 20;
    
    doc.text('Signature du bailleur', 50, doc.y, { width: signatureWidth, align: 'center' });
    doc.text('Signature du locataire', 50 + signatureWidth + 40, doc.y - 12, { width: signatureWidth, align: 'center' });
    
    doc.moveDown(4);
    
    // Signature lines
    doc.moveTo(50, doc.y).lineTo(50 + signatureWidth, doc.y).stroke();
    doc.moveTo(50 + signatureWidth + 40, doc.y - 12).lineTo(50 + signatureWidth + 40 + signatureWidth, doc.y - 12).stroke();

    // Footer
    doc.fontSize(8);
    doc.text('Document généré automatiquement par le système de télédéclaration', 50, doc.page.height - 50, {
      align: 'center',
      width: doc.page.width - 100
    });

      // Finalize the PDF
      doc.end();

      // Combine the PDF buffers
      pdfBuffer = Buffer.concat(buffers);

    } catch (pdfkitError) {
      console.warn('PDFKit failed, using fallback PDF generation:', pdfkitError);

      // Fallback to simple PDF generation
      const simplePDFContent = generateSimplePDFContent(data, 'rental-agreement');
      pdfBuffer = Buffer.from(simplePDFContent, 'utf-8');
    }

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename=contrat_location.pdf',
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Rental agreement generation error:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
