'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from './DashboardSidebar'
import { PageWrapper } from './PageWrapper'
import { MobileBottomNav } from './MobileBottomNav'
import { SidebarProvider, SidebarTrigger, useSidebar } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import { useForm } from '@/contexts/FormContext'
import {
  Hotel, BarChart2, Building2, FileText, ArrowRight, Menu, Upload, ListFilter,
  MessageSquare, Calendar, Users, TrendingUp, Eye, CheckCircle, Clock,
  MoreHorizontal, Bell, Search, Filter, Download
} from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { MessageManager } from './MessageManager'
import { CalendarManager } from './CalendarManager'
import { Progress } from "@/components/ui/progress"

export function DashboardContent() {
  const isMobile = useIsMobile()
  const { setUserType } = useForm()

  // Set user type to host when dashboard loads
  useEffect(() => {
    setUserType('host')
  }, [setUserType])

  return (
    <PageWrapper
      forceUserType="host"
      className="min-h-screen bg-gray-50 logo-background"
      showMobileActions={true}
    >
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <DashboardMain />
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </PageWrapper>
  )
}

function DashboardMain() {
  const { open, setOpen } = useSidebar()
  const router = useRouter()
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const isMobileDevice = useIsMobile()

  const handleLogout = () => {
    router.push('/')
  }

  const handleNuiteesClick = () => {
    router.push('/renseignement-nuites')
  }

  const handleRechercheNuiteesClick = () => {
    router.push('/recherche-nuites')
  }

  const handleImportNuiteesClick = () => {
    router.push('/import-nuites')
  }

  return (
    <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobileDevice ? 'pb-20' : ''}`}>
      {/* Enhanced Mobile-First Header */}
      <div className="bg-white border-b border-gray-200 mobile-container py-3 sm:py-4">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          <div className="flex items-center gap-3 sm:gap-4">
            {!isMobileDevice && (
              <SidebarTrigger className="lg:hidden">
                <Button variant="ghost" size="icon" className="hover:bg-gray-100 touch-target">
                  <Menu className="h-5 w-5 text-gray-600" />
                </Button>
              </SidebarTrigger>
            )}
            <div className="min-w-0 flex-1">
              <h1 className="mobile-text-xl font-bold text-gray-900 truncate">
                {isMobileDevice ? 'Dashboard' : 'Système de Déclaration'}
              </h1>
              <p className="text-sm text-gray-600 hidden sm:block">
                Gérez vos propriétés et déclarations de locataires
              </p>
            </div>
          </div>

          {!isMobileDevice && (
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="icon" className="hover:bg-gray-100 touch-target">
                <Bell className="h-5 w-5 text-gray-600" />
              </Button>
              <Button variant="ghost" size="icon" className="hover:bg-gray-100 touch-target">
                <Search className="h-5 w-5 text-gray-600" />
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="border-gray-200 text-gray-600 hover:bg-gray-50"
              >
                Déconnexion
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile-First Notification Banner */}
      {!isMobileDevice && (
        <div className="mobile-container mt-4 mb-2">
          <div className="bg-blue-50 border border-blue-200 mobile-card p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Eye className="w-5 h-5 text-blue-600 flex-shrink-0" />
              <span className="text-sm text-blue-800">
                Découvrez le nouveau tableau de bord de déclaration
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" className="text-blue-600 border-blue-300 hover:bg-blue-100">
                Voir
              </Button>
              <Button size="sm" variant="ghost" className="text-blue-600 hover:bg-blue-100">
                ✕
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Mobile-First Main Content */}
      <div className="mobile-container mobile-gap space-y-4 sm:space-y-6">
        {/* Enhanced Vue d'ensemble - Mobile-First Stats Row */}
        <div className="space-y-4 sm:space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="mobile-text-lg font-bold text-gray-900">Vue d'ensemble</h2>
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              Temps réel
            </Badge>
          </div>
          <div className="grid grid-cols-2 lg:grid-cols-4 mobile-gap">
            {/* Enhanced Total des Logements */}
            <Card className="enhanced-card stats-card group">
              <div className="flex items-center justify-between mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <div className="text-xs text-green-600 font-medium flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    +2 ce mois
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-3xl font-bold text-gray-900 group-hover:text-institutional-primary transition-colors">12</p>
                <p className="text-sm text-gray-600 font-medium">Total des logements</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div className="bg-gradient-to-r from-institutional-primary to-green-600 h-1.5 rounded-full w-3/4 transition-all duration-500"></div>
                </div>
              </div>
            </Card>

            {/* Enhanced Déclarations */}
            <Card className="enhanced-card stats-card group">
              <div className="flex items-center justify-between mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <div className="text-xs text-green-600 font-medium flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    +8 cette semaine
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors">45</p>
                <p className="text-sm text-gray-600 font-medium">Déclarations ce mois</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 h-1.5 rounded-full w-4/5 transition-all duration-500"></div>
                </div>
              </div>
            </Card>

            {/* Enhanced Logements Occupés */}
            <Card className="enhanced-card stats-card group">
              <div className="flex items-center justify-between mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Hotel className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <div className="text-xs text-purple-600 font-medium flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    67% taux
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors">8</p>
                <p className="text-sm text-gray-600 font-medium">Logements occupés</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div className="bg-gradient-to-r from-purple-500 to-violet-600 h-1.5 rounded-full w-2/3 transition-all duration-500"></div>
                </div>
              </div>
            </Card>

            {/* Enhanced Revenue */}
            <Card className="enhanced-card stats-card group">
              <div className="flex items-center justify-between mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <div className="text-xs text-green-600 font-medium flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    +12% ce mois
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-3xl font-bold text-gray-900 group-hover:text-amber-600 transition-colors">€2.6K</p>
                <p className="text-sm text-gray-600 font-medium">Revenus déclarés</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div className="bg-gradient-to-r from-amber-500 to-orange-600 h-1.5 rounded-full w-5/6 transition-all duration-500"></div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Enhanced Mobile-First Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 mobile-gap">
          {/* Mobile-Optimized Ownership Distribution Chart */}
          <Card className="lg:col-span-2 bg-white border-0 shadow-sm mobile-card">
            <CardHeader className="pb-3 sm:pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                <div className="min-w-0 flex-1">
                  <CardTitle className="mobile-text-lg font-semibold text-gray-900 truncate">
                    Répartition des Propriétés
                  </CardTitle>
                  <p className="text-sm text-gray-500 mt-1">Distribution par type de logement</p>
                </div>
                {!isMobileDevice && (
                  <Button variant="ghost" size="sm" className="text-gray-500 flex-shrink-0">
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {/* Mobile-Optimized Property Distribution Chart */}
              <div className="relative h-48 sm:h-64 lg:h-80 flex items-center justify-center">
                <div className="relative w-36 h-36 sm:w-48 sm:h-48 lg:w-64 lg:h-64">
                  {/* Background Circle */}
                  <div className="absolute inset-0 rounded-full bg-gray-100"></div>

                  {/* Fully Declared Segment (68.42%) */}
                  <div className="absolute inset-0">
                    <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="35"
                        fill="none"
                        stroke="hsl(var(--institutional-primary))"
                        strokeWidth="12"
                        strokeDasharray="150 220"
                        strokeLinecap="round"
                        className="drop-shadow-sm"
                      />
                    </svg>
                  </div>

                  {/* Undeclared Segment (31.58%) */}
                  <div className="absolute inset-0">
                    <svg className="w-full h-full transform rotate-[155deg]" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="35"
                        fill="none"
                        stroke="hsl(var(--institutional-gold))"
                        strokeWidth="12"
                        strokeDasharray="70 300"
                        strokeLinecap="round"
                        className="drop-shadow-sm"
                      />
                    </svg>
                  </div>

                  {/* Mobile-Optimized Center Content */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center bg-white rounded-full w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 flex items-center justify-center shadow-sm">
                      <div>
                        <Building2 className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-gray-400 mx-auto mb-1" />
                        <p className="text-base sm:text-lg lg:text-xl font-bold text-gray-900">12</p>
                        <p className="text-xs sm:text-sm text-gray-500">Logements</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Top Stakeholders List */}
              <div className="mt-8">
                <h3 className="text-sm font-medium text-gray-900 mb-4">Top Propriétaires</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-semibold text-blue-700">JS</span>
                      </div>
                      <span className="text-sm text-gray-900">Jon Smith</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">68.42%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-semibold text-green-700">JS</span>
                      </div>
                      <span className="text-sm text-gray-900">Jane Smith</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">10.52%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-xs font-semibold text-purple-700">UO</span>
                      </div>
                      <span className="text-sm text-gray-900">Options Non-allouées</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">21.05%</span>
                  </div>
                </div>
              </div>

              {/* Legend */}
              <div className="flex flex-wrap justify-center gap-6 mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-institutional-primary rounded-full shadow-sm"></div>
                  <span className="text-sm text-gray-600">Entièrement déclarés (68.42%)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-institutional-gold rounded-full shadow-sm"></div>
                  <span className="text-sm text-gray-600">Non déclarés (31.58%)</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Mobile-Optimized Activity Feed */}
          <Card className="bg-white border-0 shadow-sm mobile-card">
            <CardHeader className="pb-3 sm:pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-4">
                <CardTitle className="mobile-text-lg font-semibold text-gray-900">
                  Activité
                </CardTitle>
                <p className="text-xs sm:text-sm text-gray-500">30 derniers jours vs Précédent</p>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Activity Summary */}
              <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
                <CheckCircle className="w-4 h-4 text-institutional-primary" />
                <span className="text-sm font-medium text-gray-900">1</span>
                <span className="text-sm text-gray-500">ACTION ITEMS</span>
                <ArrowRight className="w-4 h-4 text-gray-400 ml-auto" />
              </div>

              {/* Activity Items */}
              <div className="space-y-4">
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">1</p>
                      <p className="text-xs text-gray-500">Options vested</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900">-</p>
                    <p className="text-xs text-gray-500">Options granted</p>
                  </div>
                </div>

                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">2</p>
                      <p className="text-xs text-gray-500">Offres créées</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900">-</p>
                    <p className="text-xs text-gray-500">Offres signées</p>
                  </div>
                </div>

                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <MessageSquare className="w-4 h-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">2</p>
                      <p className="text-xs text-gray-500">Annonces envoyées</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Enhanced Mobile-First Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 mobile-gap">
          {/* Mobile-Optimized Management Actions */}
          <Card className="lg:col-span-2 bg-white border-0 shadow-sm mobile-card">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="mobile-text-lg font-semibold text-gray-900">
                Actions de Gestion
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Bootstrapped Section */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-gray-900">Gestion des Nuités</h3>
                    <Button
                      size="sm"
                      className="bg-institutional-primary hover:bg-green-600 text-white"
                      onClick={handleNuiteesClick}
                    >
                      Accéder
                    </Button>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">€0.00 déclaré</span>
                      <span className="text-gray-900 font-medium">€20.00 objectif</span>
                    </div>
                    <Progress value={0} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">€110.00 Objectif total</span>
                      <span className="text-gray-500">23 jours restants</span>
                    </div>
                  </div>
                </div>

                {/* Mobile-Optimized Quick Actions */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mobile-gap">
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={handleRechercheNuiteesClick}
                  >
                    <Search className="touch-icon mr-2" />
                    Rechercher
                  </Button>
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={handleImportNuiteesClick}
                  >
                    <Upload className="touch-icon mr-2" />
                    Importer
                  </Button>
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={() => router.push('/statistiques-generales')}
                  >
                    <BarChart2 className="touch-icon mr-2" />
                    Statistiques
                  </Button>
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={() => router.push('/ajouter-logement')}
                  >
                    <Building2 className="touch-icon mr-2" />
                    Ajouter Logement
                  </Button>
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={() => router.push('/gestion-logements')}
                  >
                    <ListFilter className="touch-icon mr-2" />
                    Gérer Logements
                  </Button>
                  <Button
                    variant="outline"
                    className="mobile-button justify-start touch-target"
                    onClick={() => router.push('/creer-contrat')}
                  >
                    <FileText className="touch-icon mr-2" />
                    Créer Contrat
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Total Options / Property Summary */}
          <Card className="bg-white border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Total Logements
                </CardTitle>
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                  1 pool
                </span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary Stats */}
              <div className="text-center py-4">
                <p className="text-3xl font-bold text-gray-900 mb-1">8</p>
                <p className="text-sm text-gray-500">Total logements</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">0%</span>
                    <span className="text-gray-600">Non alloués</span>
                  </div>
                  <Progress value={0} className="h-2" />
                </div>
              </div>

              {/* Messages and Calendar Section */}
              <div className="pt-4 border-t border-gray-100">
                <h3 className="font-medium text-gray-900 mb-3">Messages & Calendrier</h3>
                <Tabs defaultValue="messages" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 bg-gray-100 rounded-lg p-1 h-8">
                    <TabsTrigger value="messages" className="rounded-md text-xs">Messages</TabsTrigger>
                    <TabsTrigger value="calendar" className="rounded-md text-xs">Calendrier</TabsTrigger>
                  </TabsList>
                  <TabsContent value="messages" className="mt-3">
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-xs font-semibold text-green-700">
                          JD
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-gray-900 truncate">John Doe</p>
                          <p className="text-xs text-gray-500 truncate">Airbnb</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-semibold text-blue-700">
                          JS
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-gray-900 truncate">Jane Smith</p>
                          <p className="text-xs text-gray-500 truncate">Booking</p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="calendar" className="mt-3">
                    <div className="text-center py-4">
                      <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-xs text-gray-500">3 réservations ce mois</p>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

