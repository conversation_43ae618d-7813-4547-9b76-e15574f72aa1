'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  MessageSquare, Send, Phone, Mail, Calendar, Users,
  Plus, Search, Filter, MoreHorizontal, Clock, CheckCircle
} from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock data for messages
const mockMessages = [
  {
    id: 1,
    sender: "<PERSON>",
    subject: "Question sur le check-in",
    message: "Bonjour, j'aimerais savoir à quelle heure je peux faire mon check-in demain ?",
    timestamp: "2024-01-20 14:30",
    status: "unread",
    type: "inquiry"
  },
  {
    id: 2,
    sender: "Maria Garcia",
    subject: "Problème avec les clés",
    message: "Je n'arrive pas à ouvrir la porte avec le code que vous m'avez donné.",
    timestamp: "2024-01-20 12:15",
    status: "replied",
    type: "issue"
  },
  {
    id: 3,
    sender: "John Smith",
    subject: "Merci pour le séjour",
    message: "Merci beaucoup pour l'excellent séjour. Tout était parfait !",
    timestamp: "2024-01-19 18:45",
    status: "read",
    type: "feedback"
  }
]

// Mock data for templates
const messageTemplates = [
  {
    id: 1,
    name: "Instructions Check-in",
    subject: "Instructions pour votre arrivée",
    content: "Bonjour {nom}, voici les instructions pour votre check-in..."
  },
  {
    id: 2,
    name: "Rappel Check-out",
    subject: "Rappel - Check-out demain",
    content: "Bonjour {nom}, nous vous rappelons que votre check-out est prévu demain..."
  },
  {
    id: 3,
    name: "Demande d'avis",
    subject: "Votre avis nous intéresse",
    content: "Bonjour {nom}, nous espérons que vous avez passé un excellent séjour..."
  }
]

export default function CommunicationPage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('messages')
  const [searchTerm, setSearchTerm] = useState('')
  const [newMessage, setNewMessage] = useState({
    recipient: '',
    subject: '',
    message: ''
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'unread':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Non lu</Badge>
      case 'replied':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Répondu</Badge>
      case 'read':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Lu</Badge>
      default:
        return <Badge variant="outline">Inconnu</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'inquiry':
        return <MessageSquare className="w-4 h-4 text-blue-600" />
      case 'issue':
        return <Phone className="w-4 h-4 text-red-600" />
      case 'feedback':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      default:
        return <MessageSquare className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Communication
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos communications avec les locataires
                  </p>
                </div>
                <Button 
                  className="bg-institutional-primary hover:bg-green-600 text-white"
                  onClick={() => setActiveTab('nouveau')}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Nouveau Message
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-4 mobile-gap">
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <MessageSquare className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">24</p>
                        <p className="text-sm text-gray-600">Messages</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <Clock className="w-5 h-5 text-red-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">3</p>
                        <p className="text-sm text-gray-600">Non lus</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">18</p>
                        <p className="text-sm text-gray-600">Répondus</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Mail className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">12</p>
                        <p className="text-sm text-gray-600">Envoyés</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Main Content */}
              <Card className="enhanced-card">
                <CardContent className="p-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <div className="border-b border-gray-200 px-6 pt-6">
                      <TabsList className="grid w-full grid-cols-3 bg-gray-100 rounded-lg p-1">
                        <TabsTrigger value="messages">Messages</TabsTrigger>
                        <TabsTrigger value="templates">Modèles</TabsTrigger>
                        <TabsTrigger value="nouveau">Nouveau</TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="messages" className="p-6 space-y-4">
                      {/* Search */}
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Rechercher dans les messages..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>

                      {/* Messages List */}
                      <div className="space-y-3">
                        {mockMessages.map((message) => (
                          <div key={message.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3 flex-1">
                                {getTypeIcon(message.type)}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium text-gray-900">{message.sender}</p>
                                    {getStatusBadge(message.status)}
                                  </div>
                                  <p className="text-sm font-medium text-gray-700 mb-1">{message.subject}</p>
                                  <p className="text-sm text-gray-600 line-clamp-2">{message.message}</p>
                                  <p className="text-xs text-gray-500 mt-2">{message.timestamp}</p>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="templates" className="p-6 space-y-4">
                      <div className="grid gap-4">
                        {messageTemplates.map((template) => (
                          <div key={template.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-medium text-gray-900">{template.name}</h3>
                              <Button variant="outline" size="sm">
                                Utiliser
                              </Button>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{template.subject}</p>
                            <p className="text-sm text-gray-500 line-clamp-2">{template.content}</p>
                          </div>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="nouveau" className="p-6 space-y-4">
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Destinataire
                          </label>
                          <Input
                            placeholder="Sélectionner un locataire..."
                            value={newMessage.recipient}
                            onChange={(e) => setNewMessage({...newMessage, recipient: e.target.value})}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Sujet
                          </label>
                          <Input
                            placeholder="Sujet du message..."
                            value={newMessage.subject}
                            onChange={(e) => setNewMessage({...newMessage, subject: e.target.value})}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Message
                          </label>
                          <Textarea
                            placeholder="Votre message..."
                            rows={6}
                            value={newMessage.message}
                            onChange={(e) => setNewMessage({...newMessage, message: e.target.value})}
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                            <Send className="w-4 h-4 mr-2" />
                            Envoyer
                          </Button>
                          <Button variant="outline">
                            Brouillon
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </div>
  )
}
