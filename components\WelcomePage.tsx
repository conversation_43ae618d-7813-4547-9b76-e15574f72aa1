'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { LanguageSelector } from './LanguageSelector'
import { PageWrapper } from './PageWrapper'
import Link from 'next/link'
import { Label } from '@/components/ui/label'
import { QRCodeScanner } from './QRCodeScanner'
import { AccommodationCodeInput } from './AccommodationCodeInput'
import { useRouter } from 'next/navigation'
import { useForm } from '@/contexts/FormContext'
import { QrCode, UserPlus, LogIn, Building2, Shield, Sparkles, Home } from 'lucide-react'

export function WelcomePage() {
  const [showScanner, setShowScanner] = useState(false)
  const [showCodeInput, setShowCodeInput] = useState(false)
  const [showSkipButton, setShowSkipButton] = useState(false)
  const router = useRouter()
  const { setUserType } = useForm()

  const handleGuestAction = () => {
    // Set user type to guest when they access QR scanner or accommodation code
    setUserType('guest')
  }



  return (
    <PageWrapper
      forceUserType="guest"
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 relative overflow-hidden logo-background-large"
    >

      {/* Enhanced Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated floating elements */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-gradient-to-br from-institutional-primary/30 to-institutional-accent/20 rounded-full blur-sm animate-pulse-slow"></div>
        <div className="absolute top-32 right-16 w-12 h-12 bg-gradient-to-br from-purple-400/30 to-pink-400/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-48 left-1/4 w-20 h-20 bg-gradient-to-br from-blue-400/25 to-institutional-primary/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-32 right-10 w-14 h-14 bg-gradient-to-br from-institutional-gold/30 to-yellow-400/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-48 left-12 w-18 h-18 bg-gradient-to-br from-institutional-accent/25 to-red-400/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '1.5s' }}></div>

        {/* Additional floating particles */}
        <div className="absolute top-1/3 right-1/4 w-8 h-8 bg-gradient-to-br from-emerald-400/20 to-teal-400/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '3s' }}></div>
        <div className="absolute bottom-1/3 left-1/3 w-10 h-10 bg-gradient-to-br from-violet-400/20 to-purple-400/20 rounded-full blur-sm animate-pulse-slow" style={{ animationDelay: '2.5s' }}></div>

        {/* Central decorative element with enhanced styling */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-br from-institutional-primary/15 to-institutional-accent/10 rounded-full blur-3xl animate-pulse-slow"></div>

        {/* Subtle grid pattern overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(0,0,0,0.05)_1px,transparent_0)] bg-[length:20px_20px] opacity-30"></div>
      </div>

      {/* Enhanced Mobile-First Main Content */}
      <div className="flex items-end justify-center min-h-[calc(100vh-80px)] mobile-container relative z-10 pb-8 sm:pb-12 logo-background-content">
        <div className="w-full max-w-sm sm:max-w-md">
          {/* Enhanced Mobile-First Card Design with Bottom-Up Animation */}
          <Card className="bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card overflow-hidden animate-slide-up-from-bottom animate-float relative">
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-institutional-primary/5 pointer-events-none"></div>

            <CardContent className="p-0 relative">
              {/* Mobile-Optimized Header Section */}
              <div className="relative mobile-padding pb-4 sm:pb-6">
                <button className="absolute top-4 sm:top-6 right-4 sm:right-6 touch-target flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200 animate-float-up-delay-3">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                {/* Mobile-Optimized Decorative Icon */}
                <div className="flex justify-start mb-4 sm:mb-6 animate-float-up">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-institutional-primary via-institutional-primary to-institutional-accent rounded-2xl flex items-center justify-center shadow-lg shadow-institutional-primary/25">
                    <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                </div>

                <h1 className="mobile-text-xl font-bold text-gray-900 mb-3 sm:mb-4 leading-tight animate-float-up-delay-1">
                  Déclaration de Séjour
                </h1>
                <p className="text-gray-600 text-sm sm:text-base leading-relaxed animate-float-up-delay-2 mb-4">
                  Utilisez le code d'hébergement fourni par votre hôte pour déclarer votre séjour.
                </p>
                <div className="bg-amber-50 border-2 border-amber-300 rounded-xl p-4 mb-4 animate-float-up-delay-2">
                  <div className="flex items-start gap-2">
                    <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-white text-xs font-bold">!</span>
                    </div>
                    <div>
                      <p className="text-amber-900 text-sm font-bold mb-1">
                        🏠 Code d'hébergement OBLIGATOIRE
                      </p>
                      <p className="text-amber-800 text-xs leading-relaxed">
                        Votre hôte vous a fourni un <strong>QR code</strong> ou un <strong>code unique au format T-XXXXXX</strong>.
                        Ce code est nécessaire pour accéder à la télédéclaration.
                      </p>
                      <p className="text-amber-700 text-xs mt-2 font-medium">
                        ⚠️ Sans ce code, vous ne pourrez pas procéder à la déclaration
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile-Optimized Action Buttons */}
              <div className="mobile-padding pb-6 sm:pb-8 space-y-3 sm:space-y-4">
                {/* Primary Action - QR Scanner */}
                <Button
                  variant="default"
                  size="lg"
                  className="w-full mobile-button text-base font-semibold bg-gradient-to-r from-gray-900 to-gray-800 hover:from-gray-800 hover:to-gray-700 text-white mobile-card shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 animate-float-up-delay-2 group relative overflow-hidden touch-target"
                  onClick={() => {
                    handleGuestAction()
                    setShowScanner(true)
                    setShowSkipButton(true)
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                  <QrCode className="touch-icon mr-3 group-hover:scale-110 transition-transform duration-200 relative z-10" />
                  <span className="relative z-10">Scanner avec QR Code</span>
                </Button>

                {/* Secondary Action - Accommodation Code */}
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full mobile-button text-base font-semibold border-2 border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white mobile-card shadow-md hover:shadow-lg hover:scale-[1.02] transition-all duration-300 animate-float-up-delay-3 group touch-target"
                  onClick={() => {
                    handleGuestAction()
                    setShowCodeInput(true)
                    setShowSkipButton(true)
                  }}
                >
                  <Home className="touch-icon mr-3 group-hover:scale-110 transition-transform duration-200" />
                  Code d'hébergement
                </Button>



                {/* Mobile-Optimized Login Options */}
                <div className="grid grid-cols-2 mobile-gap mt-6 sm:mt-8 animate-float-up-delay-3">
                  <Button
                    variant="outline"
                    size="lg"
                    className="mobile-button bg-gradient-to-br from-institutional-primary/10 to-institutional-primary/5 hover:from-institutional-primary/20 hover:to-institutional-primary/10 text-institutional-primary border border-institutional-primary/20 hover:border-institutional-primary/30 mobile-card hover:scale-[1.05] transition-all duration-300 flex items-center justify-center group touch-target"
                    onClick={() => router.push('/login')}
                  >
                    <Building2 className="touch-icon group-hover:scale-110 transition-transform duration-200" />
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="mobile-button bg-gradient-to-br from-institutional-accent/10 to-institutional-accent/5 hover:from-institutional-accent/20 hover:to-institutional-accent/10 text-institutional-accent border border-institutional-accent/20 hover:border-institutional-accent/30 mobile-card hover:scale-[1.05] transition-all duration-300 flex items-center justify-center group touch-target"
                    onClick={() => router.push('/login')}
                  >
                    <LogIn className="touch-icon group-hover:scale-110 transition-transform duration-200" />
                  </Button>
                </div>

                {/* Subtle divider */}
                <div className="flex items-center justify-center pt-3 sm:pt-4 animate-float-up-delay-3">
                  <div className="w-8 sm:w-12 h-1 bg-gradient-to-r from-transparent via-gray-300 to-transparent rounded-full"></div>
                </div>
              </div>


            </CardContent>
          </Card>

        </div>

        {showScanner && (
          <QRCodeScanner
            onClose={() => {
              setShowScanner(false)
              setShowSkipButton(false)
            }}
          />
        )}

        {showCodeInput && (
          <AccommodationCodeInput
            onClose={() => {
              setShowCodeInput(false)
              setShowSkipButton(false)
            }}
          />
        )}
      </div>
    </PageWrapper>
  )
}

