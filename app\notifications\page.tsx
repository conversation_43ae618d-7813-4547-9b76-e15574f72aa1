import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'

import { Bell, Check, X, Settings, AlertCircle, Info, CheckCircle } from 'lucide-react'

export default function NotificationsPage() {
  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          <DashboardSidebar />
          <div className="flex-1 bg-gray-50 min-h-full logo-background-content">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-6">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Notifications
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos notifications et alertes
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline">
                    <Check className="w-4 h-4 mr-2" />
                    Tout marquer lu
                  </Button>
                  <Button variant="outline">
                    <Settings className="w-4 h-4 mr-2" />
                    Paramètres
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container py-6 space-y-6">
              {/* Notification Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Non lues</CardTitle>
                    <Bell className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2</div>
                    <p className="text-xs text-muted-foreground">Notifications en attente</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Aujourd'hui</CardTitle>
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">5</div>
                    <p className="text-xs text-muted-foreground">Nouvelles notifications</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Alertes</CardTitle>
                    <AlertCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">1</div>
                    <p className="text-xs text-muted-foreground">Nécessite une action</p>
                  </CardContent>
                </Card>
              </div>

              {/* Notifications List */}
              <Card>
                <CardHeader>
                  <CardTitle>Notifications Récentes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Unread notification */}
                    <div className="flex items-start gap-4 p-4 border rounded-lg bg-blue-50 border-blue-200">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-blue-900">Nouvelle déclaration reçue</h3>
                            <p className="text-sm text-blue-700 mt-1">
                              Une nouvelle déclaration a été soumise pour l'Appartement A1
                            </p>
                            <p className="text-xs text-blue-600 mt-2">Il y a 2 heures</p>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Check className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Alert notification */}
                    <div className="flex items-start gap-4 p-4 border rounded-lg bg-red-50 border-red-200">
                      <AlertCircle className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-red-900">Paiement en retard</h3>
                            <p className="text-sm text-red-700 mt-1">
                              La facture #2024-003 est en retard de 5 jours
                            </p>
                            <p className="text-xs text-red-600 mt-2">Il y a 1 jour</p>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              Voir
                            </Button>
                            <Button variant="outline" size="sm">
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Read notification */}
                    <div className="flex items-start gap-4 p-4 border rounded-lg">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900">Contrat signé</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              Le contrat pour le Studio B2 a été signé par Marie Martin
                            </p>
                            <p className="text-xs text-gray-500 mt-2">Il y a 3 heures</p>
                          </div>
                          <Button variant="outline" size="sm">
                            Voir
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Info notification */}
                    <div className="flex items-start gap-4 p-4 border rounded-lg">
                      <Info className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900">Rapport mensuel disponible</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              Votre rapport mensuel de juin est maintenant disponible
                            </p>
                            <p className="text-xs text-gray-500 mt-2">Il y a 1 jour</p>
                          </div>
                          <Button variant="outline" size="sm">
                            Télécharger
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* System notification */}
                    <div className="flex items-start gap-4 p-4 border rounded-lg">
                      <Settings className="w-5 h-5 text-gray-600 mt-1 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900">Mise à jour système</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              Le système sera mis à jour ce soir entre 2h et 4h du matin
                            </p>
                            <p className="text-xs text-gray-500 mt-2">Il y a 2 jours</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      <MobileBottomNav />
    </div>
  )
}
