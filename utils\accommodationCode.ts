/**
 * Utility functions for generating and managing accommodation codes
 * Format: T-XXXXXX where X are numbers (e.g., T-123456)
 */

/**
 * Generates a unique accommodation code in the format T-XXXXXX
 * @returns {string} A unique accommodation code
 */
export function generateAccommodationCode(): string {
  // Generate 6 random digits
  const randomNumber = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  return `T-${randomNumber}`
}

/**
 * Validates if a string is a valid accommodation code format
 * @param {string} code - The code to validate
 * @returns {boolean} True if valid format, false otherwise
 */
export function validateAccommodationCode(code: string): boolean {
  const codeRegex = /^T-\d{6}$/
  return codeRegex.test(code)
}

/**
 * Formats a partial accommodation code input
 * @param {string} input - The user input
 * @returns {string} Formatted code
 */
export function formatAccommodationCodeInput(input: string): string {
  // Remove any non-alphanumeric characters except hyphens
  let formatted = input.toUpperCase().replace(/[^T0-9-]/g, '')
  
  // Add T- prefix if not present
  if (formatted && !formatted.startsWith('T-')) {
    if (formatted.startsWith('T')) {
      formatted = 'T-' + formatted.slice(1)
    } else {
      formatted = 'T-' + formatted
    }
  }
  
  // Limit to T-XXXXXX format (8 characters total)
  if (formatted.length > 8) {
    formatted = formatted.slice(0, 8)
  }
  
  return formatted
}

/**
 * Generates a QR code data string for an accommodation
 * @param {string} accommodationCode - The accommodation code
 * @param {object} accommodationInfo - Additional accommodation information
 * @returns {string} QR code data string
 */
export function generateQRCodeData(
  accommodationCode: string, 
  accommodationInfo?: {
    name?: string
    address?: string
    checkInUrl?: string
  }
): string {
  // For simple implementation, just return the accommodation code
  // In a real application, you might include additional data or a URL
  if (accommodationInfo?.checkInUrl) {
    return `${accommodationInfo.checkInUrl}?code=${accommodationCode}`
  }
  
  return accommodationCode
}

/**
 * Checks if an accommodation code is already in use
 * @param {string} code - The code to check
 * @returns {Promise<boolean>} True if code is available, false if taken
 */
export async function isAccommodationCodeAvailable(code: string): Promise<boolean> {
  // Mock implementation - in real app, check against database
  const mockUsedCodes = ['T-123456', 'T-789012', 'T-345678']
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  return !mockUsedCodes.includes(code)
}

/**
 * Generates a unique accommodation code that's not already in use
 * @param {number} maxAttempts - Maximum number of generation attempts
 * @returns {Promise<string>} A unique accommodation code
 */
export async function generateUniqueAccommodationCode(maxAttempts: number = 10): Promise<string> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const code = generateAccommodationCode()
    const isAvailable = await isAccommodationCodeAvailable(code)
    
    if (isAvailable) {
      return code
    }
  }
  
  throw new Error('Unable to generate unique accommodation code after maximum attempts')
}

/**
 * Mock accommodation database for demo purposes
 */
export const mockAccommodations = [
  {
    id: '1',
    code: 'T-123456',
    name: 'Villa Sunset',
    address: '123 Beach Road, Nice',
    type: 'Villa'
  },
  {
    id: '2',
    code: 'T-789012',
    name: 'Mountain Chalet',
    address: '45 Alpine Way, Chamonix',
    type: 'Chalet'
  },
  {
    id: '3',
    code: 'T-345678',
    name: 'City Apartment',
    address: '78 Urban Street, Paris',
    type: 'Appartement'
  }
]

/**
 * Finds accommodation by code
 * @param {string} code - The accommodation code to search for
 * @returns {object|null} Accommodation object or null if not found
 */
export function findAccommodationByCode(code: string) {
  return mockAccommodations.find(acc => acc.code === code) || null
}
