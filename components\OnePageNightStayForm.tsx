'use client'

import { useState, useRef } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { Camera } from 'lucide-react'
import { Progress } from "@/components/ui/progress"
import Image from 'next/image'

const formSchema = z.object({
  dateArrivee: z.string().min(1, "La date d'arrivée est requise"),
  dateDepart: z.string().min(1, "La date de départ est requise"),
  numeroPieceIdentite: z.string().min(1, "Le numéro de pièce d'identité est requis"),
  typePieceIdentite: z.string().min(1, "Le type de pièce d'identité est requis"),
  nom: z.string().min(1, "Le nom est requis"),
  prenom: z.string().min(1, "Le prénom est requis"),
  sexe: z.string().min(1, "Le sexe est requis"),
  dateNaissance: z.string().min(1, "La date de naissance est requise"),
  lieu: z.string().min(1, "Le lieu est requis"),
  categorieSocioPro: z.string().min(1, "La catégorie socio-professionnelle est requise"),
  domicileHabituel: z.string().min(1, "Le domicile habituel est requis"),
  pays: z.string().min(1, "Le pays est requis"),
  villeResidence: z.string().min(1, "La ville de résidence est requise"),
  motifSejour: z.string().min(1, "Le motif du séjour est requis"),
})

interface OnePageNightStayFormProps {
  onClose: () => void
}

export function OnePageNightStayForm({ onClose }: OnePageNightStayFormProps) {
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [selfie, setSelfie] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      logement: "",
      ville: "",
      dateArrivee: "",
      dateDepart: "",
      numeroPieceIdentite: "",
      typePieceIdentite: "",
      nom: "",
      prenom: "",
      sexe: "",
      dateNaissance: "",
      lieu: "",
      categorieSocioPro: "",
      domicileHabituel: "",
      pays: "",
      villeResidence: "",
      motifSejour: "",
    },
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (step < 4) {
      setStep(step + 1)
    } else {
      console.log(values)
      router.push('/summary')
    }
  }

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (err) {
      console.error("Error accessing camera:", err)
    }
  }

  const takeSelfie = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext('2d')
      if (context) {
        context.drawImage(videoRef.current, 0, 0, canvasRef.current.width, canvasRef.current.height)
        const dataUrl = canvasRef.current.toDataURL('image/jpeg')
        setSelfie(dataUrl)
      }
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Renseignement des Nuites</CardTitle>
      </CardHeader>
      <CardContent>
        <Progress value={(step / 4) * 100} className="mb-4" />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {step === 1 && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="logement"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logement</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un logement" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="logement1">Logement 1</SelectItem>
                          <SelectItem value="logement2">Logement 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="ville"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ville</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une ville" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ville1">Ville 1</SelectItem>
                          <SelectItem value="ville2">Ville 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dateArrivee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date d'arrivée</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dateDepart"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date de départ</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="numeroPieceIdentite"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Numéro de pièce d'identité</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="typePieceIdentite"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type de pièce d'identité</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="passport">Passeport</SelectItem>
                          <SelectItem value="cni">Carte Nationale d'Identité</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="nom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="prenom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prénom</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="sexe"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sexe</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="M">Masculin</SelectItem>
                          <SelectItem value="F">Féminin</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dateNaissance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date de Naissance</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lieu"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lieu</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="categorieSocioPro"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Catégorie SocioPro</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une catégorie" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cat1">Catégorie 1</SelectItem>
                          <SelectItem value="cat2">Catégorie 2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {step === 4 && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="domicileHabituel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Domicile Habituel</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="pays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pays</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un pays" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="maroc">Maroc</SelectItem>
                          <SelectItem value="france">France</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="villeResidence"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ville</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="motifSejour"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Motif du Séjour</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Prendre un Selfie</h3>
                  {!selfie ? (
                    <>
                      <video ref={videoRef} className="w-full h-64 bg-gray-200 rounded-lg" autoPlay playsInline />
                      <Button onClick={startCamera} className="mr-2">
                        <Camera className="mr-2 h-4 w-4" />
                        Démarrer la Caméra
                      </Button>
                      <Button onClick={takeSelfie}>
                        Prendre le Selfie
                      </Button>
                    </>
                  ) : (
                    <div className="relative w-64 h-64">
                      <Image src={selfie} alt="Selfie" layout="fill" objectFit="cover" className="rounded-lg" />
                    </div>
                  )}
                  <canvas ref={canvasRef} style={{ display: 'none' }} width={640} height={480} />
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => step > 1 ? setStep(step - 1) : onClose()}
              >
                {step > 1 ? 'Précédent' : 'Annuler'}
              </Button>
              <Button type="submit">
                {step < 4 ? 'Suivant' : 'Soumettre'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

