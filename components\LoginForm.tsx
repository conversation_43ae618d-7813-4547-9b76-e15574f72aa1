'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { PageWrapper } from './PageWrapper'
import { useIsMobile } from '@/hooks/use-mobile'
import { useForm } from '@/contexts/FormContext'
import Link from 'next/link'
import { LogIn, ArrowLeft, Building2, Mail, Lock, Eye, EyeOff } from 'lucide-react'

export function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const isMobile = useIsMobile()
  const { setUserType } = useForm()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!email || !password) {
      alert('Veuillez remplir tous les champs requis.')
      return
    }

    if (!email.includes('@')) {
      alert('Veuillez entrer une adresse email valide.')
      return
    }

    setIsLoading(true)

    try {
      // Here you would typically handle the login logic
      console.log('Login attempted with:', email, password)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Set user type to host for logged-in users
      setUserType('host')

      // Redirect to dashboard after successful login
      router.push('/dashboard')
    } catch (error) {
      console.error('Login error:', error)
      alert('Erreur de connexion. Veuillez vérifier vos identifiants et réessayer.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <PageWrapper
      forceUserType="guest"
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background-large"
    >
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] mobile-container logo-background-content">
        <div className="w-full max-w-sm sm:max-w-md space-y-4 sm:space-y-6">
          {/* Enhanced Mobile-First Back Button */}
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-institutional-primary transition-colors touch-target animate-float-up"
          >
            <ArrowLeft className="w-4 h-4" />
            {isMobile ? 'Retour' : 'Retour à l\'accueil'}
          </Link>

          {/* Enhanced Mobile-First Login Card */}
          <Card className="bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card animate-slide-up-from-bottom relative overflow-hidden">
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-institutional-primary/5 pointer-events-none"></div>

            <CardHeader className="text-center mobile-padding pb-4 sm:pb-6 relative">
              <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-institutional-primary to-institutional-accent mobile-card mb-4 sm:mb-6 shadow-lg shadow-institutional-primary/25 animate-float-up">
                <Building2 className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
              <CardTitle className="mobile-text-xl font-bold text-gray-800 animate-float-up-delay-1">
                {isMobile ? 'Connexion' : 'Connexion Propriétaires'}
              </CardTitle>
              <p className="text-gray-600 mt-2 text-sm sm:text-base animate-float-up-delay-2">
                Accédez à votre espace de gestion
              </p>
            </CardHeader>

            <CardContent className="mobile-padding pt-0 space-y-4 sm:space-y-6 relative">
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 animate-float-up-delay-2">
                {/* Enhanced Email Field */}
                <div className="space-y-2 animate-float-up-delay-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Mail className="w-4 h-4 text-institutional-primary" />
                    Adresse email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={isLoading}
                    className="mobile-input border border-gray-300 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20 mobile-card transition-all duration-200 disabled:opacity-50"
                  />
                </div>

                {/* Enhanced Password Field */}
                <div className="space-y-2 animate-float-up-delay-3">
                  <Label htmlFor="password" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-institutional-primary" />
                    Mot de passe
                  </Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      disabled={isLoading}
                      className="mobile-input border border-gray-300 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20 mobile-card transition-all duration-200 disabled:opacity-50 pr-12"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 touch-target"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4 text-gray-500" />
                      ) : (
                        <Eye className="w-4 h-4 text-gray-500" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Enhanced Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white mobile-card shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 text-base font-semibold touch-target disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 animate-float-up-delay-3 relative overflow-hidden group"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Connexion...
                    </>
                  ) : (
                    <>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                      <LogIn className="w-5 h-5 mr-2 relative z-10" />
                      <span className="relative z-10">Se connecter</span>
                    </>
                  )}
                </Button>
              </form>

              {/* Enhanced Forgot Password Link */}
              <div className="text-center animate-float-up-delay-3">
                <Link
                  href="/forgot-password"
                  className="text-sm text-institutional-primary hover:text-green-700 hover:underline transition-colors touch-target inline-block py-2"
                >
                  Mot de passe oublié ?
                </Link>
              </div>
            </CardContent>

            {/* Enhanced Footer */}
            <CardFooter className="flex justify-center mobile-padding pt-4 sm:pt-6 border-t border-gray-100 relative">
              <p className="text-sm text-gray-600 text-center animate-float-up-delay-3">
                Pas encore de compte ?{' '}
                <Link
                  href="/signup"
                  className="text-institutional-primary hover:text-green-700 font-semibold hover:underline transition-colors touch-target inline-block py-1"
                >
                  Créer un compte
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </PageWrapper>
  )
}

