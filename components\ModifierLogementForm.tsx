'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useRouter } from 'next/navigation'
import { QrCode } from 'lucide-react'
import Image from 'next/image'

const steps = [
  { id: 1, title: "Informations de base" },
  { id: 2, title: "Équipements et Photos" },
  { id: 3, title: "Le Propriétaire" },
  { id: 4, title: "Le Gestionnaire" },
  { id: 5, title: "Le Directeur" },
  { id: 6, title: "Récapitulatif" }
]

const equipements = [
  "Wi-Fi", "Climatisation", "Chauffage", "Cuisine équipée", "Lave-linge",
  "Sèche-linge", "Télévision", "Parking", "Piscine", "Jacuzzi",
  "Barbecue", "Terrasse", "Balcon", "Vue sur mer", "Accès handicapé",
]

const documentTypes = [
  "Carte Nationale d'Identité", "Passeport", "Carte de Séjour", "Autre"
]

const educationLevels = [
  "Baccalauréat", "Licence", "Master", "Doctorat", "Autre"
]

// Mock data for an existing lodging
const mockLodging = {
  id: '1',
  nomLogement: "Villa Sunset",
  type: "villa",
  adresse: "123 Beach Road",
  region: "Provence-Alpes-Côte d'Azur",
  codePostal: "06000",
  description: "A beautiful villa overlooking the Mediterranean Sea.",
  chambres: 4,
  lits: 6,
  equipements: ["Wi-Fi", "Climatisation", "Piscine"],
  photos: ["/placeholder.svg?height=100&width=100"],
  proprietaire: {
    profil: "profile1",
    nationalite: "Française",
    natureDocument: "Carte Nationale d'Identité",
    numeroDocument: "123456789",
    nom: "Dupont",
    prenom: "Jean",
    sexe: "M",
    telephone: "0123456789",
    email: "<EMAIL>"
  },
  gestionnaire: {
    estProprietaire: true,
    dateContrat: "2023-01-01"
  },
  directeur: {
    estProprietaire: false,
    profil: "profile2",
    nationalite: "Française",
    natureDocument: "Passeport",
    numeroDocument: "987654321",
    nom: "Martin",
    prenom: "Sophie",
    sexe: "F",
    telephone: "9876543210",
    email: "<EMAIL>",
    niveauScolaire: "Master",
    dateRecrutement: "2023-02-01"
  }
}

interface ModifierLogementFormProps {
  id: string
}

export function ModifierLogementForm({ id }: ModifierLogementFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isOwnerManager, setIsOwnerManager] = useState(false)
  const [isOwnerDirector, setIsOwnerDirector] = useState(false)
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [formData, setFormData] = useState(mockLodging)
  const router = useRouter()

  useEffect(() => {
    // In a real application, you would fetch the lodging data here
    // For now, we're using mock data
    setFormData({ ...mockLodging, id })
    setIsOwnerManager(mockLodging.gestionnaire.estProprietaire)
    setIsOwnerDirector(mockLodging.directeur.estProprietaire)
  }, [id])

  const generateQRCode = async () => {
    // In a real application, this would be an API call to generate a QR code
    // For this example, we'll use a placeholder QR code
    const dummyQrCodeUrl = `/placeholder.svg?height=200&width=200&text=${encodeURIComponent(formData.nomLogement)}`
    setQrCodeUrl(dummyQrCodeUrl)
  }

  const handleSave = async () => {
    // Here you would typically:
    // 1. Save the form data to your backend
    // 2. Generate and save the QR code
    // 3. Redirect to the dashboard
    await generateQRCode()
    // Wait for a moment to simulate saving
    setTimeout(() => {
      router.push('/dashboard')
    }, 2000)
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission logic here
    console.log('Form submitted')
    router.push('/dashboard')
  }

  const renderOperatorFields = (title: string, showAdditionalFields = false, educationLevel = false) => (
    <div className="space-y-4">
      <div className="text-lg font-semibold mb-4">{title}</div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="profil">Profil</Label>
          <Select>
            <SelectTrigger id="profil">
              <SelectValue placeholder="Sélectionner le profil" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="profile1">Profile 1</SelectItem>
              <SelectItem value="profile2">Profile 2</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="nationalite">Nationalité</Label>
          <Input id="nationalite" placeholder="Entrez la nationalité" />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="natureDocument">Nature document</Label>
          <Select>
            <SelectTrigger id="natureDocument">
              <SelectValue placeholder="Sélectionner le type" />
            </SelectTrigger>
            <SelectContent>
              {documentTypes.map((type) => (
                <SelectItem key={type} value={type.toLowerCase()}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="numeroDocument">N° document</Label>
          <Input id="numeroDocument" placeholder="Entrez le numéro" />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="nom">Nom</Label>
          <Input id="nom" placeholder="Entrez le nom" />
        </div>
        <div>
          <Label htmlFor="prenom">Prénom</Label>
          <Input id="prenom" placeholder="Entrez le prénom" />
        </div>
        <div>
          <Label htmlFor="sexe">Sexe</Label>
          <Select>
            <SelectTrigger id="sexe">
              <SelectValue placeholder="Sélectionner" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="M">Masculin</SelectItem>
              <SelectItem value="F">Féminin</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="telephone">N° Tél</Label>
          <Input id="telephone" type="tel" placeholder="Entrez le numéro" />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="Entrez l'email" />
        </div>
      </div>
      {showAdditionalFields && (
        <div>
          <Label htmlFor="dateContrat">Date de Contrat de Gestion</Label>
          <Input id="dateContrat" type="date" />
        </div>
      )}
      {educationLevel && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="niveauScolaire">Niveau Scolaire</Label>
            <Select>
              <SelectTrigger id="niveauScolaire">
                <SelectValue placeholder="Sélectionner le niveau" />
              </SelectTrigger>
              <SelectContent>
                {educationLevels.map((level) => (
                  <SelectItem key={level} value={level.toLowerCase()}>
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="dateRecrutement">Date du Recrutement</Label>
            <Input id="dateRecrutement" type="date" />
          </div>
        </div>
      )}
    </div>
  )

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Modifier un Logement - Étape {currentStep}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  step.id === currentStep
                    ? 'bg-primary text-primary-foreground'
                    : step.id < currentStep
                    ? 'bg-primary/50 text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground'
                }`}
              >
                {step.id}
              </div>
            ))}
          </div>
          <div className="h-2 bg-secondary rounded-full">
            <div
              className="h-full bg-primary rounded-full transition-all duration-300 ease-in-out"
              style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
            ></div>
          </div>
        </div>

        <form onSubmit={(e) => e.preventDefault()}>
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="nom">Nom du logement</Label>
                <Input 
                  id="nom" 
                  placeholder="Entrez le nom du logement" 
                  value={formData.nomLogement}
                  onChange={(e) => setFormData({...formData, nomLogement: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="type">Type de logement</Label>
                <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Sélectionnez le type de logement" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="appartement">Appartement</SelectItem>
                    <SelectItem value="maison">Maison</SelectItem>
                    <SelectItem value="villa">Villa</SelectItem>
                    <SelectItem value="riad">Riad</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="adresse">Adresse du logement</Label>
                <Textarea 
                  id="adresse" 
                  placeholder="Entrez l'adresse complète" 
                  value={formData.adresse}
                  onChange={(e) => setFormData({...formData, adresse: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="region">Région</Label>
                <Input 
                  id="region" 
                  placeholder="Entrez la région" 
                  value={formData.region}
                  onChange={(e) => setFormData({...formData, region: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="code-postal">Code postal</Label>
                <Input 
                  id="code-postal" 
                  placeholder="Entrez le code postal" 
                  value={formData.codePostal}
                  onChange={(e) => setFormData({...formData, codePostal: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  placeholder="Décrivez votre logement" 
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="chambres">Nombre de chambres</Label>
                <Input 
                  id="chambres" 
                  type="number" 
                  min="1" 
                  value={formData.chambres}
                  onChange={(e) => setFormData({...formData, chambres: parseInt(e.target.value)})}
                />
              </div>
              <div>
                <Label htmlFor="lits">Nombre de lits</Label>
                <Input 
                  id="lits" 
                  type="number" 
                  min="1" 
                  value={formData.lits}
                  onChange={(e) => setFormData({...formData, lits: parseInt(e.target.value)})}
                />
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <Label>Équipements disponibles</Label>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  {equipements.map((item) => (
                    <div key={item} className="flex items-center space-x-2">
                      <Checkbox 
                        id={item} 
                        checked={formData.equipements.includes(item)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData({...formData, equipements: [...formData.equipements, item]})
                          } else {
                            setFormData({...formData, equipements: formData.equipements.filter(e => e !== item)})
                          }
                        }}
                      />
                      <Label htmlFor={item}>{item}</Label>
                    </div>
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="photos">Ajouter des photos</Label>
                  <Input id="photos" type="file" multiple accept="image/*" />
                </div>
                <div>
                  <Label>Photos téléchargées</Label>
                  <div className="grid grid-cols-3 gap-4 mt-2">
                    {formData.photos.map((photo, index) => (
                      <div key={index} className="w-full h-32 bg-secondary rounded-md flex items-center justify-center">
                        <Image src={photo} alt={`Image ${index + 1}`} width={100} height={100} />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && renderOperatorFields("Le Propriétaire")}

          {currentStep === 4 && (
            <>
              <div className="mb-4 flex items-center space-x-2">
                <Checkbox
                  id="ownerIsManager"
                  checked={isOwnerManager}
                  onCheckedChange={(checked) => setIsOwnerManager(checked as boolean)}
                />
                <Label htmlFor="ownerIsManager">Le Propriétaire est Gestionnaire</Label>
              </div>
              {!isOwnerManager && renderOperatorFields("Le Gestionnaire", true)}
            </>
          )}

          {currentStep === 5 && (
            <>
              <div className="mb-4 flex items-center space-x-2">
                <Checkbox
                  id="ownerIsDirector"
                  checked={isOwnerDirector}
                  onCheckedChange={(checked) => setIsOwnerDirector(checked as boolean)}
                />
                <Label htmlFor="ownerIsDirector">Le Propriétaire est Directeur</Label>
              </div>
              {!isOwnerDirector && renderOperatorFields("Le Directeur", false, true)}
            </>
          )}

          {currentStep === 6 && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations du Logement</h3>
                  <div className="grid gap-2">
                    <div>
                      <span className="font-medium">Nom:</span> {formData.nomLogement}
                    </div>
                    <div>
                      <span className="font-medium">Type:</span> {formData.type}
                    </div>
                    <div>
                      <span className="font-medium">Adresse:</span> {formData.adresse}
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">QR Code</h3>
                  {qrCodeUrl ? (
                    <div className="flex flex-col items-center gap-4">
                      <Image
                        src={qrCodeUrl}
                        alt="QR Code du logement"
                        width={200}
                        height={200}
                        className="border rounded-lg p-2"
                      />
                      <Button variant="outline" onClick={() => window.open(qrCodeUrl, '_blank')}>
                        Télécharger le QR Code
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-[200px] h-[200px] border rounded-lg flex items-center justify-center">
                        <QrCode className="w-16 h-16 text-muted-foreground" />
                      </div>
                      <Button variant="outline" onClick={generateQRCode}>
                        Générer le QR Code
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              <div className="pt-6 border-t">
                <h3 className="text-lg font-semibold mb-4">Confirmation</h3>
                <p className="text-muted-foreground mb-4">
                  Veuillez vérifier toutes les informations ci-dessus. Une fois confirmé, les modifications du logement seront enregistrées.
                </p>
                <div className="flex items-center space-x-2">
                  <Checkbox id="confirm" />
                  <Label htmlFor="confirm">
                    Je confirme que toutes les informations saisies sont correctes
                  </Label>
                </div>
              </div>
            </div>
          )}
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 1}>
          Précédent
        </Button>
        {currentStep < steps.length ? (
          <Button onClick={handleNext}>Suivant</Button>
        ) : (
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Enregistrer les modifications
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

