# User Journeys Implementation Guide

## Overview

This document describes the implementation of two distinct user journeys in the tenant declaration application with different header configurations for guest and host users.

## 🎯 User Journey Types

### 1. **GUEST/TRAVELER USER JOURNEY**
**Entry Points:** QR Code scanning OR manual accommodation code entry

**Flow:**
1. **Entry Point**: QR code scanning OR manual accommodation code entry
2. **Identity Document Selection**: Choose between ID card or passport, with option for manual data entry as fallback
3. **Document Verification**: 
   - Photo capture of document (front and back sides)
   - RFID/NFC scanning of passport or ID card for data extraction
4. **Liveness Detection**: Facial verification to confirm the person matches the document
5. **Digital Signature**: Guest signs the declaration electronically
6. **Contract Generation** (Optional): Generate and finalize short-term rental agreement

**Header Configuration:**
- ❌ No profile menu/avatar
- ❌ No notification bell/alerts
- ❌ No search functionality
- ✅ Essential branding only (logo, institutional header)
- 🎯 Clean, minimal interface focused on the declaration process

### 2. **HOST USER JOURNEY**
**Entry Points:** Login form

**Flow:**
- Standard host login → Dashboard with full management features

**Header Configuration:**
- ✅ Full profile menu/avatar
- ✅ Notification bell/alerts
- ✅ Search functionality
- ✅ Complete institutional branding
- 🎯 Full management interface

## 🏗️ Technical Implementation

### Core Components

#### 1. **UserTypeContext** (`contexts/FormContext.tsx`)
```typescript
export type UserType = 'guest' | 'host' | null

interface FormContextType {
  userType: UserType;
  setUserType: (type: UserType) => void;
  // ... other form properties
}
```

#### 2. **Enhanced InstitutionalHeader** (`components/InstitutionalHeader.tsx`)
```typescript
interface InstitutionalHeaderProps {
  userType?: UserType
  showMobileActions?: boolean
}
```

#### 3. **PageWrapper Component** (`components/PageWrapper.tsx`)
Automatically handles user type detection and header configuration:
```typescript
interface PageWrapperProps {
  forceUserType?: 'guest' | 'host'
  showMobileActions?: boolean
  children: ReactNode
  className?: string
}
```

#### 4. **useUserType Hook** (`hooks/use-user-type.tsx`)
Automatically detects user type based on current route and manages context.

### Route-Based User Type Detection

**Guest Routes:**
- `/` (Welcome page)
- `/self-check-in`
- `/verification-self-check-in`
- `/summary-self-check-in`
- `/login`
- `/signup`

**Host Routes:**
- `/dashboard`
- `/gestion-logements`
- `/locataires`
- `/ajouter-logement`
- `/renseignement-nuites`
- All other management pages

## 🚀 Usage Examples

### Using PageWrapper (Recommended)

```typescript
// Guest page
export default function SelfCheckInPage() {
  return (
    <PageWrapper forceUserType="guest">
      <NightStayForm />
    </PageWrapper>
  )
}

// Host page
export default function DashboardPage() {
  return (
    <PageWrapper forceUserType="host" showMobileActions={true}>
      <DashboardContent />
    </PageWrapper>
  )
}
```

### Manual Header Configuration

```typescript
// Guest header
<InstitutionalHeader userType="guest" />

// Host header
<InstitutionalHeader userType="host" showMobileActions={true} />
```

### Setting User Type Programmatically

```typescript
const { setUserType } = useForm()

// When guest scans QR code or enters accommodation code
setUserType('guest')

// When user logs in as host
setUserType('host')
```

## 🔄 User Type Flow

### Guest Flow
1. User visits welcome page → `userType` set to `'guest'`
2. User scans QR code or enters accommodation code → `userType` confirmed as `'guest'`
3. User navigates through self-check-in flow → Guest header maintained
4. All guest pages show minimal header with institutional branding only

### Host Flow
1. User visits login page → `userType` set to `'guest'` (login form)
2. User successfully logs in → `userType` set to `'host'`
3. User redirected to dashboard → Host header with full features
4. All host pages show complete header with search, notifications, profile

## 📱 Mobile Considerations

- **Guest Mobile**: No action buttons in header, clean interface
- **Host Mobile**: Full mobile action buttons (search, notifications, profile)
- **Responsive Design**: Both journeys maintain mobile-first approach
- **Touch Targets**: Appropriate sizing for mobile interactions

## 🧪 Testing

### Test Page
Visit `/test-headers` to:
- Switch between guest and host header demos
- Test user type context management
- Verify header feature differences
- Navigate between different user journey pages

### Manual Testing Checklist

**Guest Journey:**
- [ ] Welcome page shows guest header (no mobile actions)
- [ ] QR scanner sets user type to guest
- [ ] Accommodation code input sets user type to guest
- [ ] Self-check-in flow maintains guest header
- [ ] Verification page shows guest header
- [ ] Summary page shows guest header

**Host Journey:**
- [ ] Login page shows guest header (login form)
- [ ] Successful login sets user type to host
- [ ] Dashboard shows host header with mobile actions
- [ ] Property management pages show host header
- [ ] Tenant management pages show host header

## 🔧 Configuration

### Adding New Routes

**For Guest Routes:**
1. Add route to `GUEST_ROUTES` in `hooks/use-user-type.tsx`
2. Use `<PageWrapper forceUserType="guest">` in page component

**For Host Routes:**
1. Add route to `HOST_ROUTES` in `hooks/use-user-type.tsx`
2. Use `<PageWrapper forceUserType="host" showMobileActions={true}>` in page component

### Customizing Headers

The `PageWrapper` component automatically determines the appropriate header configuration, but you can override:

```typescript
<PageWrapper 
  forceUserType="host" 
  showMobileActions={false}  // Override default behavior
>
  {children}
</PageWrapper>
```

## 🎨 Styling Considerations

- **Institutional Branding**: Maintained across both journeys
- **Color Palette**: Consistent institutional colors
- **Typography**: Homogeneous fonts throughout
- **Accessibility**: WCAG compliance maintained
- **Mobile-First**: Responsive design for all screen sizes

## 🔒 Security Considerations

- User type stored in session storage for persistence
- Route-based validation ensures appropriate access
- Guest users cannot access host-only features
- Context automatically clears on navigation away from app
