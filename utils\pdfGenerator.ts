/**
 * Alternative PDF Generation using HTML-to-PDF approach
 * Fallback solution for environments where PDFKit has font issues
 */

export interface PDFGenerationOptions {
  title: string
  data: any
  template: 'declaration' | 'rental-agreement'
}

export class HTMLToPDFGenerator {
  /**
   * Generate PDF from HTML template
   */
  static async generatePDF(options: PDFGenerationOptions): Promise<Blob> {
    const html = this.generateHTML(options)
    
    // Use browser's print functionality to generate PDF
    return new Promise((resolve, reject) => {
      try {
        // Create a hidden iframe for PDF generation
        const iframe = document.createElement('iframe')
        iframe.style.position = 'absolute'
        iframe.style.left = '-9999px'
        iframe.style.width = '210mm'
        iframe.style.height = '297mm'
        
        document.body.appendChild(iframe)
        
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
        if (!iframeDoc) {
          throw new Error('Cannot access iframe document')
        }
        
        iframeDoc.open()
        iframeDoc.write(html)
        iframeDoc.close()
        
        // Wait for content to load
        setTimeout(() => {
          try {
            // Use the iframe's print functionality
            iframe.contentWindow?.print()
            
            // Clean up
            document.body.removeChild(iframe)
            
            // For now, return a mock blob since browser print doesn't return blob
            const mockPDFContent = this.generateMockPDFContent(options)
            const blob = new Blob([mockPDFContent], { type: 'application/pdf' })
            resolve(blob)
          } catch (error) {
            document.body.removeChild(iframe)
            reject(error)
          }
        }, 1000)
        
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * Generate HTML template for PDF
   */
  private static generateHTML(options: PDFGenerationOptions): string {
    const { title, data, template } = options
    
    const baseStyles = `
      <style>
        @page {
          size: A4;
          margin: 2cm;
        }
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 20px;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .subtitle {
          font-size: 14px;
          color: #666;
        }
        .section {
          margin-bottom: 25px;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          border-bottom: 1px solid #ccc;
          padding-bottom: 5px;
          margin-bottom: 15px;
        }
        .field {
          margin-bottom: 10px;
        }
        .field-label {
          font-weight: bold;
          display: inline-block;
          width: 150px;
        }
        .field-value {
          display: inline-block;
        }
        .footer {
          margin-top: 50px;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
        .signature-section {
          margin-top: 40px;
          display: flex;
          justify-content: space-between;
        }
        .signature-box {
          width: 200px;
          text-align: center;
        }
        .signature-line {
          border-top: 1px solid #333;
          margin-top: 60px;
          padding-top: 5px;
        }
      </style>
    `
    
    if (template === 'declaration') {
      return this.generateDeclarationHTML(data, baseStyles)
    } else {
      return this.generateRentalAgreementHTML(data, baseStyles)
    }
  }

  /**
   * Generate declaration HTML
   */
  private static generateDeclarationHTML(data: any, styles: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Déclaration de Séjour</title>
        ${styles}
      </head>
      <body>
        <div class="header">
          <div class="title">DÉCLARATION DE SÉJOUR TOURISTIQUE</div>
          <div class="subtitle">Générée le ${new Date().toLocaleDateString('fr-FR')}</div>
        </div>

        <div class="section">
          <div class="section-title">Informations Personnelles</div>
          <div class="field">
            <span class="field-label">Nom:</span>
            <span class="field-value">${data.nom || 'Non renseigné'}</span>
          </div>
          <div class="field">
            <span class="field-label">Prénom:</span>
            <span class="field-value">${data.prenom || 'Non renseigné'}</span>
          </div>
          <div class="field">
            <span class="field-label">Date de naissance:</span>
            <span class="field-value">${data.dateNaissance || 'Non renseignée'}</span>
          </div>
          <div class="field">
            <span class="field-label">Sexe:</span>
            <span class="field-value">${data.sexe || 'Non renseigné'}</span>
          </div>
          <div class="field">
            <span class="field-label">Lieu de naissance:</span>
            <span class="field-value">${data.lieu || 'Non renseigné'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Pièce d'Identité</div>
          <div class="field">
            <span class="field-label">Type:</span>
            <span class="field-value">${data.typePieceIdentite || 'Non renseigné'}</span>
          </div>
          <div class="field">
            <span class="field-label">Numéro:</span>
            <span class="field-value">${data.numeroPieceIdentite || 'Non renseigné'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Séjour</div>
          <div class="field">
            <span class="field-label">Date d'arrivée:</span>
            <span class="field-value">${data.dateArrivee || 'Non renseignée'}</span>
          </div>
          <div class="field">
            <span class="field-label">Date de départ:</span>
            <span class="field-value">${data.dateDepart || 'Non renseignée'}</span>
          </div>
          <div class="field">
            <span class="field-label">Motif du séjour:</span>
            <span class="field-value">${data.motifSejour || 'Non renseigné'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Adresse</div>
          <div class="field">
            <span class="field-label">Pays:</span>
            <span class="field-value">${data.pays || 'Non renseigné'}</span>
          </div>
          <div class="field">
            <span class="field-label">Ville:</span>
            <span class="field-value">${data.villeResidence || 'Non renseignée'}</span>
          </div>
          <div class="field">
            <span class="field-label">Domicile:</span>
            <span class="field-value">${data.domicileHabituel || 'Non renseigné'}</span>
          </div>
        </div>

        <div class="signature-section">
          <div class="signature-box">
            <div>Signature du déclarant</div>
            <div class="signature-line">Date et signature</div>
          </div>
          <div class="signature-box">
            <div>Cachet de l'établissement</div>
            <div class="signature-line">Cachet officiel</div>
          </div>
        </div>

        <div class="footer">
          Document généré automatiquement par le système de télédéclaration<br>
          Conforme à la réglementation en vigueur
        </div>
      </body>
      </html>
    `
  }

  /**
   * Generate rental agreement HTML
   */
  private static generateRentalAgreementHTML(data: any, styles: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Contrat de Location</title>
        ${styles}
      </head>
      <body>
        <div class="header">
          <div class="title">CONTRAT DE LOCATION SAISONNIÈRE</div>
          <div class="subtitle">Généré le ${new Date().toLocaleDateString('fr-FR')}</div>
        </div>

        <div class="section">
          <div class="section-title">Parties Contractantes</div>
          <p><strong>LE BAILLEUR:</strong><br>
          [Nom du propriétaire]<br>
          [Adresse du propriétaire]</p>
          
          <p><strong>LE LOCATAIRE:</strong><br>
          ${data.nom || '[Nom]'} ${data.prenom || '[Prénom]'}<br>
          Né(e) le ${data.dateNaissance || '[Date de naissance]'}<br>
          Pièce d'identité: ${data.typePieceIdentite || '[Type]'} n° ${data.numeroPieceIdentite || '[Numéro]'}<br>
          Domicile: ${data.domicileHabituel || '[Adresse]'}</p>
        </div>

        <div class="section">
          <div class="section-title">Objet du Contrat</div>
          <p>Le présent contrat a pour objet la location saisonnière du logement situé à [Adresse du logement].</p>
        </div>

        <div class="section">
          <div class="section-title">Durée de la Location</div>
          <div class="field">
            <span class="field-label">Date d'arrivée:</span>
            <span class="field-value">${data.dateArrivee || '[Date d\'arrivée]'}</span>
          </div>
          <div class="field">
            <span class="field-label">Date de départ:</span>
            <span class="field-value">${data.dateDepart || '[Date de départ]'}</span>
          </div>
          <div class="field">
            <span class="field-label">Motif du séjour:</span>
            <span class="field-value">${data.motifSejour || '[Motif]'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Conditions Générales</div>
          <ol>
            <li>Le locataire s'engage à occuper personnellement les lieux loués.</li>
            <li>Le locataire s'engage à restituer les lieux en bon état.</li>
            <li>Toute sous-location est interdite.</li>
            <li>Le locataire s'engage à respecter la tranquillité du voisinage.</li>
          </ol>
        </div>

        <div class="signature-section">
          <div class="signature-box">
            <div>Signature du bailleur</div>
            <div class="signature-line">Date et signature</div>
          </div>
          <div class="signature-box">
            <div>Signature du locataire</div>
            <div class="signature-line">Date et signature</div>
          </div>
        </div>

        <div class="footer">
          Contrat généré automatiquement - ${new Date().toLocaleDateString('fr-FR')}
        </div>
      </body>
      </html>
    `
  }

  /**
   * Generate mock PDF content for fallback
   */
  private static generateMockPDFContent(options: PDFGenerationOptions): string {
    const { title, data, template } = options
    
    return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 750 Td
(${title}) Tj
0 -20 Td
(Nom: ${data.nom || 'Non renseigné'}) Tj
0 -20 Td
(Prénom: ${data.prenom || 'Non renseigné'}) Tj
0 -20 Td
(Date: ${new Date().toLocaleDateString('fr-FR')}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000526 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
623
%%EOF`
  }
}

/**
 * Server-side PDF generation fallback
 */
export function generateSimplePDFContent(data: any, type: 'declaration' | 'rental-agreement'): string {
  const title = type === 'declaration' ? 'DÉCLARATION DE SÉJOUR' : 'CONTRAT DE LOCATION'
  const date = new Date().toLocaleDateString('fr-FR')
  
  return `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R/Resources<</Font<</F1 5 0 R>>>>>>endobj
4 0 obj<</Length ${200 + JSON.stringify(data).length}>>stream
BT/F1 16 Tf 50 750 Td(${title}) Tj 0 -30 Td/F1 12 Tf(Généré le: ${date}) Tj 0 -40 Td(Nom: ${data.nom || 'Non renseigné'}) Tj 0 -20 Td(Prénom: ${data.prenom || 'Non renseigné'}) Tj 0 -20 Td(Date de naissance: ${data.dateNaissance || 'Non renseignée'}) Tj 0 -20 Td(Numéro pièce: ${data.numeroPieceIdentite || 'Non renseigné'}) Tj 0 -20 Td(Date arrivée: ${data.dateArrivee || 'Non renseignée'}) Tj 0 -20 Td(Date départ: ${data.dateDepart || 'Non renseignée'}) Tj 0 -40 Td(Document généré automatiquement) Tj ET
endstream endobj
5 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj
xref 0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000526 00000 n 
trailer<</Size 6/Root 1 0 R>>startxref 623 %%EOF`
}
