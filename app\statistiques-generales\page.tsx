'use client'

import dynamic from 'next/dynamic'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'

const StatisticsDashboard = dynamic(() => import('@/components/StatisticsDashboard').then(mod => ({ default: mod.StatisticsDashboard })), {
  ssr: false,
  loading: () => <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background flex items-center justify-center">
    <div className="text-center">
      <div className="w-8 h-8 border-4 border-institutional-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-600">Chargement des statistiques...</p>
    </div>
  </div>
})

export default function StatisticsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <StatisticsDashboard />
    </div>
  )
}

