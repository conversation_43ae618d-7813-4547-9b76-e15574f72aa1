export type FormStep = 1 | 2 | 3 | 4;

export interface PersonalInfo {
  firstName: string;
  surname: string;
  dateOfBirth: string;
  placeOfBirth: string;
  fatherName: string;
  motherName: string;
  occupation: string;
  permanentAddress: string;
  childrenUnder15: number;
}

export interface TravelInfo {
  nationalId: string;
  idScan: File | null;
  selfie: File | null;
  travelType: 'Domestic' | 'International';
  checkIn: string;
  checkOut: string;
  reasonForTravel: string;
  nationality?: string;
  nextDestination?: string;
}

export interface ContactInfo {
  phoneNumber: string;
  email: string;
  signature: string;
}

export interface FormData extends PersonalInfo, TravelInfo, ContactInfo {
  hotelName: string;
  isDataCorrect: boolean;
}

