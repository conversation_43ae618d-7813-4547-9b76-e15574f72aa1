'use client'

import { useState, useEffect } from 'react'
import { QrReader } from 'react-qr-reader'
import { Button } from '@/components/ui/button'
// Using custom modal for better centering control
import { useRouter } from 'next/navigation'
import { useForm } from '@/contexts/FormContext'
import { Camera, AlertCircle, CheckCircle } from 'lucide-react'

interface QRCodeScannerProps {
  onClose: () => void
}

export function QRCodeScanner({ onClose }: QRCodeScannerProps) {
  const [error, setError] = useState<string | null>(null)
  const [isScanning, setIsScanning] = useState(true)
  const [cameraReady, setCameraReady] = useState(false)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const [qrReaderError, setQrReaderError] = useState(false)
  const router = useRouter()
  const { setUserType } = useForm()

  useEffect(() => {
    // Auto-start scanning when component mounts
    checkCameraPermission()
  }, [])

  const checkCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      setCameraReady(true)
      setError(null)
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop())
    } catch (err) {
      console.error('Camera permission error:', err)
      setError('Impossible d\'accéder à la caméra. Veuillez autoriser l\'accès à la caméra et réessayer.')
      setIsScanning(false)
    }
  }

  const handleScan = (result: any) => {
    if (result) {
      // Handle different result formats from different versions of react-qr-reader
      let text = ''
      if (typeof result === 'string') {
        text = result
      } else if (result.text) {
        text = result.text
      } else if (result.getText && typeof result.getText === 'function') {
        text = result.getText()
      } else if (result.data) {
        text = result.data
      }

      if (text && text.trim()) {
        console.log('QR Code scanned:', text)

        // Check if it's an accommodation code format (T-XXXXXX)
        const accommodationCodeRegex = /^T-\d{6}$/
        if (accommodationCodeRegex.test(text.trim())) {
          // Store the accommodation code
          sessionStorage.setItem('accommodationCode', text.trim())
          console.log('Accommodation code detected:', text.trim())
        }

        setScanResult(text)
        setIsScanning(false)

        // Show success message briefly then navigate
        setTimeout(() => {
          setUserType('guest')
          router.push('/self-check-in')
          onClose()
        }, 1500)
      }
    }
  }

  const handleError = (err: any) => {
    console.error('QR Scanner error:', err)
    if (err?.name === 'NotAllowedError') {
      setError('Accès à la caméra refusé. Veuillez autoriser l\'accès à la caméra dans les paramètres de votre navigateur.')
    } else if (err?.name === 'NotFoundError') {
      setError('Aucune caméra trouvée sur cet appareil.')
    } else {
      setError('Erreur lors de l\'initialisation de la caméra. Veuillez réessayer.')
    }
    setIsScanning(false)
  }

  const retryCamera = () => {
    setError(null)
    setQrReaderError(false)
    setIsScanning(true)
    setCameraReady(false)
    checkCameraPermission()
  }

  const handleNext = () => {
    setUserType('guest')
    router.push('/self-check-in')
    onClose()
  }

  const simulateQRScan = () => {
    // Simulate a successful QR code scan for testing
    setScanResult('TEST_QR_CODE_12345')
    setIsScanning(false)
    setTimeout(() => {
      setUserType('guest')
      router.push('/self-check-in')
      onClose()
    }, 1500)
  }

  return (
    <div
      className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="w-full max-w-lg max-h-[85vh] overflow-y-auto rounded-3xl border-0 shadow-2xl bg-white/95 backdrop-blur-sm logo-background-content p-4 sm:p-6"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="text-center pb-4">
          <div className="mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
              <Camera className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Scanner QR Code
            </h2>
          </div>
          <p className="text-gray-600 text-sm leading-relaxed">
            {scanResult ?
              '✅ Code d\'hébergement détecté! Redirection en cours...' :
              error || qrReaderError ?
                '⚠️ Veuillez autoriser l\'accès à la caméra pour scanner le QR code' :
                '📱 Scannez le QR code fourni par votre hôte pour accéder au check-in'
            }
          </p>

          {/* Instructions */}
          {!error && !scanResult && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-left space-y-1">
                <p className="text-blue-800 text-xs font-medium">Instructions:</p>
                <ul className="text-blue-700 text-xs space-y-0.5">
                  <li>• Tenez votre appareil stable</li>
                  <li>• Centrez le QR code dans le cadre</li>
                </ul>

                {/* Development test button */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-2 pt-1 border-t border-blue-200">
                    <Button
                      onClick={simulateQRScan}
                      size="sm"
                      variant="outline"
                      className="text-xs h-6 bg-blue-100 hover:bg-blue-200 border-blue-300"
                    >
                      🧪 Test QR Scan
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="relative">
          {/* Scanner Container */}
          <div className="relative bg-gradient-to-br from-gray-900 via-black to-gray-800 rounded-3xl overflow-hidden aspect-square max-h-72 shadow-2xl border-4 border-institutional-primary/20">
            {isScanning && cameraReady && !error && !qrReaderError ? (
              <div className="w-full h-full">
                <QrReader
                  onResult={(result, error) => {
                    try {
                      // Suppress internal QR reader errors that don't affect functionality
                      if (error) {
                        // Only log significant errors, ignore pattern detection errors
                        if (error.message && error.message !== 'e' && !error.message.includes('pattern')) {
                          console.warn('QR Reader warning:', error.message)
                        }
                        return
                      }
                      if (result) {
                        handleScan(result)
                      }
                    } catch (err) {
                      console.error('Error in QR result handler:', err)
                      setQrReaderError(true)
                    }
                  }}
                  constraints={{
                    facingMode: 'environment'
                  }}
                  containerStyle={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '16px'
                  }}
                  videoStyle={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '16px'
                  }}
                  scanDelay={500}
                  ViewFinder={() => null}
                />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-2xl">
                {error || qrReaderError ? (
                  <div className="text-center p-6">
                    <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                    <p className="text-white text-sm mb-2">
                      {qrReaderError ? 'Scanner non disponible' : 'Caméra non disponible'}
                    </p>
                    <p className="text-white/70 text-xs">
                      Utilisez la saisie manuelle
                    </p>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
                    <p className="text-white text-sm">Initialisation de la caméra...</p>
                  </div>
                )}
              </div>
            )}

            {/* Success State */}
            {scanResult && (
              <div className="absolute inset-0 bg-gradient-to-br from-green-500 via-green-600 to-emerald-600 flex items-center justify-center rounded-3xl">
                <div className="text-center animate-bounce">
                  <div className="relative">
                    <CheckCircle className="w-20 h-20 text-white mx-auto mb-4 drop-shadow-lg" />
                    <div className="absolute inset-0 w-20 h-20 mx-auto rounded-full bg-white/20 animate-ping"></div>
                  </div>
                  <p className="text-white text-xl font-bold mb-2 drop-shadow-md">✅ Code Détecté!</p>
                  <p className="text-white/90 text-sm font-medium">Accès au check-in autorisé</p>
                  <div className="mt-3 flex justify-center">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Scanning Overlay */}
            {isScanning && cameraReady && !error && !scanResult && (
              <div className="absolute inset-0 flex items-center justify-center">
                {/* Outer glow effect */}
                <div className="absolute w-40 h-40 border-2 border-institutional-primary/30 rounded-2xl animate-pulse"></div>

                {/* Main scanning frame */}
                <div className="w-36 h-36 relative">
                  {/* Corner indicators with enhanced styling */}
                  <div className="absolute -top-2 -left-2 w-6 h-6 border-l-4 border-t-4 border-institutional-primary rounded-tl-xl shadow-lg animate-pulse"></div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 border-r-4 border-t-4 border-institutional-primary rounded-tr-xl shadow-lg animate-pulse"></div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 border-l-4 border-b-4 border-institutional-primary rounded-bl-xl shadow-lg animate-pulse"></div>
                  <div className="absolute -bottom-2 -right-2 w-6 h-6 border-r-4 border-b-4 border-institutional-primary rounded-br-xl shadow-lg animate-pulse"></div>

                  {/* Animated scanning line */}
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-institutional-primary to-transparent animate-pulse shadow-lg"></div>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-institutional-primary to-transparent animate-pulse shadow-lg"></div>

                  {/* Center instruction with better styling */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white/95 backdrop-blur-sm px-3 py-2 rounded-xl shadow-lg border border-institutional-primary/20">
                      <p className="text-institutional-primary text-xs font-semibold text-center">
                        📱 Positionnez le QR code ici
                      </p>
                    </div>
                  </div>

                  {/* Scanning animation dots */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-institutional-primary rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-institutional-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-1 h-1 bg-institutional-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Status indicator */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex items-center gap-3 bg-black/80 backdrop-blur-md rounded-2xl px-5 py-3 border border-white/10 shadow-xl">
                <div className={`w-3 h-3 rounded-full shadow-lg ${
                  scanResult ? 'bg-green-400 shadow-green-400/50' :
                  isScanning && cameraReady ? 'bg-institutional-primary animate-pulse shadow-institutional-primary/50' :
                  'bg-gray-400'
                }`}></div>
                <span className="text-white text-sm font-semibold">
                  {scanResult ? '✅ Code détecté!' :
                   isScanning && cameraReady && !qrReaderError ? '🔍 Recherche en cours...' :
                   error || qrReaderError ? '⚠️ Scanner indisponible' :
                   '⏳ Initialisation...'}
                </span>
              </div>
            </div>
          </div>

          {(error || qrReaderError) && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-800 text-xs font-medium mb-1">
                    {qrReaderError ? 'Scanner indisponible' : 'Erreur caméra'}
                  </p>
                  <p className="text-red-600 text-xs">
                    {qrReaderError
                      ? 'Utilisez la saisie manuelle.'
                      : 'Autorisez l\'accès à la caméra.'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 mt-6">
          {/* Primary Action */}
          {error || qrReaderError ? (
            <Button
              onClick={retryCamera}
              className="w-full h-12 bg-institutional-primary hover:bg-green-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Camera className="w-5 h-5 mr-2" />
              Réessayer la caméra
            </Button>
          ) : scanResult ? (
            <Button
              onClick={handleNext}
              className="w-full h-12 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <CheckCircle className="w-5 h-5 mr-2" />
              Accéder au Check-in
            </Button>
          ) : (
            <Button
              onClick={retryCamera}
              disabled={isScanning && cameraReady}
              className="w-full h-12 bg-institutional-primary hover:bg-green-600 text-white rounded-xl font-semibold disabled:opacity-50 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isScanning && cameraReady ? (
                <>
                  <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Recherche de QR code...
                </>
              ) : (
                <>
                  <Camera className="w-5 h-5 mr-2" />
                  {cameraReady ? 'Redémarrer le scanner' : 'Démarrer le scanner'}
                </>
              )}
            </Button>
          )}

          {/* Secondary Action - Only Cancel */}
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full h-12 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 rounded-xl font-semibold transition-all duration-200"
          >
            Retour
          </Button>

          {/* Information Note */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-xl">
            <p className="text-blue-800 text-xs text-center">
              💡 <strong>Code d'hébergement requis:</strong> Vous devez scanner le QR code ou saisir le code fourni par votre hôte pour accéder au check-in.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

