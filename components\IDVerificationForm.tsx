'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { useIsMobile } from '@/hooks/use-mobile'
import { Camera, CreditCard, Scan, Shield, Pen, CheckCircle2, ArrowLeft, ArrowRight, Smartphone, Fingerprint } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"

type VerificationStep = 'recto' | 'verso' | 'nfc' | 'signature' | 'complete'

export function IDVerificationForm() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null)
  const [currentStep, setCurrentStep] = useState<VerificationStep>('recto')
  const [showScanner, setShowScanner] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [isDrawing, setIsDrawing] = useState(false)
  const { toast } = useToast()
  const [verificationData, setVerificationData] = useState({
    recto: false,
    verso: false,
    nfc: false,
    liveness: false,
    signature: null as string | null
  })

  const steps = [
    { key: 'recto', title: 'Recto', description: 'Scanner le recto de votre pièce d\'identité' },
    { key: 'verso', title: 'Verso', description: 'Scanner le verso de votre pièce d\'identité' },
    { key: 'nfc', title: 'NFC', description: 'Lecture de la puce électronique' },
    { key: 'signature', title: 'Signature', description: 'Apposer votre signature numérique' }
  ]

  const getCurrentStepIndex = () => steps.findIndex(step => step.key === currentStep)
  const getProgress = () => ((getCurrentStepIndex() + 1) / steps.length) * 100

  const handlePrevious = () => {
    router.back()
  }

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'accéder à la caméra",
      })
    }
  }

  const stopCamera = () => {
    const stream = videoRef.current?.srcObject as MediaStream
    stream?.getTracks().forEach(track => track.stop())
  }

  const handleScan = async () => {
    setIsScanning(true)
    
    // Simulate scanning process
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    if (currentStep === 'recto') {
      setVerificationData(prev => ({ ...prev, recto: true }))
      setCurrentStep('verso')
    } else if (currentStep === 'verso') {
      setVerificationData(prev => ({ ...prev, verso: true }))
      setCurrentStep('nfc')
    }
    
    setIsScanning(false)
    setShowScanner(false)
    stopCamera()
    
    toast({
      title: "Succès",
      description: `Scan ${currentStep} réussi`,
    })
  }

  const handleNFCScan = async () => {
    setIsScanning(true)
    
    try {
      // Check if NFC is available
      if ('NDEFReader' in window) {
        const ndef = new (window as any).NDEFReader()
        await ndef.scan()
        
        // Listen for NFC messages
        ndef.addEventListener('reading', () => {
          setVerificationData(prev => ({ ...prev, nfc: true }))
          setCurrentStep('signature')
          toast({
            title: "Succès",
            description: "Lecture NFC réussie",
          })
        })
      } else {
        throw new Error('NFC non disponible')
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Erreur lors de la lecture NFC. Simulation activée...",
      })
      
      // Simulate NFC read for development
      await new Promise(resolve => setTimeout(resolve, 2000))
      setVerificationData(prev => ({ ...prev, nfc: true }))
      setCurrentStep('signature')
    }
    
    setIsScanning(false)
  }

  const handleLivenessCheck = async () => {
    setIsScanning(true)
    
    // Simulate liveness detection and face matching
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setVerificationData(prev => ({ ...prev, liveness: true }))
    setCurrentStep('signature')
    setIsScanning(false)
    
    toast({
      title: "Succès",
      description: "Vérification biométrique réussie",
    })
  }

  const handleSignatureStart = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true)
    const canvas = signatureCanvasRef.current
    const ctx = canvas?.getContext('2d')
    if (ctx) {
      ctx.beginPath()
      if ('touches' in e) {
        ctx.moveTo(e.touches[0].clientX - canvas!.offsetLeft, e.touches[0].clientY - canvas!.offsetTop)
      } else {
        ctx.moveTo(e.clientX - canvas!.offsetLeft, e.clientY - canvas!.offsetTop)
      }
    }
  }

  const handleSignatureMove = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return
    const canvas = signatureCanvasRef.current
    const ctx = canvas?.getContext('2d')
    if (ctx) {
      if ('touches' in e) {
        ctx.lineTo(e.touches[0].clientX - canvas!.offsetLeft, e.touches[0].clientY - canvas!.offsetTop)
      } else {
        ctx.lineTo(e.clientX - canvas!.offsetLeft, e.clientY - canvas!.offsetTop)
      }
      ctx.stroke()
    }
  }

  const handleSignatureEnd = () => {
    setIsDrawing(false)
  }

  const handleClearSignature = () => {
    const canvas = signatureCanvasRef.current
    const ctx = canvas?.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvas!.width, canvas!.height)
    }
  }

  const handleSaveSignature = () => {
    const canvas = signatureCanvasRef.current
    if (canvas) {
      const signatureData = canvas.toDataURL()
      setVerificationData(prev => ({ ...prev, signature: signatureData }))
      setCurrentStep('complete')
    }
  }

  const renderStepIcon = (step: VerificationStep) => {
    const isComplete = verificationData[step]
    const isCurrent = currentStep === step
    const baseClass = "w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300"
    const activeClass = isCurrent ? "bg-blue-100 ring-2 ring-blue-500" : "bg-gray-100"
    const completedClass = isComplete ? "bg-green-100" : ""
    
    return (
      <div className={`${baseClass} ${activeClass} ${completedClass}`}>
        {isComplete ? (
          <CheckCircle2 className="w-10 h-10 text-green-500" />
        ) : (
          <>
            {step === 'recto' && <CreditCard className={`w-10 h-10 ${isCurrent ? 'text-blue-500' : 'text-gray-400'}`} />}
            {step === 'verso' && <CreditCard className={`w-10 h-10 ${isCurrent ? 'text-blue-500' : 'text-gray-400'}`} />}
            {step === 'nfc' && <Scan className={`w-10 h-10 ${isCurrent ? 'text-blue-500' : 'text-gray-400'}`} />}
            {step === 'signature' && <Pen className={`w-10 h-10 ${isCurrent ? 'text-blue-500' : 'text-gray-400'}`} />}
          </>
        )}
      </div>
    )
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'recto':
      case 'verso':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-center w-16 h-16 mx-auto bg-institutional-primary/10 rounded-full mb-4">
              <Camera className="w-8 h-8 text-institutional-primary" />
            </div>
            <Button
              onClick={() => setShowScanner(true)}
              className="w-full mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target"
              disabled={isScanning}
            >
              {isScanning ? (
                <>
                  <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Scan en cours...
                </>
              ) : (
                <>
                  <Camera className="w-5 h-5 mr-2" />
                  Scanner le {currentStep === 'recto' ? 'recto' : 'verso'}
                </>
              )}
            </Button>
          </div>
        )
      case 'nfc':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-center w-16 h-16 mx-auto bg-blue-500/10 rounded-full mb-4">
              <Smartphone className="w-8 h-8 text-blue-600" />
            </div>
            <Button
              onClick={handleNFCScan}
              className="w-full mobile-button bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target"
              disabled={isScanning}
            >
              {isScanning ? (
                <>
                  <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Lecture NFC...
                </>
              ) : (
                <>
                  <Scan className="w-5 h-5 mr-2" />
                  Lire la puce NFC
                </>
              )}
            </Button>
          </div>
        )
      case 'signature':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-center w-16 h-16 mx-auto bg-purple-500/10 rounded-full mb-4">
              <Pen className="w-8 h-8 text-purple-600" />
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Signez dans la zone ci-dessous avec votre doigt ou stylet
            </p>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-white">
              <canvas
                ref={signatureCanvasRef}
                width={isMobile ? 280 : 400}
                height={isMobile ? 150 : 200}
                className="border border-gray-200 rounded-lg w-full touch-none"
                onMouseDown={handleSignatureStart}
                onMouseMove={handleSignatureMove}
                onMouseUp={handleSignatureEnd}
                onMouseLeave={handleSignatureEnd}
                onTouchStart={handleSignatureStart}
                onTouchMove={handleSignatureMove}
                onTouchEnd={handleSignatureEnd}
              />
            </div>
            <div className="flex gap-3">
              <Button
                onClick={handleClearSignature}
                variant="outline"
                className="flex-1 mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Effacer
              </Button>
              <Button
                onClick={handleSaveSignature}
                className="flex-1 mobile-button bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target"
              >
                <CheckCircle2 className="w-4 h-4 mr-2" />
                Enregistrer
              </Button>
            </div>
          </div>
        )
      case 'complete':
        return (
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center w-20 h-20 mx-auto bg-green-500/10 rounded-full mb-4">
              <Shield className="w-10 h-10 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-green-600">Vérification Complète !</h3>
            <p className="text-gray-600">
              Votre identité a été vérifiée avec succès. Vous pouvez maintenant continuer.
            </p>
            <div className="grid grid-cols-2 gap-4 mt-6">
              {Object.entries(verificationData).map(([key, value]) => (
                key !== 'signature' && (
                  <div key={key} className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-green-800 capitalize">{key}</span>
                  </div>
                )
              ))}
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        {/* Enhanced Mobile-First Back Button */}
        <div className="mb-4 sm:mb-6">
          <Button
            variant="ghost"
            onClick={handlePrevious}
            className="touch-target text-gray-600 hover:text-institutional-primary"
            disabled={isScanning}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {isMobile ? 'Retour' : 'Retour'}
          </Button>
        </div>

        <Card className="max-w-2xl mx-auto bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card animate-slide-up-from-bottom">
          <CardHeader className="mobile-padding pb-4 sm:pb-6 text-center">
            <CardTitle className="mobile-text-xl font-bold text-gray-900">
              {isMobile ? 'Vérification d\'Identité' : 'Vérification d\'Identité'}
            </CardTitle>
            <p className="text-gray-600 text-sm sm:text-base mt-2">
              Suivez les étapes pour vérifier votre identité
            </p>

            {/* Progress Bar */}
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Étape {getCurrentStepIndex() + 1} sur {steps.length}</span>
                <span>{Math.round(getProgress())}%</span>
              </div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-institutional-primary to-green-600 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${getProgress()}%` }}
                ></div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="mobile-padding pt-0 space-y-6">
            {/* Enhanced Step Indicators */}
            <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-4'} gap-4`}>
              {steps.map((step, index) => {
                const isComplete = verificationData[step.key as keyof typeof verificationData]
                const isCurrent = currentStep === step.key
                const isAccessible = index <= getCurrentStepIndex()

                return (
                  <div
                    key={step.key}
                    className={`text-center space-y-3 p-4 rounded-2xl transition-all duration-300 border-2 ${
                      isCurrent
                        ? 'bg-gradient-to-br from-institutional-primary/10 to-green-500/10 border-institutional-primary/30 shadow-lg'
                        : isComplete
                        ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className={`w-14 h-14 mx-auto rounded-2xl flex items-center justify-center transition-all duration-300 shadow-md ${
                      isComplete
                        ? 'bg-gradient-to-br from-green-500 to-emerald-600 text-white scale-110'
                        : isCurrent
                          ? 'bg-gradient-to-br from-institutional-primary to-green-600 text-white scale-110'
                          : isAccessible
                            ? 'bg-gray-200 text-gray-600'
                            : 'bg-gray-100 text-gray-400'
                    }`}>
                      {isComplete ? (
                        <CheckCircle2 className="w-7 h-7" />
                      ) : (
                        <>
                          {step.key === 'recto' && <CreditCard className="w-7 h-7" />}
                          {step.key === 'verso' && <CreditCard className="w-7 h-7" />}
                          {step.key === 'nfc' && <Smartphone className="w-7 h-7" />}
                          {step.key === 'signature' && <Pen className="w-7 h-7" />}
                        </>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className={`text-sm font-bold ${
                        isCurrent ? 'text-institutional-primary' :
                        isComplete ? 'text-green-700' : 'text-gray-700'
                      }`}>
                        {step.title}
                      </p>
                      {!isMobile && (
                        <p className="text-xs text-gray-500 leading-tight">
                          {step.description}
                        </p>
                      )}
                    </div>
                    {isComplete && (
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 border-green-200">
                        ✓ Terminé
                      </Badge>
                    )}
                    {isCurrent && !isComplete && (
                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                        En cours...
                      </Badge>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Current Step Content */}
            <div className="bg-gray-50 mobile-card p-6 text-center space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {steps.find(s => s.key === currentStep)?.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {steps.find(s => s.key === currentStep)?.description}
              </p>

              {renderStepContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-4 border-t border-gray-100">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={getCurrentStepIndex() === 0 || isScanning}
                className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target order-2 sm:order-1"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Précédent
              </Button>

              <Button
                className="mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target flex-1 sm:flex-initial order-1 sm:order-2"
                disabled={currentStep !== 'complete' || isScanning}
                onClick={() => router.push('/summary')}
              >
                {currentStep === 'complete' ? (
                  <>
                    <CheckCircle2 className="w-4 h-4 mr-2" />
                    Terminer
                  </>
                ) : (
                  <>
                    Suivant
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Mobile-First Scanner Dialog */}
      <Dialog open={showScanner} onOpenChange={(open) => {
        if (!open) stopCamera()
        setShowScanner(open)
        if (open) startCamera()
      }}>
        <DialogContent className={`${isMobile ? 'max-w-sm mx-4' : 'sm:max-w-md'} mobile-card`}>
          <DialogHeader className="text-center">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Scanner le {currentStep === 'recto' ? 'recto' : 'verso'}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Placez votre pièce d'identité dans le cadre et appuyez sur scanner
            </p>
          </DialogHeader>

          <div className="relative bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              className="w-full aspect-video object-cover"
            />

            {/* Scanning Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="border-2 border-dashed border-white/70 w-4/5 aspect-[1.6/1] rounded-lg flex items-center justify-center">
                <div className="text-white/70 text-center">
                  <CreditCard className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-xs">Placez votre carte ici</p>
                </div>
              </div>
            </div>

            {/* Scanning Animation */}
            {isScanning && (
              <div className="absolute inset-0 bg-institutional-primary/20 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-2" />
                  <p className="text-sm">Scan en cours...</p>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex flex-col gap-3">
            <Button
              onClick={handleScan}
              disabled={isScanning}
              className="w-full mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target"
            >
              {isScanning ? (
                <>
                  <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Scan en cours...
                </>
              ) : (
                <>
                  <Camera className="w-5 h-5 mr-2" />
                  Scanner maintenant
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={() => setShowScanner(false)}
              disabled={isScanning}
              className="w-full mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
            >
              Annuler
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

