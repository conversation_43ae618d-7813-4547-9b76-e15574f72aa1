'use client'

import { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { GeneratedContract } from '@/components/GeneratedContract'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { Button } from "@/components/ui/button"
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

function ContratGenereContent() {
  const searchParams = useSearchParams()
  const logementId = searchParams.get('logement')
  const voyageurId = searchParams.get('voyageur')

  // Mock data (in a real app, you'd fetch this data based on the IDs)
  const logements = [
    { id: 1, name: "Villa Sunset" },
    { id: 2, name: "Appartement City Center" },
    { id: 3, name: "Chalet Montagne" },
  ]
  const voyageurs = [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "<PERSON>" },
    { id: 3, name: "<PERSON>" },
  ]

  const logement = logements.find(l => l.id.toString() === logementId)?.name || ''
  const voyageur = voyageurs.find(v => v.id.toString() === voyageurId)?.name || ''

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <GeneratedContract logement={logement} voyageur={voyageur} />
    </div>
  )
}

export default function ContratGenerePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ContratGenereContent />
    </Suspense>
  )
}

