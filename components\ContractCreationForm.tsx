'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useRouter } from 'next/navigation'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  FileText,
  Home,
  User,
  Calendar,
  MapPin,
  Edit3,
  Download,
  Eye,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

// Enhanced mock data for dropdowns
const logements = [
  {
    id: 1,
    name: "Villa Sunset",
    address: "123 Beach Road, Nice",
    type: "Villa",
    status: "available"
  },
  {
    id: 2,
    name: "Appartement City Center",
    address: "45 Urban Street, Paris",
    type: "Appartement",
    status: "available"
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    address: "78 Alpine Way, Chamonix",
    type: "<PERSON><PERSON>",
    status: "occupied"
  },
]

const voyageurs = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+33 6 12 34 56 78",
    nationality: "<PERSON>an<PERSON>"
  },
  {
    id: 2,
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "+33 6 87 65 43 21",
    nationality: "Britannique"
  },
  {
    id: 3,
    name: "<PERSON> Dupont",
    email: "<EMAIL>",
    phone: "+33 6 11 22 33 44",
    nationality: "Fran<PERSON>"
  },
]

const templateContent = `
Contrat engagement en réseau

GENERALITES
- Nom du logement
- Adresse du logement
- Ville du logement
- Code postal du logement
- Date de début
- Date de fin

Clauses générales:
1. Le locataire s'engage à respecter les règles de copropriété
2. Le locataire doit maintenir le logement en bon état
3. Les animaux ne sont pas autorisés sauf accord préalable
4. Le nombre d'occupants ne peut excéder celui indiqué au contrat
5. Les nuisances sonores sont interdites entre 22h et 7h
6. Le locataire doit souscrire une assurance habitation
7. L'état des lieux sera effectué à l'entrée et à la sortie
8. Le dépôt de garantie sera restitué dans les délais légaux
`

export function ContractCreationForm() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [selectedLogement, setSelectedLogement] = useState('')
  const [selectedVoyageur, setSelectedVoyageur] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const selectedLogementData = logements.find(l => l.id.toString() === selectedLogement)
  const selectedVoyageurData = voyageurs.find(v => v.id.toString() === selectedVoyageur)
  const isFormValid = selectedLogement && selectedVoyageur

  const handleCreateContract = async () => {
    if (!isFormValid) {
      alert("Veuillez sélectionner un logement et un voyageur avant de créer le contrat.")
      return
    }

    setIsLoading(true)
    try {
      // Simulate contract creation
      await new Promise(resolve => setTimeout(resolve, 2000))
      router.push(`/contrat-genere?logement=${selectedLogement}&voyageur=${selectedVoyageur}`)
    } catch (error) {
      console.error('Error creating contract:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        <div className="max-w-4xl mx-auto space-y-6">

          {/* Enhanced Mobile-First Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <h1 className="mobile-text-xl font-bold text-gray-900">
                  {isMobile ? 'Créer un Contrat' : 'Création de Contrat de Location'}
                </h1>
                <p className="text-gray-600 text-sm sm:text-base">
                  Générez un contrat personnalisé pour vos locataires
                </p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center justify-center gap-2">
              <div className="flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-blue-700 font-medium">Étape 1/3 - Sélection</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 mobile-gap">

            {/* Selection Form */}
            <Card className="bg-white/98 backdrop-blur-2xl shadow-lg border border-white/20 mobile-card animate-slide-up-from-bottom">
              <CardHeader className="mobile-padding pb-4">
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-institutional-primary" />
                  Informations du Contrat
                </CardTitle>
              </CardHeader>

              <CardContent className="mobile-padding pt-0 space-y-6">
                {/* Property Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Home className="w-4 h-4 text-institutional-primary" />
                    Sélectionner le logement
                  </Label>
                  <Select
                    onValueChange={(value) => setSelectedLogement(value)}
                    disabled={isLoading}
                    value={selectedLogement}
                  >
                    <SelectTrigger className="mobile-input mobile-card">
                      <SelectValue placeholder="Choisir un logement..." />
                    </SelectTrigger>
                    <SelectContent>
                      {logements.map((logement) => (
                        <SelectItem
                          key={logement.id}
                          value={logement.id.toString()}
                          disabled={logement.status === 'occupied'}
                        >
                          <div className="flex items-center justify-between w-full">
                            <div>
                              <div className="font-medium">{logement.name}</div>
                              <div className="text-xs text-gray-500">{logement.address}</div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {logement.type}
                              </Badge>
                              {logement.status === 'occupied' && (
                                <Badge variant="destructive" className="text-xs">
                                  Occupé
                                </Badge>
                              )}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* Selected Property Info */}
                  {selectedLogementData && (
                    <div className="p-3 bg-green-50 border border-green-200 mobile-card">
                      <div className="flex items-center gap-2 text-green-800">
                        <CheckCircle className="w-4 h-4" />
                        <span className="font-medium">{selectedLogementData.name}</span>
                      </div>
                      <div className="flex items-center gap-1 text-green-600 text-sm mt-1">
                        <MapPin className="w-3 h-3" />
                        <span>{selectedLogementData.address}</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Traveler Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <User className="w-4 h-4 text-institutional-primary" />
                    Sélectionner le voyageur
                  </Label>
                  <Select
                    onValueChange={(value) => setSelectedVoyageur(value)}
                    disabled={isLoading}
                    value={selectedVoyageur}
                  >
                    <SelectTrigger className="mobile-input mobile-card">
                      <SelectValue placeholder="Choisir un voyageur..." />
                    </SelectTrigger>
                    <SelectContent>
                      {voyageurs.map((voyageur) => (
                        <SelectItem key={voyageur.id} value={voyageur.id.toString()}>
                          <div className="flex items-center justify-between w-full">
                            <div>
                              <div className="font-medium">{voyageur.name}</div>
                              <div className="text-xs text-gray-500">{voyageur.email}</div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {voyageur.nationality}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* Selected Traveler Info */}
                  {selectedVoyageurData && (
                    <div className="p-3 bg-blue-50 border border-blue-200 mobile-card">
                      <div className="flex items-center gap-2 text-blue-800">
                        <CheckCircle className="w-4 h-4" />
                        <span className="font-medium">{selectedVoyageurData.name}</span>
                      </div>
                      <div className="text-blue-600 text-sm mt-1">
                        <div>{selectedVoyageurData.email}</div>
                        <div>{selectedVoyageurData.phone}</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-4 border-t border-gray-100">
                  <Button
                    className="w-full mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 relative overflow-hidden group"
                    onClick={handleCreateContract}
                    disabled={!isFormValid || isLoading}
                  >
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                        <FileText className="w-5 h-5 mr-2 relative z-10" />
                        <span className="relative z-10">Créer le Contrat</span>
                      </>
                    )}
                  </Button>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1 mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
                      onClick={() => setShowPreview(!showPreview)}
                      disabled={isLoading}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      {showPreview ? 'Masquer' : 'Aperçu'}
                    </Button>

                    <Button
                      variant="outline"
                      className="flex-1 mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200 touch-target"
                      disabled={isLoading}
                    >
                      <Edit3 className="w-4 h-4 mr-2" />
                      Modifier Template
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Template Preview */}
            <Card className="bg-white/98 backdrop-blur-2xl shadow-lg border border-white/20 mobile-card animate-slide-up-from-bottom" style={{ animationDelay: '100ms' }}>
              <CardHeader className="mobile-padding pb-4">
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-institutional-primary" />
                  Aperçu du Template de Contrat
                </CardTitle>
              </CardHeader>

              <CardContent className="mobile-padding pt-0">
                <ScrollArea className={`w-full border mobile-card ${isMobile ? 'h-[250px]' : 'h-[400px]'}`}>
                  <div className="p-4">
                    <pre className="text-sm whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {templateContent}
                    </pre>
                  </div>
                </ScrollArea>

                {/* Template Actions */}
                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
                    disabled={isLoading}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200 touch-target"
                    disabled={isLoading}
                  >
                    <Edit3 className="w-4 h-4 mr-2" />
                    Personnaliser
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Status Indicators */}
            {!isFormValid && (
              <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 mobile-card text-amber-800">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">
                  Veuillez sélectionner un logement et un voyageur pour continuer.
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

