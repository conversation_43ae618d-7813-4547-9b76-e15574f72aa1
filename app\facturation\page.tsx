import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'

import { CreditCard, Plus, Download, Eye, Euro, Calendar } from 'lucide-react'

export default function FacturationPage() {
  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          <DashboardSidebar />
          <div className="flex-1 bg-gray-50 min-h-full logo-background-content">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-6">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Facturation et Paiements
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos factures et suivez vos paiements
                  </p>
                </div>
                <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Nouvelle Facture
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container py-6 space-y-6">
              {/* Financial Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Revenus ce mois</CardTitle>
                    <Euro className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">€2,640</div>
                    <p className="text-xs text-muted-foreground">+12% par rapport au mois dernier</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Factures en attente</CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">5</div>
                    <p className="text-xs text-muted-foreground">€850 au total</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Taux de paiement</CardTitle>
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">94%</div>
                    <p className="text-xs text-muted-foreground">Paiements à temps</p>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Invoices */}
              <Card>
                <CardHeader>
                  <CardTitle>Factures Récentes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Facture #2024-001</h3>
                        <p className="text-sm text-gray-600">Appartement A1 - Jean Dupont</p>
                        <p className="text-sm text-gray-500">15 Juin 2024</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">€320</div>
                        <span className="inline-block px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
                          Payée
                        </span>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Facture #2024-002</h3>
                        <p className="text-sm text-gray-600">Studio B2 - Marie Martin</p>
                        <p className="text-sm text-gray-500">18 Juin 2024</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">€180</div>
                        <span className="inline-block px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded">
                          En attente
                        </span>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Facture #2024-003</h3>
                        <p className="text-sm text-gray-600">Villa C3 - Pierre Durand</p>
                        <p className="text-sm text-gray-500">20 Juin 2024</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">€450</div>
                        <span className="inline-block px-2 py-1 text-xs bg-red-100 text-red-700 rounded">
                          En retard
                        </span>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      <MobileBottomNav />
    </div>
  )
}
