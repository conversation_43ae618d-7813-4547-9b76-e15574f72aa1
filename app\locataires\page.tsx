'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { PageWrapper } from '@/components/PageWrapper'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  Users, Search, Filter, Plus, Phone, Mail, MapPin, Calendar,
  MoreHorizontal, Edit, Trash2, Eye, UserCheck, UserX
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Mock data for tenants
const mockTenants = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+33 6 12 34 56 78",
    property: "Appartement A1",
    checkIn: "2024-01-15",
    checkOut: "2024-01-22",
    status: "active",
    nationality: "Française"
  },
  {
    id: 2,
    name: "Maria Garcia",
    email: "<EMAIL>",
    phone: "+34 6 98 76 54 32",
    property: "Studio B2",
    checkIn: "2024-01-18",
    checkOut: "2024-01-25",
    status: "pending",
    nationality: "Espagnole"
  },
  {
    id: 3,
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+44 7 12 34 56 78",
    property: "Appartement C3",
    checkIn: "2024-01-10",
    checkOut: "2024-01-17",
    status: "completed",
    nationality: "Britannique"
  }
]

export default function LocatairesPage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const filteredTenants = mockTenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.property.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Actif</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">En attente</Badge>
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Terminé</Badge>
      default:
        return <Badge variant="outline">Inconnu</Badge>
    }
  }

  return (
    <PageWrapper
      forceUserType="host"
      className="min-h-screen bg-gray-50 logo-background"
      showMobileActions={true}
    >
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Gestion des Locataires
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos locataires et leurs séjours
                  </p>
                </div>
                <Button 
                  className="bg-institutional-primary hover:bg-green-600 text-white"
                  onClick={() => router.push('/ajouter-locataire')}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter Locataire
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-4 mobile-gap">
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">12</p>
                        <p className="text-sm text-gray-600">Total</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <UserCheck className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">8</p>
                        <p className="text-sm text-gray-600">Actifs</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">3</p>
                        <p className="text-sm text-gray-600">En attente</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <UserX className="w-5 h-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">1</p>
                        <p className="text-sm text-gray-600">Terminés</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Filters and Search */}
              <Card className="enhanced-card">
                <CardContent className="p-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Rechercher par nom, email ou propriété..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant={statusFilter === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setStatusFilter('all')}
                      >
                        Tous
                      </Button>
                      <Button
                        variant={statusFilter === 'active' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setStatusFilter('active')}
                      >
                        Actifs
                      </Button>
                      <Button
                        variant={statusFilter === 'pending' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setStatusFilter('pending')}
                      >
                        En attente
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tenants Table */}
              <Card className="enhanced-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Liste des Locataires ({filteredTenants.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Locataire</TableHead>
                          <TableHead>Propriété</TableHead>
                          <TableHead>Séjour</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTenants.map((tenant) => (
                          <TableRow key={tenant.id}>
                            <TableCell>
                              <div>
                                <p className="font-medium text-gray-900">{tenant.name}</p>
                                <p className="text-sm text-gray-500">{tenant.email}</p>
                                <p className="text-sm text-gray-500">{tenant.phone}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="font-medium text-gray-900">{tenant.property}</p>
                                <p className="text-sm text-gray-500">{tenant.nationality}</p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="text-sm text-gray-900">
                                  Du {new Date(tenant.checkIn).toLocaleDateString('fr-FR')}
                                </p>
                                <p className="text-sm text-gray-900">
                                  Au {new Date(tenant.checkOut).toLocaleDateString('fr-FR')}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(tenant.status)}
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="w-4 h-4 mr-2" />
                                    Voir détails
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="w-4 h-4 mr-2" />
                                    Modifier
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-red-600">
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Supprimer
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </PageWrapper>
  )
}
