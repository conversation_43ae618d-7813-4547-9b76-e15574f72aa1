'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Image from 'next/image'
import { ArrowLeft, Bed, Users, MapPin, Home } from 'lucide-react'
import { useRouter } from 'next/navigation'

// Mock data for a single lodging
const mockLodging = {
  id: '1',
  name: "Villa Sunset",
  address: "123 Beach Road",
  city: "Nice",
  image: "/placeholder.svg?height=300&width=400",
  description: "A beautiful villa overlooking the Mediterranean Sea. Perfect for family vacations.",
  bedrooms: 4,
  capacity: 8,
  amenities: ["Wi-Fi", "Pool", "Air Conditioning", "Kitchen", "Parking"],
}

interface ViewLodgingProps {
  id: string
}

export function ViewLodging({ id }: ViewLodgingProps) {
  const [lodging, setLodging] = useState(mockLodging)
  const router = useRouter()

  useEffect(() => {
    // In a real application, you would fetch the lodging data here
    // For now, we're using mock data
    setLodging({ ...mockLodging, id })
  }, [id])

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Retour
          </Button>
          <CardTitle>{lodging.name}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <Image
          src={lodging.image}
          alt={lodging.name}
          width={400}
          height={300}
          className="w-full h-64 object-cover rounded-lg"
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center">
              <MapPin className="mr-2 h-4 w-4" />
              <span>{lodging.address}, {lodging.city}</span>
            </div>
            <div className="flex items-center">
              <Bed className="mr-2 h-4 w-4" />
              <span>{lodging.bedrooms} chambres</span>
            </div>
            <div className="flex items-center">
              <Users className="mr-2 h-4 w-4" />
              <span>Capacité: {lodging.capacity} personnes</span>
            </div>
          </div>
          <div>
            <h3 className="font-semibold mb-2">Description</h3>
            <p>{lodging.description}</p>
          </div>
        </div>
        <div>
          <h3 className="font-semibold mb-2">Équipements</h3>
          <div className="flex flex-wrap gap-2">
            {lodging.amenities.map((amenity, index) => (
              <span key={index} className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm">
                {amenity}
              </span>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

