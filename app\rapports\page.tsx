import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'

import { BarChart3, FileText, Download, Calendar, TrendingUp } from 'lucide-react'

export default function RapportsPage() {
  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          <DashboardSidebar />
          <div className="flex-1 bg-gray-50 min-h-full logo-background-content">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-6">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Rapports et Analyses
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Consultez vos rapports de performance et analyses
                  </p>
                </div>
                <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                  <Download className="w-4 h-4 mr-2" />
                  Exporter
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container py-6 space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Déclarations ce mois</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">45</div>
                    <p className="text-xs text-muted-foreground">+12% par rapport au mois dernier</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Revenus générés</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">€2,640</div>
                    <p className="text-xs text-muted-foreground">+8% par rapport au mois dernier</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Taux d'occupation</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">87%</div>
                    <p className="text-xs text-muted-foreground">+5% par rapport au mois dernier</p>
                  </CardContent>
                </Card>
              </div>

              {/* Reports List */}
              <Card>
                <CardHeader>
                  <CardTitle>Rapports Disponibles</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Rapport Mensuel</h3>
                        <p className="text-sm text-gray-600">Synthèse complète du mois en cours</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Télécharger
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Analyse des Revenus</h3>
                        <p className="text-sm text-gray-600">Détail des revenus par logement</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Télécharger
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Rapport d'Occupation</h3>
                        <p className="text-sm text-gray-600">Statistiques d'occupation des logements</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Télécharger
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      <MobileBottomNav />
    </div>
  )
}
