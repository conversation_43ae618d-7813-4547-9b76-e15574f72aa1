'use client'

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const revParData = [
  { month: 'Dec 2023', value: 150 * 11 },
  { month: 'Jan 2024', value: 145 * 11 },
  { month: 'Feb 2024', value: 135 * 11 },
  { month: 'Mar 2024', value: 120 * 11 },
  { month: 'Apr 2024', value: 115 * 11 },
  { month: 'May 2024', value: 110 * 11 },
  { month: 'Jun 2024', value: 105 * 11 },
  { month: 'Jul 2024', value: 100 * 11 },
  { month: 'Aug 2024', value: 115 * 11 },
  { month: 'Sep 2024', value: 125 * 11 },
  { month: 'Oct 2024', value: 120 * 11 },
  { month: 'Nov 2024', value: 140 * 11 },
]

const months = ['January', 'February', 'March', 'April', 'May', 'June', 
              'July', 'August', 'September', 'October', 'November', 'December']
const days = ['D', 'L', 'M', 'M', 'J', 'V', 'S']

type CalendarDay = {
  value: number
  date: Date
}

const generateCalendarData = () => {
  const data: CalendarDay[][] = []
  const currentDate = new Date()

  for (let month = 0; month < 12; month++) {
    const monthData: CalendarDay[] = []
    const date = new Date(currentDate.getFullYear(), month, 1)
    
    // Add empty cells for days before the first of the month
    const firstDay = date.getDay()
    for (let i = 0; i < firstDay; i++) {
      monthData.push({ value: 0, date: new Date(0) })
    }
    
    // Add days of the month
    while (date.getMonth() === month) {
      monthData.push({
        value: Math.random() * 100, // Mock RevPAR value
        date: new Date(date)
      })
      date.setDate(date.getDate() + 1)
    }
    
    data.push(monthData)
  }

  return data
}

export function RevPARDashboard() {
  const calendarData = generateCalendarData()

  return (
    <div className="space-y-6">
      {/* RevPAR Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">RevPAR</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline justify-between">
              <div className="text-2xl font-bold">804.6 MAD</div>
              <div className="text-sm text-red-500">-1%</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Weekend RevPAR</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">--</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Meilleur Mois</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Janvier</div>
          </CardContent>
        </Card>
      </div>

      {/* RevPAR Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Évolution du RevPAR</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              revpar: {
                label: "RevPAR",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revParData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tickFormatter={(value) => value.split(' ')[0]}
                />
                <YAxis domain={[90 * 11, 160 * 11]} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="var(--color-revpar)"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* RevPAR Calendar Heatmap */}
      <Card>
        <CardHeader>
          <CardTitle>Distribution du RevPAR par Jour</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 md:grid-cols-4 gap-6">
            {calendarData.map((month, monthIndex) => (
              <div key={monthIndex} className="space-y-2">
                <div className="text-sm font-medium">{months[monthIndex]}</div>
                <div className="grid grid-cols-7 gap-1">
                  {/* Day labels */}
                  {days.map((day, i) => (
                    <div key={i} className="text-xs text-center text-muted-foreground">
                      {day}
                    </div>
                  ))}
                  {/* Calendar days */}
                  {month.map((day, dayIndex) => (
                    <div
                      key={dayIndex}
                      className={`aspect-square rounded-sm ${
                        day.value === 0 
                          ? 'invisible'
                          : 'bg-primary/10 hover:bg-primary/20 transition-colors'
                      }`}
                      style={{
                        opacity: day.value === 0 ? 0 : 0.3 + (day.value / 100) * 0.7,
                      }}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

