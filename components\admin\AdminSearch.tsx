'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, Filter, MapPin, Building2, Users, FileText, 
  Eye, Phone, Mail, Calendar, Clock, User
} from 'lucide-react'
import { adminHosts, adminAccommodations, adminDeclarations, moroccanCities } from '@/utils/mockData'
import { AdminHost, AdminAccommodation, AdminDeclaration } from '@/types/admin'

interface SearchResult {
  type: 'host' | 'accommodation' | 'declaration'
  id: string | number
  title: string
  subtitle: string
  details: string
  data: AdminHost | AdminAccommodation | AdminDeclaration
  coordinates?: { lat: number; lng: number }
}

interface AdminSearchProps {
  onViewDetails?: (item: any, type: 'host' | 'accommodation' | 'declaration') => void
  initialCityFilter?: string
  onCityFilterChange?: (city: string) => void
}

export function AdminSearch({ onViewDetails, initialCityFilter, onCityFilterChange }: AdminSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCity, setSelectedCity] = useState<string>(initialCityFilter || 'all')
  const [selectedType, setSelectedType] = useState<string>('all')

  // Update city filter when initialCityFilter changes
  useEffect(() => {
    if (initialCityFilter) {
      setSelectedCity(initialCityFilter)
    }
  }, [initialCityFilter])
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null)

  const performSearch = () => {
    if (!searchQuery.trim() && selectedCity === 'all' && selectedType === 'all') {
      setResults([])
      return
    }

    setIsSearching(true)

    const searchResults: SearchResult[] = []
    const query = searchQuery.toLowerCase()

    // Search hosts
    if (selectedType === 'all' || selectedType === 'host') {
      adminHosts.forEach(host => {
        const matchesQuery = !query || 
          host.nom.toLowerCase().includes(query) ||
          host.prenom.toLowerCase().includes(query) ||
          host.email.toLowerCase().includes(query) ||
          host.cin.toLowerCase().includes(query) ||
          host.ice.toLowerCase().includes(query) ||
          host.telephone.includes(query) ||
          host.adresse.toLowerCase().includes(query)

        const matchesCity = selectedCity === 'all' || host.ville === selectedCity

        if (matchesQuery && matchesCity) {
          searchResults.push({
            type: 'host',
            id: host.id,
            title: `${host.prenom} ${host.nom}`,
            subtitle: `Hôte - ${host.ville}`,
            details: `${host.nombreLogements} logements • ${host.totalDeclarations} déclarations`,
            data: host,
            coordinates: host.coordinates
          })
        }
      })
    }

    // Search accommodations
    if (selectedType === 'all' || selectedType === 'accommodation') {
      adminAccommodations.forEach(acc => {
        const matchesQuery = !query ||
          acc.nom.toLowerCase().includes(query) ||
          acc.type.toLowerCase().includes(query) ||
          acc.codeLogement.toLowerCase().includes(query) ||
          acc.adresse.toLowerCase().includes(query)

        const matchesCity = selectedCity === 'all' || acc.ville === selectedCity

        if (matchesQuery && matchesCity) {
          searchResults.push({
            type: 'accommodation',
            id: acc.id,
            title: acc.nom,
            subtitle: `${acc.type} - ${acc.ville}`,
            details: `${acc.capacite} personnes • ${acc.statut} • ${acc.tarif} MAD/nuit`,
            data: acc,
            coordinates: acc.coordinates
          })
        }
      })
    }

    // Search declarations
    if (selectedType === 'all' || selectedType === 'declaration') {
      adminDeclarations.forEach(decl => {
        const matchesQuery = !query ||
          decl.nom.toLowerCase().includes(query) ||
          decl.prenom.toLowerCase().includes(query) ||
          decl.numeroPieceIdentite.toLowerCase().includes(query) ||
          decl.telephone.includes(query) ||
          decl.email.toLowerCase().includes(query) ||
          decl.guestId.toLowerCase().includes(query)

        const matchesCity = selectedCity === 'all' || decl.ville === selectedCity

        if (matchesQuery && matchesCity) {
          searchResults.push({
            type: 'declaration',
            id: decl.id,
            title: `${decl.prenom} ${decl.nom}`,
            subtitle: `Déclaration - ${decl.ville}`,
            details: `${decl.nomLogement} • ${decl.dateArrivee} - ${decl.dateDepart}`,
            data: decl,
            coordinates: decl.coordinates
          })
        }
      })
    }

    setResults(searchResults.slice(0, 50)) // Limit to 50 results
    setIsSearching(false)
  }

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch()
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery, selectedCity, selectedType])

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'host': return Users
      case 'accommodation': return Building2
      case 'declaration': return FileText
      default: return Search
    }
  }

  const getResultColor = (type: string) => {
    switch (type) {
      case 'host': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'accommodation': return 'bg-green-50 text-green-700 border-green-200'
      case 'declaration': return 'bg-purple-50 text-purple-700 border-purple-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Controls */}
      <Card className="bg-white border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5 text-institutional-primary" />
            Recherche Avancée
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <Input
                placeholder="Rechercher par nom, CIN, ICE, téléphone, email, adresse..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={selectedCity} onValueChange={(value) => {
              setSelectedCity(value)
              onCityFilterChange?.(value)
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Toutes les villes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les villes</SelectItem>
                {moroccanCities.map(city => (
                  <SelectItem key={city.name} value={city.name}>
                    {city.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Tous les types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les types</SelectItem>
                <SelectItem value="host">Hôtes</SelectItem>
                <SelectItem value="accommodation">Logements</SelectItem>
                <SelectItem value="declaration">Déclarations</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="bg-white border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Résultats de Recherche</span>
                <Badge variant="outline">
                  {results.length} résultat{results.length !== 1 ? 's' : ''}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isSearching ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-institutional-primary mx-auto mb-2"></div>
                  <p className="text-gray-600">Recherche en cours...</p>
                </div>
              ) : results.length === 0 ? (
                <div className="text-center py-8">
                  <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    {searchQuery || selectedCity !== 'all' || selectedType !== 'all'
                      ? 'Aucun résultat trouvé'
                      : 'Saisissez un terme de recherche'}
                  </p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {results.map((result, index) => {
                    const Icon = getResultIcon(result.type)
                    return (
                      <div
                        key={`${result.type}-${result.id}-${index}`}
                        className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => setSelectedResult(result)}
                      >
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getResultColor(result.type)}`}>
                          <Icon className="w-5 h-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 truncate">{result.title}</p>
                          <p className="text-sm text-gray-600 truncate">{result.subtitle}</p>
                          <p className="text-xs text-gray-500 truncate">{result.details}</p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onViewDetails?.(result.data, result.type)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Selected Result Details */}
        <div>
          {selectedResult ? (
            <Card className="bg-white border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {(() => {
                    const Icon = getResultIcon(selectedResult.type)
                    return <Icon className="w-5 h-5 text-institutional-primary" />
                  })()}
                  Détails
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900">{selectedResult.title}</h3>
                    <p className="text-sm text-gray-600">{selectedResult.subtitle}</p>
                  </div>

                  {selectedResult.type === 'host' && (
                    <div className="space-y-3">
                      {(() => {
                        const host = selectedResult.data as AdminHost
                        return (
                          <>
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="w-4 h-4 text-gray-400" />
                              <span>{host.email}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span>{host.telephone}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <User className="w-4 h-4 text-gray-400" />
                              <span>CIN: {host.cin}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Building2 className="w-4 h-4 text-gray-400" />
                              <span>ICE: {host.ice}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span>{host.adresse}</span>
                            </div>
                          </>
                        )
                      })()}
                    </div>
                  )}

                  {selectedResult.type === 'accommodation' && (
                    <div className="space-y-3">
                      {(() => {
                        const acc = selectedResult.data as AdminAccommodation
                        return (
                          <>
                            <div className="flex items-center gap-2 text-sm">
                              <Building2 className="w-4 h-4 text-gray-400" />
                              <span>Code: {acc.codeLogement}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Users className="w-4 h-4 text-gray-400" />
                              <span>Capacité: {acc.capacite} personnes</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span>{acc.adresse}</span>
                            </div>
                            <Badge className={acc.statut === 'occupé' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}>
                              {acc.statut}
                            </Badge>
                          </>
                        )
                      })()}
                    </div>
                  )}

                  {selectedResult.type === 'declaration' && (
                    <div className="space-y-3">
                      {(() => {
                        const decl = selectedResult.data as AdminDeclaration
                        return (
                          <>
                            <div className="flex items-center gap-2 text-sm">
                              <User className="w-4 h-4 text-gray-400" />
                              <span>ID: {decl.guestId}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span>{decl.telephone}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="w-4 h-4 text-gray-400" />
                              <span>{decl.dateArrivee} - {decl.dateDepart}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Building2 className="w-4 h-4 text-gray-400" />
                              <span>{decl.nomLogement}</span>
                            </div>
                            <Badge className={
                              decl.statut === 'validée' ? 'bg-green-100 text-green-800' :
                              decl.statut === 'en_cours' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }>
                              {decl.statut}
                            </Badge>
                          </>
                        )
                      })()}
                    </div>
                  )}

                  <Button
                    className="w-full"
                    size="sm"
                    onClick={() => selectedResult && onViewDetails?.(selectedResult.data, selectedResult.type)}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Voir tous les détails
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-8 text-center">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  Sélectionnez un résultat pour voir les détails
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
