# Data Storage Directory

This directory contains all the data generated by the tenant declaration application.

## Structure

```
data/
├── declarations/          # Tele-declaration documents
│   ├── pdf/              # Generated PDF declarations
│   └── json/             # Raw declaration data
├── images/               # Captured images and documents
│   ├── id_documents/     # ID document photos (recto/verso)
│   ├── selfies/          # Guest selfie photos
│   └── signatures/       # Digital signatures
├── rental_agreements/    # Generated rental agreements
├── encrypted/           # AES encrypted data storage
└── temp/               # Temporary files during processing

## Security

- All sensitive data is encrypted using AES encryption
- Images are stored with unique identifiers
- Personal data is anonymized where possible
- Regular cleanup of temporary files

## File Naming Convention

- Declarations: `declaration_YYYYMMDD_HHMMSS_[guestId].pdf`
- Images: `[type]_YYYYMMDD_HHMMSS_[guestId].[ext]`
- Encrypted files: `encrypted_YYYYMMDD_HHMMSS_[guestId].enc`

## Backup and Retention

- Daily backups of all declaration data
- Images retained for legal compliance period
- Automatic cleanup of expired temporary files
