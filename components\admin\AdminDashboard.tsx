'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Building2, Users, FileText, MapPin, Search, Filter, Download,
  Eye, TrendingUp, AlertTriangle, Clock, CheckCircle, BarChart3,
  Home, Shield, Globe, Calendar, Activity, Settings, Bell, User, Phone
} from 'lucide-react'
import { AdminStatsCards } from './AdminStatsCards'
import { AdminMap } from './AdminMap'
import { AdminSearch } from './AdminSearch'
import { AdminHostRankings } from './AdminHostRankings'
import { AdminCityStats } from './AdminCityStats'
import { AdminRecentActivity } from './AdminRecentActivity'
import { AdminDeclarationsList } from './AdminDeclarationsList'
import { useAdminData } from '@/hooks/useAdminData'

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showNotifications, setShowNotifications] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [selectedItemType, setSelectedItemType] = useState<'host' | 'accommodation' | 'declaration' | null>(null)
  const [searchCityFilter, setSearchCityFilter] = useState<string>('')
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)
  const [showTravelHistory, setShowTravelHistory] = useState(false)
  const [showIncidentReport, setShowIncidentReport] = useState(false)
  const [incidentForm, setIncidentForm] = useState({
    guestId: '',
    incidentType: '',
    description: '',
    severity: 'low',
    location: '',
    reportedBy: ''
  })
  const { dashboardStats, isLoading, adminHosts, adminAccommodations, adminDeclarations } = useAdminData()

  // Update time every minute
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Investigation tool handlers
  const handleAdvancedSearch = () => {
    setShowAdvancedSearch(true)
  }

  const handleExportReport = () => {
    // Generate comprehensive security report
    const reportData = {
      timestamp: new Date().toISOString(),
      totalDeclarations: adminDeclarations.length,
      watchListedGuests: adminDeclarations.filter(d => d.securityFlags?.isWatchListed).length,
      highRiskGuests: adminDeclarations.filter(d =>
        d.securityFlags?.riskLevel === 'high' || d.securityFlags?.riskLevel === 'critical'
      ).length,
      pendingVerifications: adminDeclarations.filter(d => d.verificationStatut === 'en_attente').length,
      recentIncidents: adminDeclarations.filter(d =>
        d.investigationNotes?.some(note => note.category === 'suspicious')
      ).length
    }

    // Create downloadable report
    const reportContent = `RAPPORT DE SÉCURITÉ - TÉLÉDÉCLARATIONS
Généré le: ${new Date().toLocaleString('fr-FR')}

STATISTIQUES GÉNÉRALES:
- Total des déclarations: ${reportData.totalDeclarations}
- Individus surveillés: ${reportData.watchListedGuests}
- Invités à risque élevé: ${reportData.highRiskGuests}
- Vérifications en attente: ${reportData.pendingVerifications}
- Incidents récents: ${reportData.recentIncidents}

DÉTAILS DES INDIVIDUS SURVEILLÉS:
${adminDeclarations
  .filter(d => d.securityFlags?.isWatchListed)
  .map(d => `- ${d.prenom} ${d.nom} (${d.guestId}) - Risque: ${d.securityFlags.riskLevel}`)
  .join('\n')}

VÉRIFICATIONS EN ATTENTE:
${adminDeclarations
  .filter(d => d.verificationStatut === 'en_attente')
  .map(d => `- ${d.prenom} ${d.nom} (${d.guestId}) - ${d.ville}`)
  .join('\n')}
`

    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `rapport-securite-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleTravelHistory = () => {
    setShowTravelHistory(true)
  }

  const handleIncidentReport = () => {
    setShowIncidentReport(true)
  }

  const submitIncidentReport = () => {
    // In production, this would send to backend
    console.log('Incident Report Submitted:', incidentForm)
    alert('Rapport d\'incident soumis avec succès')
    setShowIncidentReport(false)
    setIncidentForm({
      guestId: '',
      incidentType: '',
      description: '',
      severity: 'low',
      location: '',
      reportedBy: ''
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-institutional-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du tableau de bord...</p>
        </div>
      </div>
    )
  }

  return React.createElement('div', { className: "w-full logo-background" },
    React.createElement('div', { className: "bg-white border-b border-gray-100" },
      React.createElement('div', { className: "container mx-auto px-4 py-3" },
        React.createElement('div', { className: "flex items-center justify-between" },
          React.createElement('div', { className: "flex items-center gap-6" },
            React.createElement('div', { className: "flex items-center gap-3" },
              React.createElement('img', {
                src: "/MINISTERE INTERIEUR.png",
                alt: "Ministère de l'Intérieur",
                width: 60,
                height: 60,
                className: "object-contain"
              }),
              React.createElement('div', { className: "hidden md:block" },
                React.createElement('p', { className: "text-sm font-semibold text-gray-900" }, "ROYAUME DU MAROC"),
                React.createElement('p', { className: "text-xs text-gray-600" }, "Ministère de l'Intérieur")
              )
            ),
            React.createElement('div', { className: "hidden lg:flex items-center gap-4" },
              React.createElement('img', {
                src: "/Patch_of_the_DGSN - Copie.png",
                alt: "DGSN",
                width: 50,
                height: 50,
                className: "object-contain"
              }),
              React.createElement('img', {
                src: "/Royal_Moroccan_Gendarmerie.png",
                alt: "Gendarmerie Royale",
                width: 50,
                height: 50,
                className: "object-contain"
              })
            )
          ),
          React.createElement('div', { className: "text-center" },
            React.createElement('h1', { className: "text-lg md:text-xl font-bold text-institutional-primary" }, "ADMINISTRATION"),
            React.createElement('p', { className: "text-xs md:text-sm text-gray-600" }, "Système de Déclaration des Locataires")
          ),
          React.createElement('div', { className: "flex items-center gap-3" },
            React.createElement('div', { className: "text-right hidden md:block" },
              React.createElement('p', { className: "text-sm font-medium text-gray-900" },
                currentTime.toLocaleDateString('fr-FR', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })
              ),
              React.createElement('p', { className: "text-xs text-gray-600" },
                currentTime.toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })
              )
            ),
            React.createElement(Badge, { variant: "outline", className: "bg-green-50 text-green-700 border-green-200" },
              React.createElement('div', { className: "w-2 h-2 bg-green-500 rounded-full mr-2" }),
              React.createElement('span', { className: "hidden sm:inline" }, "Système "),
              "Opérationnel"
            )
          )
        )
      )
    ),

    React.createElement('header', { className: "bg-white border-b border-gray-200 shadow-sm" },
      React.createElement('div', { className: "container mx-auto px-4" },
        React.createElement('div', { className: "flex items-center justify-between h-16" },
          React.createElement('div', { className: "flex items-center gap-4" },
            React.createElement('div', { className: "flex items-center gap-3" },
              React.createElement('div', { className: "w-10 h-10 bg-institutional-primary rounded-lg flex items-center justify-center" },
                React.createElement(Shield, { className: "w-6 h-6 text-white" })
              ),
              React.createElement('div', null,
                React.createElement('h2', { className: "text-lg font-semibold text-gray-900" }, "Tableau de Bord"),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Interface d'administration")
              )
            )
          ),
          React.createElement('div', { className: "flex items-center gap-3" },
            React.createElement('div', { className: "relative" },
              React.createElement(Button, {
                variant: "ghost",
                size: "icon",
                className: "relative",
                onClick: () => setShowNotifications(!showNotifications)
              },
                React.createElement(Bell, { className: "w-5 h-5" }),
                React.createElement(Badge, { className: "absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs" }, "3")
              ),
              showNotifications && React.createElement('div', {
                className: "absolute right-0 top-12 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4"
              },
                React.createElement('h3', { className: "font-semibold text-gray-900 mb-3" }, "Notifications"),
                React.createElement('div', { className: "space-y-3" },
                  React.createElement('div', { className: "flex items-start gap-3 p-2 hover:bg-gray-50 rounded" },
                    React.createElement('div', { className: "w-2 h-2 bg-red-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium" }, "Nouvelle déclaration en attente"),
                      React.createElement('p', { className: "text-xs text-gray-600" }, "Il y a 5 minutes")
                    )
                  ),
                  React.createElement('div', { className: "flex items-start gap-3 p-2 hover:bg-gray-50 rounded" },
                    React.createElement('div', { className: "w-2 h-2 bg-yellow-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium" }, "Vérification document requise"),
                      React.createElement('p', { className: "text-xs text-gray-600" }, "Il y a 15 minutes")
                    )
                  ),
                  React.createElement('div', { className: "flex items-start gap-3 p-2 hover:bg-gray-50 rounded" },
                    React.createElement('div', { className: "w-2 h-2 bg-blue-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium" }, "Nouveau hôte inscrit"),
                      React.createElement('p', { className: "text-xs text-gray-600" }, "Il y a 1 heure")
                    )
                  )
                ),
                React.createElement('div', { className: "mt-3 pt-3 border-t" },
                  React.createElement(Button, { variant: "outline", size: "sm", className: "w-full" }, "Voir toutes les notifications")
                )
              )
            ),
            React.createElement('div', { className: "relative" },
              React.createElement(Button, {
                variant: "ghost",
                size: "icon",
                onClick: () => setShowSettings(!showSettings)
              },
                React.createElement(Settings, { className: "w-5 h-5" })
              ),
              showSettings && React.createElement('div', {
                className: "absolute right-0 top-12 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4"
              },
                React.createElement('h3', { className: "font-semibold text-gray-900 mb-3" }, "Paramètres"),
                React.createElement('div', { className: "space-y-2" },
                  React.createElement(Button, { variant: "ghost", className: "w-full justify-start text-sm" }, "Préférences d'affichage"),
                  React.createElement(Button, { variant: "ghost", className: "w-full justify-start text-sm" }, "Gestion des utilisateurs"),
                  React.createElement(Button, { variant: "ghost", className: "w-full justify-start text-sm" }, "Configuration système"),
                  React.createElement(Button, { variant: "ghost", className: "w-full justify-start text-sm" }, "Rapports et exports"),
                  React.createElement('hr', { className: "my-2" }),
                  React.createElement(Button, { variant: "ghost", className: "w-full justify-start text-sm text-red-600" }, "Déconnexion")
                )
              )
            ),
            React.createElement(Button, { variant: "ghost", className: "flex items-center gap-2" },
              React.createElement('div', { className: "w-8 h-8 bg-institutional-primary rounded-full flex items-center justify-center" },
                React.createElement(User, { className: "w-4 h-4 text-white" })
              ),
              React.createElement('span', { className: "hidden md:block text-sm font-medium" }, "Administrateur")
            )
          )
        )
      )
    ),

    React.createElement('div', { className: "container mx-auto px-4 py-6 space-y-6" },
      React.createElement(AdminStatsCards, { stats: dashboardStats }),
      React.createElement(Tabs, { value: activeTab, onValueChange: setActiveTab, className: "space-y-6" },
        React.createElement(TabsList, { className: "grid w-full grid-cols-7 bg-white border border-gray-200 rounded-lg p-1" },
          React.createElement(TabsTrigger, { value: "overview", className: "flex items-center gap-2" },
            React.createElement(Home, { className: "w-4 h-4" }),
            "Vue d'ensemble"
          ),
          React.createElement(TabsTrigger, { value: "map", className: "flex items-center gap-2" },
            React.createElement(MapPin, { className: "w-4 h-4" }),
            "Cartographie"
          ),
          React.createElement(TabsTrigger, { value: "search", className: "flex items-center gap-2" },
            React.createElement(Search, { className: "w-4 h-4" }),
            "Recherche"
          ),
          React.createElement(TabsTrigger, { value: "hosts", className: "flex items-center gap-2" },
            React.createElement(Users, { className: "w-4 h-4" }),
            "Hôtes"
          ),
          React.createElement(TabsTrigger, { value: "declarations", className: "flex items-center gap-2" },
            React.createElement(FileText, { className: "w-4 h-4" }),
            "Déclarations"
          ),
          React.createElement(TabsTrigger, { value: "analytics", className: "flex items-center gap-2" },
            React.createElement(BarChart3, { className: "w-4 h-4" }),
            "Analyses"
          ),
          React.createElement(TabsTrigger, { value: "security", className: "flex items-center gap-2" },
            React.createElement(Shield, { className: "w-4 h-4" }),
            "Sécurité"
          )
        ),

        React.createElement(TabsContent, { value: "overview", className: "space-y-6" },
          React.createElement('div', { className: "grid grid-cols-1 lg:grid-cols-3 gap-6" },
            React.createElement('div', { className: "lg:col-span-2" },
              React.createElement(AdminCityStats, {
                cities: dashboardStats.topCities,
                onViewCityDetails: (city: any) => {
                  // Switch to search tab and filter by city
                  setSearchCityFilter(city.ville)
                  setActiveTab('search')
                }
              })
            ),
            React.createElement('div', null,
              React.createElement(AdminRecentActivity, {
                declarations: dashboardStats.recentDeclarations,
                onViewDetails: (declaration: any) => {
                  setSelectedItem(declaration)
                  setSelectedItemType('declaration')
                },
                onViewAllDeclarations: () => {
                  setActiveTab('declarations')
                }
              })
            )
          ),
          React.createElement(AdminHostRankings)
        ),
        React.createElement(TabsContent, { value: "map", className: "space-y-6" },
          React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
            React.createElement(CardHeader, null,
              React.createElement(CardTitle, { className: "flex items-center gap-2" },
                React.createElement(MapPin, { className: "w-5 h-5 text-institutional-primary" }),
                "Cartographie des Logements et Déclarations"
              )
            ),
            React.createElement(CardContent, null,
              React.createElement(AdminMap)
            )
          )
        ),
        React.createElement(TabsContent, { value: "search", className: "space-y-6" },
          React.createElement(AdminSearch, {
            onViewDetails: (item: any, type: 'host' | 'accommodation' | 'declaration') => {
              setSelectedItem(item)
              setSelectedItemType(type)
            },
            initialCityFilter: searchCityFilter,
            onCityFilterChange: (city: string) => setSearchCityFilter(city)
          })
        ),
        React.createElement(TabsContent, { value: "hosts", className: "space-y-6" },
          React.createElement(AdminHostRankings, {
            showFilters: true,
            onViewDetails: (host: any) => {
              setSelectedItem(host)
              setSelectedItemType('host')
            }
          })
        ),
        React.createElement(TabsContent, { value: "declarations", className: "space-y-6" },
          React.createElement(AdminDeclarationsList, {
            onViewDetails: (declaration: any) => {
              setSelectedItem(declaration)
              setSelectedItemType('declaration')
            }
          })
        ),

        // Security Analytics Tab
        React.createElement(TabsContent, { value: "security", className: "space-y-6" },
          React.createElement('div', { className: "grid grid-cols-1 lg:grid-cols-3 gap-6" },
            // Security Alerts
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2 text-red-700" },
                  React.createElement(AlertTriangle, { className: "w-5 h-5" }),
                  "Alertes Sécurité"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-3" },
                  React.createElement('div', { className: "flex items-center justify-between p-3 bg-red-50 rounded-lg" },
                    React.createElement('div', null,
                      React.createElement('p', { className: "font-medium text-red-800" }, "Individus surveillés"),
                      React.createElement('p', { className: "text-sm text-red-600" }, "Actuellement présents")
                    ),
                    React.createElement('span', { className: "text-2xl font-bold text-red-700" },
                      dashboardStats.recentDeclarations.filter((d: any) => d.securityFlags?.isWatchListed).length
                    )
                  ),
                  React.createElement('div', { className: "flex items-center justify-between p-3 bg-yellow-50 rounded-lg" },
                    React.createElement('div', null,
                      React.createElement('p', { className: "font-medium text-yellow-800" }, "Vérifications en attente"),
                      React.createElement('p', { className: "text-sm text-yellow-600" }, "Documents à vérifier")
                    ),
                    React.createElement('span', { className: "text-2xl font-bold text-yellow-700" },
                      dashboardStats.recentDeclarations.filter((d: any) => d.verificationStatut === 'en_attente').length
                    )
                  ),
                  React.createElement('div', { className: "flex items-center justify-between p-3 bg-orange-50 rounded-lg" },
                    React.createElement('div', null,
                      React.createElement('p', { className: "font-medium text-orange-800" }, "Risque élevé"),
                      React.createElement('p', { className: "text-sm text-orange-600" }, "Niveau de risque > medium")
                    ),
                    React.createElement('span', { className: "text-2xl font-bold text-orange-700" },
                      dashboardStats.recentDeclarations.filter((d: any) =>
                        d.securityFlags?.riskLevel === 'high' || d.securityFlags?.riskLevel === 'critical'
                      ).length
                    )
                  )
                )
              )
            ),

            // Document Verification Status
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(Shield, { className: "w-5 h-5 text-institutional-primary" }),
                  "Vérification Documents"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-4" },
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Photos ID vérifiées"),
                    React.createElement('span', { className: "text-lg font-bold text-green-600" }, "94%")
                  ),
                  React.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2" },
                    React.createElement('div', { className: "bg-green-600 h-2 rounded-full", style: { width: '94%' } })
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Signatures vérifiées"),
                    React.createElement('span', { className: "text-lg font-bold text-blue-600" }, "89%")
                  ),
                  React.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2" },
                    React.createElement('div', { className: "bg-blue-600 h-2 rounded-full", style: { width: '89%' } })
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Biométrie disponible"),
                    React.createElement('span', { className: "text-lg font-bold text-purple-600" }, "67%")
                  ),
                  React.createElement('div', { className: "w-full bg-gray-200 rounded-full h-2" },
                    React.createElement('div', { className: "bg-purple-600 h-2 rounded-full", style: { width: '67%' } })
                  )
                )
              )
            ),

            // Risk Level Distribution
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(BarChart3, { className: "w-5 h-5 text-institutional-primary" }),
                  "Niveaux de Risque"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-3" },
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('div', { className: "flex items-center gap-2" },
                      React.createElement('div', { className: "w-3 h-3 bg-green-500 rounded-full" }),
                      React.createElement('span', { className: "text-sm" }, "Faible")
                    ),
                    React.createElement('span', { className: "font-bold" }, "87%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('div', { className: "flex items-center gap-2" },
                      React.createElement('div', { className: "w-3 h-3 bg-yellow-500 rounded-full" }),
                      React.createElement('span', { className: "text-sm" }, "Moyen")
                    ),
                    React.createElement('span', { className: "font-bold" }, "9%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('div', { className: "flex items-center gap-2" },
                      React.createElement('div', { className: "w-3 h-3 bg-orange-500 rounded-full" }),
                      React.createElement('span', { className: "text-sm" }, "Élevé")
                    ),
                    React.createElement('span', { className: "font-bold" }, "3%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('div', { className: "flex items-center gap-2" },
                      React.createElement('div', { className: "w-3 h-3 bg-red-500 rounded-full" }),
                      React.createElement('span', { className: "text-sm" }, "Critique")
                    ),
                    React.createElement('span', { className: "font-bold" }, "1%")
                  )
                )
              )
            )
          ),

          // Watch List and Investigation Tools
          React.createElement('div', { className: "grid grid-cols-1 lg:grid-cols-2 gap-6" },
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(Eye, { className: "w-5 h-5 text-institutional-primary" }),
                  "Liste de Surveillance"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-3" },
                  dashboardStats.recentDeclarations
                    .filter((d: any) => d.securityFlags?.isWatchListed)
                    .slice(0, 5)
                    .map((declaration: any, index: number) =>
                      React.createElement('div', {
                        key: index,
                        className: "flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",
                        onClick: () => {
                          setSelectedItem(declaration)
                          setSelectedItemType('declaration')
                        }
                      },
                        React.createElement('div', null,
                          React.createElement('p', { className: "font-medium" }, `${declaration.prenom} ${declaration.nom}`),
                          React.createElement('p', { className: "text-sm text-gray-600" }, declaration.guestId),
                          React.createElement('p', { className: "text-xs text-red-600" },
                            `Risque: ${declaration.securityFlags.riskLevel}`
                          )
                        ),
                        React.createElement(Button, { variant: "ghost", size: "sm" },
                          React.createElement(Eye, { className: "w-4 h-4" })
                        )
                      )
                    )
                )
              )
            ),

            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(FileText, { className: "w-5 h-5 text-institutional-primary" }),
                  "Outils d'Enquête"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-3" },
                  React.createElement(Button, {
                    className: "w-full justify-start",
                    variant: "outline",
                    onClick: handleAdvancedSearch
                  },
                    React.createElement(Search, { className: "w-4 h-4 mr-2" }),
                    "Recherche Avancée"
                  ),
                  React.createElement(Button, {
                    className: "w-full justify-start",
                    variant: "outline",
                    onClick: handleExportReport
                  },
                    React.createElement(Download, { className: "w-4 h-4 mr-2" }),
                    "Exporter Rapport"
                  ),
                  React.createElement(Button, {
                    className: "w-full justify-start",
                    variant: "outline",
                    onClick: handleTravelHistory
                  },
                    React.createElement(Globe, { className: "w-4 h-4 mr-2" }),
                    "Historique Voyages"
                  ),
                  React.createElement(Button, {
                    className: "w-full justify-start",
                    variant: "outline",
                    onClick: handleIncidentReport
                  },
                    React.createElement(AlertTriangle, { className: "w-4 h-4 mr-2" }),
                    "Signaler Incident"
                  )
                )
              )
            )
          )
        ),

        React.createElement(TabsContent, { value: "analytics", className: "space-y-6" },
          React.createElement('div', { className: "grid grid-cols-1 lg:grid-cols-2 gap-6" },
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(BarChart3, { className: "w-5 h-5 text-institutional-primary" }),
                  "Tendances des Déclarations"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "h-64" },
                  React.createElement('div', { className: "w-full h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg flex items-center justify-center" },
                    React.createElement('div', { className: "text-center" },
                      React.createElement(TrendingUp, { className: "w-12 h-12 text-institutional-primary mx-auto mb-2" }),
                      React.createElement('p', { className: "text-lg font-semibold text-gray-900" }, "+24% ce mois"),
                      React.createElement('p', { className: "text-sm text-gray-600" }, "Croissance des déclarations")
                    )
                  )
                )
              )
            ),
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(MapPin, { className: "w-5 h-5 text-institutional-primary" }),
                  "Répartition par Région"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "h-64" },
                  React.createElement('div', { className: "space-y-3" },
                    React.createElement('div', { className: "flex items-center justify-between" },
                      React.createElement('span', { className: "text-sm font-medium" }, "Casablanca-Settat"),
                      React.createElement('div', { className: "flex items-center gap-2" },
                        React.createElement('div', { className: "w-24 bg-gray-200 rounded-full h-2" },
                          React.createElement('div', { className: "bg-institutional-primary h-2 rounded-full", style: { width: '85%' } })
                        ),
                        React.createElement('span', { className: "text-sm text-gray-600" }, "85%")
                      )
                    ),
                    React.createElement('div', { className: "flex items-center justify-between" },
                      React.createElement('span', { className: "text-sm font-medium" }, "Rabat-Salé-Kénitra"),
                      React.createElement('div', { className: "flex items-center gap-2" },
                        React.createElement('div', { className: "w-24 bg-gray-200 rounded-full h-2" },
                          React.createElement('div', { className: "bg-blue-500 h-2 rounded-full", style: { width: '72%' } })
                        ),
                        React.createElement('span', { className: "text-sm text-gray-600" }, "72%")
                      )
                    ),
                    React.createElement('div', { className: "flex items-center justify-between" },
                      React.createElement('span', { className: "text-sm font-medium" }, "Marrakech-Safi"),
                      React.createElement('div', { className: "flex items-center gap-2" },
                        React.createElement('div', { className: "w-24 bg-gray-200 rounded-full h-2" },
                          React.createElement('div', { className: "bg-green-500 h-2 rounded-full", style: { width: '68%' } })
                        ),
                        React.createElement('span', { className: "text-sm text-gray-600" }, "68%")
                      )
                    ),
                    React.createElement('div', { className: "flex items-center justify-between" },
                      React.createElement('span', { className: "text-sm font-medium" }, "Fès-Meknès"),
                      React.createElement('div', { className: "flex items-center gap-2" },
                        React.createElement('div', { className: "w-24 bg-gray-200 rounded-full h-2" },
                          React.createElement('div', { className: "bg-yellow-500 h-2 rounded-full", style: { width: '45%' } })
                        ),
                        React.createElement('span', { className: "text-sm text-gray-600" }, "45%")
                      )
                    ),
                    React.createElement('div', { className: "flex items-center justify-between" },
                      React.createElement('span', { className: "text-sm font-medium" }, "Tanger-Tétouan-Al Hoceïma"),
                      React.createElement('div', { className: "flex items-center gap-2" },
                        React.createElement('div', { className: "w-24 bg-gray-200 rounded-full h-2" },
                          React.createElement('div', { className: "bg-purple-500 h-2 rounded-full", style: { width: '38%' } })
                        ),
                        React.createElement('span', { className: "text-sm text-gray-600" }, "38%")
                      )
                    )
                  )
                )
              )
            )
          ),
          React.createElement('div', { className: "grid grid-cols-1 lg:grid-cols-3 gap-6" },
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(Clock, { className: "w-5 h-5 text-institutional-primary" }),
                  "Activité par Heure"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "h-48" },
                  React.createElement('div', { className: "grid grid-cols-6 gap-1 h-full" },
                    Array.from({ length: 24 }, (_, i) =>
                      React.createElement('div', {
                        key: i,
                        className: "flex flex-col items-center justify-end"
                      },
                        React.createElement('div', {
                          className: `w-full bg-institutional-primary rounded-t`,
                          style: { height: `${Math.random() * 80 + 20}%` }
                        }),
                        React.createElement('span', { className: "text-xs text-gray-500 mt-1" },
                          i % 4 === 0 ? `${i}h` : ''
                        )
                      )
                    )
                  )
                )
              )
            ),
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(Users, { className: "w-5 h-5 text-institutional-primary" }),
                  "Types de Séjour"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-4" },
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Tourisme"),
                    React.createElement('span', { className: "text-lg font-bold text-blue-600" }, "65%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Affaires"),
                    React.createElement('span', { className: "text-lg font-bold text-green-600" }, "22%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Famille"),
                    React.createElement('span', { className: "text-lg font-bold text-purple-600" }, "10%")
                  ),
                  React.createElement('div', { className: "flex items-center justify-between" },
                    React.createElement('span', { className: "text-sm" }, "Études"),
                    React.createElement('span', { className: "text-lg font-bold text-orange-600" }, "3%")
                  )
                )
              )
            ),
            React.createElement(Card, { className: "bg-white border-0 shadow-sm" },
              React.createElement(CardHeader, null,
                React.createElement(CardTitle, { className: "flex items-center gap-2" },
                  React.createElement(AlertTriangle, { className: "w-5 h-5 text-institutional-primary" }),
                  "Alertes Système"
                )
              ),
              React.createElement(CardContent, null,
                React.createElement('div', { className: "space-y-3" },
                  React.createElement('div', { className: "flex items-start gap-3 p-3 bg-red-50 rounded-lg" },
                    React.createElement('div', { className: "w-2 h-2 bg-red-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium text-red-800" }, "Documents expirés"),
                      React.createElement('p', { className: "text-xs text-red-600" }, "12 documents à renouveler")
                    )
                  ),
                  React.createElement('div', { className: "flex items-start gap-3 p-3 bg-yellow-50 rounded-lg" },
                    React.createElement('div', { className: "w-2 h-2 bg-yellow-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium text-yellow-800" }, "Vérifications en attente"),
                      React.createElement('p', { className: "text-xs text-yellow-600" }, "8 déclarations à vérifier")
                    )
                  ),
                  React.createElement('div', { className: "flex items-start gap-3 p-3 bg-blue-50 rounded-lg" },
                    React.createElement('div', { className: "w-2 h-2 bg-blue-500 rounded-full mt-2" }),
                    React.createElement('div', null,
                      React.createElement('p', { className: "text-sm font-medium text-blue-800" }, "Nouveaux hôtes"),
                      React.createElement('p', { className: "text-xs text-blue-600" }, "5 inscriptions aujourd'hui")
                    )
                  )
                )
              )
            )
          )
        )
      )
    ),

    // Modal for detailed views
    selectedItem && React.createElement('div', {
      className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
      onClick: () => setSelectedItem(null)
    },
      React.createElement('div', {
        className: "bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",
        onClick: (e: any) => e.stopPropagation()
      },
        React.createElement('div', { className: "p-6" },
          React.createElement('div', { className: "flex items-center justify-between mb-6" },
            React.createElement('h2', { className: "text-xl font-bold text-gray-900" },
              selectedItemType === 'host' ? 'Détails de l\'Hôte' :
              selectedItemType === 'accommodation' ? 'Détails du Logement' :
              'Détails de la Déclaration'
            ),
            React.createElement(Button, {
              variant: "ghost",
              size: "sm",
              onClick: () => setSelectedItem(null)
            }, "✕")
          ),

          // Host Details
          selectedItemType === 'host' && React.createElement('div', { className: "space-y-4" },
            React.createElement('div', { className: "grid grid-cols-2 gap-4" },
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Nom complet"),
                React.createElement('p', { className: "text-gray-900" }, `${selectedItem.prenom} ${selectedItem.nom}`)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Email"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.email)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Téléphone"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.telephone)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "CIN"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.cin)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "ICE"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.ice)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Ville"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.ville)
              )
            ),
            React.createElement('div', null,
              React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Adresse"),
              React.createElement('p', { className: "text-gray-900" }, selectedItem.adresse)
            ),
            React.createElement('div', { className: "grid grid-cols-3 gap-4 pt-4 border-t" },
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-blue-600" }, selectedItem.nombreLogements),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Logements")
              ),
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-green-600" }, selectedItem.totalDeclarations),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Déclarations")
              ),
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-purple-600" }, selectedItem.statut),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Statut")
              )
            )
          ),

          // Accommodation Details
          selectedItemType === 'accommodation' && React.createElement('div', { className: "space-y-4" },
            React.createElement('div', { className: "grid grid-cols-2 gap-4" },
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Nom du logement"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.nom)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Type"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.type)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Code logement"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.codeLogement)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Ville"),
                React.createElement('p', { className: "text-gray-900" }, selectedItem.ville)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Capacité"),
                React.createElement('p', { className: "text-gray-900" }, `${selectedItem.capacite} personnes`)
              ),
              React.createElement('div', null,
                React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Tarif"),
                React.createElement('p', { className: "text-gray-900" }, `${selectedItem.tarif} MAD/nuit`)
              )
            ),
            React.createElement('div', null,
              React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Adresse"),
              React.createElement('p', { className: "text-gray-900" }, selectedItem.adresse)
            ),
            React.createElement('div', { className: "grid grid-cols-3 gap-4 pt-4 border-t" },
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-blue-600" }, selectedItem.occupationActuelle),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Occupation")
              ),
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-green-600" }, selectedItem.totalDeclarations),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Déclarations")
              ),
              React.createElement('div', { className: "text-center" },
                React.createElement('p', { className: "text-2xl font-bold text-purple-600" }, selectedItem.statut),
                React.createElement('p', { className: "text-sm text-gray-600" }, "Statut")
              )
            )
          ),

          // Declaration Details - Enhanced for Law Enforcement
          selectedItemType === 'declaration' && React.createElement('div', { className: "space-y-6" },
            // Security Alert Section
            selectedItem.securityFlags?.isWatchListed && React.createElement('div', { className: "bg-red-50 border border-red-200 rounded-lg p-4" },
              React.createElement('div', { className: "flex items-center gap-2 mb-2" },
                React.createElement(AlertTriangle, { className: "w-5 h-5 text-red-600" }),
                React.createElement('h3', { className: "font-semibold text-red-800" }, "ALERTE SÉCURITÉ")
              ),
              React.createElement('p', { className: "text-sm text-red-700 mb-2" }, `Niveau de risque: ${selectedItem.securityFlags.riskLevel.toUpperCase()}`),
              selectedItem.securityFlags.flaggedReasons?.length > 0 && React.createElement('div', null,
                React.createElement('p', { className: "text-sm font-medium text-red-700" }, "Raisons:"),
                React.createElement('ul', { className: "list-disc list-inside text-sm text-red-600" },
                  selectedItem.securityFlags.flaggedReasons.map((reason: string, idx: number) =>
                    React.createElement('li', { key: idx }, reason)
                  )
                )
              )
            ),

            // Personal Information
            React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(User, { className: "w-5 h-5" }),
                "Informations Personnelles"
              ),
              React.createElement('div', { className: "grid grid-cols-2 gap-4" },
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Nom complet"),
                  React.createElement('p', { className: "text-gray-900 font-medium" }, `${selectedItem.prenom} ${selectedItem.nom}`)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "ID Invité"),
                  React.createElement('p', { className: "text-gray-900 font-mono" }, selectedItem.guestId)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Date de naissance"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.dateNaissance)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Sexe"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.sexe)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Nationalité"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.nationalite)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Pièce d'identité"),
                  React.createElement('p', { className: "text-gray-900 font-mono" }, selectedItem.numeroPieceIdentite)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Téléphone"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.telephone)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Email"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.email)
                )
              )
            ),

            // Document Verification Section
            React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(Shield, { className: "w-5 h-5" }),
                "Vérification des Documents"
              ),
              React.createElement('div', { className: "grid grid-cols-2 md:grid-cols-4 gap-4" },
                React.createElement('div', { className: "border rounded-lg p-3 text-center" },
                  React.createElement('img', {
                    src: selectedItem.documentVerification?.idPhotoUrl || '/placeholder-id.jpg',
                    alt: "Photo ID",
                    className: "w-full h-20 object-cover rounded mb-2"
                  }),
                  React.createElement('p', { className: "text-xs text-gray-600" }, "Photo ID")
                ),
                React.createElement('div', { className: "border rounded-lg p-3 text-center" },
                  React.createElement('img', {
                    src: selectedItem.documentVerification?.signatureUrl || '/placeholder-signature.jpg',
                    alt: "Signature",
                    className: "w-full h-20 object-cover rounded mb-2"
                  }),
                  React.createElement('p', { className: "text-xs text-gray-600" }, "Signature")
                ),
                React.createElement('div', { className: "border rounded-lg p-3 text-center" },
                  React.createElement('img', {
                    src: selectedItem.documentVerification?.selfieUrl || '/placeholder-selfie.jpg',
                    alt: "Selfie",
                    className: "w-full h-20 object-cover rounded mb-2"
                  }),
                  React.createElement('p', { className: "text-xs text-gray-600" }, "Selfie")
                ),
                React.createElement('div', { className: "border rounded-lg p-3 text-center" },
                  React.createElement('div', { className: "w-full h-20 bg-gray-100 rounded mb-2 flex items-center justify-center" },
                    selectedItem.documentVerification?.biometricData ?
                      React.createElement(CheckCircle, { className: "w-8 h-8 text-green-600" }) :
                      React.createElement('span', { className: "text-xs text-gray-500" }, "N/A")
                  ),
                  React.createElement('p', { className: "text-xs text-gray-600" }, "Biométrie")
                )
              )
            ),

            // Stay Information
            React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(Calendar, { className: "w-5 h-5" }),
                "Informations du Séjour"
              ),
              React.createElement('div', { className: "grid grid-cols-2 gap-4" },
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Date d'arrivée"),
                  React.createElement('p', { className: "text-gray-900" }, `${selectedItem.dateArrivee} à ${selectedItem.heureArrivee}`)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Date de départ"),
                  React.createElement('p', { className: "text-gray-900" }, `${selectedItem.dateDepart} à ${selectedItem.heureDepart}`)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Motif du séjour"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.motifSejour)
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Nombre de personnes"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.nombrePersonnes)
                ),
                React.createElement('div', { className: "col-span-2" },
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Logement"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.nomLogement),
                  React.createElement('p', { className: "text-sm text-gray-600" }, selectedItem.adresseLogement)
                )
              )
            ),

            // Travel History
            selectedItem.travelHistory && React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(Globe, { className: "w-5 h-5" }),
                "Historique de Voyage"
              ),
              React.createElement('div', { className: "grid grid-cols-2 gap-4" },
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Pays précédents"),
                  React.createElement('p', { className: "text-gray-900" },
                    selectedItem.travelHistory.previousCountries?.join(', ') || 'Aucun'
                  )
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Point d'entrée"),
                  React.createElement('p', { className: "text-gray-900" },
                    selectedItem.travelHistory.entryPoints?.[0] || 'Non spécifié'
                  )
                ),
                React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Statut visa"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.travelHistory.visaStatus)
                ),
                selectedItem.travelHistory.passportExpiry && React.createElement('div', null,
                  React.createElement('label', { className: "text-sm font-medium text-gray-600" }, "Expiration passeport"),
                  React.createElement('p', { className: "text-gray-900" }, selectedItem.travelHistory.passportExpiry)
                )
              )
            ),

            // Emergency Contacts
            selectedItem.emergencyContacts?.length > 0 && React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(Phone, { className: "w-5 h-5" }),
                "Contacts d'Urgence"
              ),
              React.createElement('div', { className: "space-y-3" },
                selectedItem.emergencyContacts.map((contact: any, idx: number) =>
                  React.createElement('div', { key: idx, className: "border rounded-lg p-3" },
                    React.createElement('div', { className: "grid grid-cols-2 gap-2 text-sm" },
                      React.createElement('div', null,
                        React.createElement('span', { className: "font-medium" }, contact.name),
                        React.createElement('span', { className: "text-gray-600 ml-2" }, `(${contact.relationship})`)
                      ),
                      React.createElement('div', null,
                        React.createElement('span', { className: "text-gray-900" }, contact.phone)
                      )
                    )
                  )
                )
              )
            ),

            // Investigation Notes
            selectedItem.investigationNotes?.length > 0 && React.createElement('div', null,
              React.createElement('h3', { className: "text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2" },
                React.createElement(FileText, { className: "w-5 h-5" }),
                "Notes d'Enquête"
              ),
              React.createElement('div', { className: "space-y-2 max-h-40 overflow-y-auto" },
                selectedItem.investigationNotes.map((note: any, idx: number) =>
                  React.createElement('div', { key: idx, className: "border-l-4 border-blue-500 pl-3 py-2" },
                    React.createElement('div', { className: "flex items-center justify-between mb-1" },
                      React.createElement('span', { className: "text-sm font-medium" }, note.officer),
                      React.createElement('span', { className: "text-xs text-gray-500" }, note.timestamp)
                    ),
                    React.createElement('p', { className: "text-sm text-gray-700" }, note.note),
                    React.createElement('span', {
                      className: `inline-block px-2 py-1 text-xs rounded mt-1 ${
                        note.category === 'suspicious' ? 'bg-red-100 text-red-800' :
                        note.category === 'verified' ? 'bg-green-100 text-green-800' :
                        note.category === 'follow-up' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`
                    }, note.category)
                  )
                )
              )
            )
          )
        )
      )
    )
  )
}
