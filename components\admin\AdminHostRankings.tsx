'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Users, Building2, TrendingUp, MapPin, Phone, Mail, 
  Crown, Medal, Award, Filter, Download, Eye
} from 'lucide-react'
import { adminHosts, moroccanCities } from '@/utils/mockData'
import { AdminHost } from '@/types/admin'

interface AdminHostRankingsProps {
  showFilters?: boolean
  onViewDetails?: (host: AdminHost) => void
}

export function AdminHostRankings({ showFilters = false, onViewDetails }: AdminHostRankingsProps) {
  const [sortBy, setSortBy] = useState<'accommodations' | 'declarations'>('accommodations')
  const [filterCity, setFilterCity] = useState<string>('all')
  const [filterRegion, setFilterRegion] = useState<string>('all')

  const sortedHosts = useMemo(() => {
    let filtered = adminHosts.filter(host => {
      const cityMatch = filterCity === 'all' || host.ville === filterCity
      const regionMatch = filterRegion === 'all' || host.region === filterRegion
      return cityMatch && regionMatch && host.statut === 'actif'
    })

    return filtered.sort((a, b) => {
      if (sortBy === 'accommodations') {
        return b.nombreLogements - a.nombreLogements
      } else {
        return b.totalDeclarations - a.totalDeclarations
      }
    }).slice(0, 20) // Top 20
  }, [sortBy, filterCity, filterRegion])

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="w-5 h-5 text-yellow-500" />
      case 2: return <Medal className="w-5 h-5 text-gray-400" />
      case 3: return <Award className="w-5 h-5 text-amber-600" />
      default: return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</span>
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 2: return 'bg-gray-100 text-gray-800 border-gray-200'
      case 3: return 'bg-amber-100 text-amber-800 border-amber-200'
      default: return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const regions = [...new Set(moroccanCities.map(city => city.region))]

  return (
    <Card className="bg-white border-0 shadow-sm">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-institutional-primary" />
            Classement des Hôtes
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={(value: 'accommodations' | 'declarations') => setSortBy(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="accommodations">Par nombre de logements</SelectItem>
                <SelectItem value="declarations">Par nombre de déclarations</SelectItem>
              </SelectContent>
            </Select>
            
            {showFilters && (
              <>
                <Select value={filterCity} onValueChange={setFilterCity}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Ville" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les villes</SelectItem>
                    {moroccanCities.map(city => (
                      <SelectItem key={city.name} value={city.name}>
                        {city.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={filterRegion} onValueChange={setFilterRegion}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Région" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les régions</SelectItem>
                    {regions.map(region => (
                      <SelectItem key={region} value={region}>
                        {region}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </>
            )}
            
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          {sortedHosts.map((host, index) => {
            const rank = index + 1
            return (
              <div
                key={host.id}
                className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {/* Rank */}
                <div className="flex items-center gap-3">
                  {getRankIcon(rank)}
                  <Badge variant="outline" className={getRankBadgeColor(rank)}>
                    #{rank}
                  </Badge>
                </div>

                {/* Host Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {host.prenom} {host.nom}
                    </h3>
                    <Badge 
                      variant="outline" 
                      className={host.statut === 'actif' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-red-50 text-red-700 border-red-200'}
                    >
                      {host.statut}
                    </Badge>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span>{host.ville}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Phone className="w-3 h-3" />
                      <span>{host.telephone}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="w-3 h-3" />
                      <span className="truncate max-w-48">{host.email}</span>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center gap-6 text-center">
                  <div className="min-w-0">
                    <div className="flex items-center gap-1 text-lg font-bold text-gray-900">
                      <Building2 className="w-4 h-4 text-institutional-primary" />
                      <span>{host.nombreLogements}</span>
                    </div>
                    <p className="text-xs text-gray-500">Logements</p>
                  </div>
                  
                  <div className="min-w-0">
                    <div className="flex items-center gap-1 text-lg font-bold text-gray-900">
                      <TrendingUp className="w-4 h-4 text-green-600" />
                      <span>{host.totalDeclarations}</span>
                    </div>
                    <p className="text-xs text-gray-500">Déclarations</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewDetails?.(host)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {sortedHosts.length === 0 && (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Aucun hôte trouvé avec les filtres sélectionnés</p>
          </div>
        )}

        {/* Summary Stats */}
        {sortedHosts.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-2xl font-bold text-blue-600">
                  {sortedHosts.reduce((sum, host) => sum + host.nombreLogements, 0)}
                </p>
                <p className="text-sm text-blue-600">Total Logements</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <p className="text-2xl font-bold text-green-600">
                  {sortedHosts.reduce((sum, host) => sum + host.totalDeclarations, 0)}
                </p>
                <p className="text-sm text-green-600">Total Déclarations</p>
              </div>
              <div className="bg-purple-50 rounded-lg p-4">
                <p className="text-2xl font-bold text-purple-600">
                  {(sortedHosts.reduce((sum, host) => sum + host.totalDeclarations, 0) / 
                    sortedHosts.reduce((sum, host) => sum + host.nombreLogements, 0)).toFixed(1)}
                </p>
                <p className="text-sm text-purple-600">Déclarations/Logement</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
