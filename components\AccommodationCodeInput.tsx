'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useRouter } from 'next/navigation'
import { useForm } from '@/contexts/FormContext'
import { Home, AlertCircle, CheckCircle } from 'lucide-react'

interface AccommodationCodeInputProps {
  onClose: () => void
}

export function AccommodationCodeInput({ onClose }: AccommodationCodeInputProps) {
  const [code, setCode] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [isValid, setIsValid] = useState(false)
  const router = useRouter()
  const { setUserType } = useForm()

  // Format: T-XXXXXX where X are numbers
  const validateCode = (inputCode: string): boolean => {
    const codeRegex = /^T-\d{6}$/
    return codeRegex.test(inputCode)
  }

  const handleCodeChange = (value: string) => {
    // Auto-format the input
    let formattedValue = value.toUpperCase().replace(/[^T0-9-]/g, '')
    
    // Add T- prefix if not present
    if (formattedValue && !formattedValue.startsWith('T-')) {
      if (formattedValue.startsWith('T')) {
        formattedValue = 'T-' + formattedValue.slice(1)
      } else {
        formattedValue = 'T-' + formattedValue
      }
    }
    
    // Limit to T-XXXXXX format
    if (formattedValue.length > 8) {
      formattedValue = formattedValue.slice(0, 8)
    }
    
    setCode(formattedValue)
    setError(null)
    setIsValid(false)
    
    // Validate if complete
    if (formattedValue.length === 8) {
      const isValidCode = validateCode(formattedValue)
      setIsValid(isValidCode)
      if (!isValidCode) {
        setError('Format invalide. Utilisez le format T-XXXXXX (ex: T-123456)')
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!code) {
      setError('Veuillez saisir un code d\'hébergement')
      return
    }
    
    if (!validateCode(code)) {
      setError('Format invalide. Utilisez le format T-XXXXXX (ex: T-123456)')
      return
    }
    
    setIsValidating(true)
    setError(null)
    
    try {
      // Simulate API call to validate accommodation code
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // For demo purposes, accept any valid format
      // In real implementation, check against database
      const mockValidCodes = ['T-123456', 'T-789012', 'T-345678']
      const isCodeValid = mockValidCodes.includes(code) || Math.random() > 0.3
      
      if (isCodeValid) {
        // Store the accommodation code for the check-in process
        sessionStorage.setItem('accommodationCode', code)
        setUserType('guest')
        router.push('/self-check-in')
        onClose()
      } else {
        setError('Code d\'hébergement non trouvé. Vérifiez le code et réessayez.')
      }
    } catch (err) {
      setError('Erreur lors de la validation. Veuillez réessayer.')
    } finally {
      setIsValidating(false)
    }
  }

  return (
    <div 
      className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div 
        className="w-full max-w-md bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl logo-background-content p-6"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="text-center pb-6">
          <div className="mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
              <Home className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Code d'Hébergement
            </h2>
          </div>
          <p className="text-gray-600 text-sm leading-relaxed">
            🏠 Saisissez le code fourni par votre hôte pour accéder au check-in
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="accommodationCode" className="text-sm font-medium text-gray-700">
              Code d'hébergement
            </Label>
            <Input
              id="accommodationCode"
              type="text"
              value={code}
              onChange={(e) => handleCodeChange(e.target.value)}
              placeholder="T-123456"
              className={`mt-2 text-center text-xl font-mono tracking-wider h-14 rounded-xl border-2 transition-all duration-200 ${
                isValid ? 'border-green-500 bg-green-50 shadow-green-100 shadow-lg' :
                error ? 'border-red-500 bg-red-50 shadow-red-100 shadow-lg' :
                'border-gray-300 hover:border-institutional-primary focus:border-institutional-primary focus:shadow-institutional-primary/20 focus:shadow-lg'
              }`}
              disabled={isValidating}
              maxLength={8}
            />
            
            {/* Format hint */}
            <p className="text-xs text-gray-500 mt-1 text-center">
              Format: T-XXXXXX (T suivi d'un tiret et de 6 chiffres)
            </p>
          </div>

          {/* Validation Status */}
          {isValid && !error && (
            <div className="flex items-center gap-2 text-green-600 text-sm">
              <CheckCircle className="w-4 h-4" />
              Format valide
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Example codes for demo */}
          <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
            <p className="text-blue-800 text-sm font-semibold mb-3 flex items-center gap-2">
              🧪 <span>Codes d'exemple pour test:</span>
            </p>
            <div className="grid grid-cols-1 gap-2">
              {['T-123456', 'T-789012', 'T-345678'].map((exampleCode) => (
                <button
                  key={exampleCode}
                  type="button"
                  onClick={() => handleCodeChange(exampleCode)}
                  className="text-sm bg-white hover:bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-mono border border-blue-200 hover:border-blue-300 transition-all duration-200 hover:shadow-md"
                  disabled={isValidating}
                >
                  {exampleCode}
                </button>
              ))}
            </div>
            <p className="text-blue-600 text-xs mt-2 text-center">
              Cliquez sur un code pour le tester
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4">
            <Button
              type="submit"
              disabled={!code || !isValid || isValidating}
              className="w-full h-12 bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
            >
              {isValidating ? (
                <>
                  <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Validation du code...
                </>
              ) : (
                <>
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Accéder au Check-in
                </>
              )}
            </Button>

            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="w-full h-12 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 rounded-xl font-semibold transition-all duration-200"
              disabled={isValidating}
            >
              Retour
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
