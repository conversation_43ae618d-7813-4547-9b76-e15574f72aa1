'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Image from 'next/image'
import { useRouter } from 'next/navigation'

// Mock data - this would come from the previous steps in a real application
const contractData = {
  logement: "Chalet Montagne",
  locataire: "John Doe",
  dateDebut: "20/12/2024",
  dateFin: "27/12/2024",
  idRectoUrl: "/placeholder.svg?height=200&width=320",
  idVersoUrl: "/placeholder.svg?height=200&width=320",
  signatureUrl: "/placeholder.svg?height=100&width=300",
}

const clausesGenerales = [
  "Le locataire s'engage à respecter les règles de copropriété",
  "Le locataire doit maintenir le logement en bon état",
  "Les animaux ne sont pas autorisés sauf accord préalable",
  "Le nombre d'occupants ne peut excéder celui indiqué au contrat",
  "Les nuisances sonores sont interdites entre 22h et 7h",
  "Le locataire doit souscrire une assurance habitation",
  "L'état des lieux sera effectué à l'entrée et à la sortie",
  "Le dépôt de garantie sera restitué dans les délais légaux"
]

export function GenerateContract() {
  const router = useRouter()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const handlePrint = () => {
    window.print()
  }

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true)
    try {
      // Simulate PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      // In a real application, you would generate and download the PDF here
      alert('PDF téléchargé')
    } catch (error) {
      console.error('Error generating PDF:', error)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardContent className="p-6">
        <div className="space-y-6">
          <h1 className="text-3xl font-bold text-center">Contrat de Location</h1>

          <div className="space-y-2">
            <div>
              <span className="font-semibold">Logement:</span> {contractData.logement}
            </div>
            <div>
              <span className="font-semibold">Locataire:</span> {contractData.locataire}
            </div>
            <div>
              <span className="font-semibold">Date de début:</span> {contractData.dateDebut}
            </div>
            <div>
              <span className="font-semibold">Date de fin:</span> {contractData.dateFin}
            </div>
          </div>

          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Clauses générales:</h2>
            <ol className="list-decimal list-inside space-y-1">
              {clausesGenerales.map((clause, index) => (
                <li key={index}>{clause}</li>
              ))}
            </ol>
          </div>

          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Pièce d'identité du locataire:</h2>
            <div className="flex flex-wrap gap-4">
              <Image 
                src={contractData.idRectoUrl} 
                alt="ID Recto" 
                width={320} 
                height={200} 
                className="rounded-lg bg-gray-100"
              />
              <Image 
                src={contractData.idVersoUrl} 
                alt="ID Verso" 
                width={320} 
                height={200} 
                className="rounded-lg bg-gray-100"
              />
            </div>
          </div>

          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Signature du locataire:</h2>
            <Image 
              src={contractData.signatureUrl} 
              alt="Signature" 
              width={300} 
              height={100} 
              className="rounded-lg bg-gray-100"
            />
          </div>

          <div className="flex justify-end space-x-4 print:hidden">
            <Button
              variant="default"
              onClick={handlePrint}
            >
              Imprimer
            </Button>
            <Button
              variant="default"
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
            >
              {isGeneratingPDF ? 'Génération...' : 'Télécharger PDF'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

