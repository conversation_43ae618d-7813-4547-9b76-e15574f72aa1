'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { InstitutionalHeader } from './InstitutionalHeader'
import { useIsMobile } from '@/hooks/use-mobile'
import { Building2, ArrowLeft, Mail, Lock, Eye, EyeOff, UserPlus, Check, X } from 'lucide-react'
import Link from 'next/link'

export function SignUpForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const router = useRouter()
  const isMobile = useIsMobile()

  // Password strength checker
  const checkPasswordStrength = (pwd: string) => {
    let strength = 0
    if (pwd.length >= 8) strength++
    if (/[A-Z]/.test(pwd)) strength++
    if (/[a-z]/.test(pwd)) strength++
    if (/[0-9]/.test(pwd)) strength++
    if (/[^A-Za-z0-9]/.test(pwd)) strength++
    return strength
  }

  const handlePasswordChange = (value: string) => {
    setPassword(value)
    setPasswordStrength(checkPasswordStrength(value))
  }

  const isPasswordMatch = password === confirmPassword && confirmPassword.length > 0
  const isFormValid = email && password && confirmPassword && isPasswordMatch && passwordStrength >= 3

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormValid) return

    setIsLoading(true)

    try {
      // Here you would typically handle the sign-up logic
      console.log('Sign-up attempted with:', email, password)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Redirect to dashboard after successful sign-up
      router.push('/dashboard')
    } catch (error) {
      console.error('Sign-up error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background-large">
      <InstitutionalHeader />
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] mobile-container logo-background-content">
        <div className="w-full max-w-sm sm:max-w-md space-y-4 sm:space-y-6">
          {/* Enhanced Mobile-First Back Button */}
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-institutional-primary transition-colors touch-target animate-float-up"
          >
            <ArrowLeft className="w-4 h-4" />
            {isMobile ? 'Retour' : 'Retour à la connexion'}
          </Link>

          {/* Enhanced Mobile-First Signup Card */}
          <Card className="bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card animate-slide-up-from-bottom relative overflow-hidden">
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-institutional-primary/5 pointer-events-none"></div>

            <CardHeader className="text-center mobile-padding pb-4 sm:pb-6 relative">
              <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-institutional-primary to-institutional-accent mobile-card mb-4 sm:mb-6 shadow-lg shadow-institutional-primary/25 animate-float-up">
                <Building2 className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
              <CardTitle className="mobile-text-xl font-bold text-gray-800 animate-float-up-delay-1">
                {isMobile ? 'Inscription' : 'Inscription Propriétaires'}
              </CardTitle>
              <p className="text-gray-600 mt-2 text-sm sm:text-base animate-float-up-delay-2">
                Créez votre compte pour gérer vos propriétés
              </p>
            </CardHeader>
            <CardContent className="mobile-padding pt-0 space-y-4 sm:space-y-6 relative">
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 animate-float-up-delay-2">
                {/* Enhanced Email Field */}
                <div className="space-y-2 animate-float-up-delay-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Mail className="w-4 h-4 text-institutional-primary" />
                    Adresse email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={isLoading}
                    className="mobile-input border border-gray-300 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20 mobile-card transition-all duration-200 disabled:opacity-50"
                  />
                </div>
                {/* Enhanced Password Field with Strength Indicator */}
                <div className="space-y-2 animate-float-up-delay-3">
                  <Label htmlFor="password" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-institutional-primary" />
                    Mot de passe
                  </Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => handlePasswordChange(e.target.value)}
                      required
                      disabled={isLoading}
                      className="mobile-input border border-gray-300 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20 mobile-card transition-all duration-200 disabled:opacity-50 pr-12"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 touch-target"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4 text-gray-500" />
                      ) : (
                        <Eye className="w-4 h-4 text-gray-500" />
                      )}
                    </Button>
                  </div>

                  {/* Password Strength Indicator */}
                  {password && (
                    <div className="space-y-2">
                      <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <div
                            key={level}
                            className={`h-1 flex-1 rounded-full transition-colors duration-200 ${
                              passwordStrength >= level
                                ? passwordStrength <= 2
                                  ? 'bg-red-400'
                                  : passwordStrength <= 3
                                  ? 'bg-yellow-400'
                                  : 'bg-green-400'
                                : 'bg-gray-200'
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs text-gray-500">
                        Force: {passwordStrength <= 2 ? 'Faible' : passwordStrength <= 3 ? 'Moyenne' : 'Forte'}
                      </p>
                    </div>
                  )}
                </div>
                {/* Enhanced Confirm Password Field */}
                <div className="space-y-2 animate-float-up-delay-3">
                  <Label htmlFor="confirmPassword" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-institutional-primary" />
                    Confirmer le mot de passe
                    {confirmPassword && (
                      isPasswordMatch ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : (
                        <X className="w-4 h-4 text-red-500" />
                      )
                    )}
                  </Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      disabled={isLoading}
                      className={`mobile-input border transition-all duration-200 disabled:opacity-50 pr-12 mobile-card ${
                        confirmPassword
                          ? isPasswordMatch
                            ? 'border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-500/20'
                            : 'border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-500/20'
                          : 'border-gray-300 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20'
                      }`}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 touch-target"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      disabled={isLoading}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="w-4 h-4 text-gray-500" />
                      ) : (
                        <Eye className="w-4 h-4 text-gray-500" />
                      )}
                    </Button>
                  </div>

                  {/* Password Match Indicator */}
                  {confirmPassword && !isPasswordMatch && (
                    <p className="text-xs text-red-500 flex items-center gap-1">
                      <X className="w-3 h-3" />
                      Les mots de passe ne correspondent pas
                    </p>
                  )}
                </div>
                {/* Enhanced Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading || !isFormValid}
                  className="w-full mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white mobile-card shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 text-base font-semibold touch-target disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 animate-float-up-delay-3 relative overflow-hidden group"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Création...
                    </>
                  ) : (
                    <>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                      <UserPlus className="w-5 h-5 mr-2 relative z-10" />
                      <span className="relative z-10">Créer un compte</span>
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
            {/* Enhanced Footer */}
            <CardFooter className="flex justify-center mobile-padding pt-4 sm:pt-6 border-t border-gray-100 relative">
              <p className="text-sm text-gray-600 text-center animate-float-up-delay-3">
                Déjà un compte ?{' '}
                <Link
                  href="/login"
                  className="text-institutional-primary hover:text-green-700 font-semibold hover:underline transition-colors touch-target inline-block py-1"
                >
                  Se connecter
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}

