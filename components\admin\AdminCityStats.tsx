'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  MapPin, Building2, FileText, Users, TrendingUp, 
  BarChart3, Eye, ArrowRight 
} from 'lucide-react'
import { CityStats } from '@/types/admin'

interface AdminCityStatsProps {
  cities: CityStats[]
  onViewCityDetails?: (city: CityStats) => void
}

export function AdminCityStats({ cities, onViewCityDetails }: AdminCityStatsProps) {
  const maxAccommodations = Math.max(...cities.map(city => city.totalAccommodations))
  const maxDeclarations = Math.max(...cities.map(city => city.totalDeclarations))

  return (
    <Card className="bg-white border-0 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-institutional-primary" />
          Statistiques par Ville
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {cities.slice(0, 10).map((city, index) => (
            <div
              key={city.ville}
              className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              onClick={() => onViewCityDetails?.(city)}
            >
              {/* Rank and City Info */}
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <div className="w-8 h-8 bg-institutional-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {index + 1}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900 truncate">{city.ville}</h3>
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      {city.region}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <MapPin className="w-3 h-3" />
                    <span>{city.activeHosts} hôtes actifs</span>
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-3 gap-4 min-w-0">
                {/* Accommodations */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Building2 className="w-4 h-4 text-green-600" />
                    <span className="text-lg font-bold text-gray-900">
                      {city.totalAccommodations}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mb-2">Logements</p>
                  <Progress 
                    value={(city.totalAccommodations / maxAccommodations) * 100} 
                    className="h-1.5"
                  />
                </div>

                {/* Declarations */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <FileText className="w-4 h-4 text-blue-600" />
                    <span className="text-lg font-bold text-gray-900">
                      {city.totalDeclarations}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mb-2">Déclarations</p>
                  <Progress 
                    value={(city.totalDeclarations / maxDeclarations) * 100} 
                    className="h-1.5"
                  />
                </div>

                {/* Occupancy Rate */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <TrendingUp className="w-4 h-4 text-purple-600" />
                    <span className="text-lg font-bold text-gray-900">
                      {city.occupancyRate}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mb-2">Occupation</p>
                  <Progress 
                    value={city.occupancyRate} 
                    className="h-1.5"
                  />
                </div>
              </div>

              {/* Action */}
              <div className="flex items-center">
                <button
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={(e) => {
                    e.stopPropagation()
                    onViewCityDetails?.(city)
                  }}
                >
                  <Eye className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center bg-blue-50 rounded-lg p-3">
              <p className="text-xl font-bold text-blue-600">
                {cities.reduce((sum, city) => sum + city.totalAccommodations, 0)}
              </p>
              <p className="text-sm text-blue-600">Total Logements</p>
            </div>
            <div className="text-center bg-green-50 rounded-lg p-3">
              <p className="text-xl font-bold text-green-600">
                {cities.reduce((sum, city) => sum + city.totalDeclarations, 0)}
              </p>
              <p className="text-sm text-green-600">Total Déclarations</p>
            </div>
            <div className="text-center bg-purple-50 rounded-lg p-3">
              <p className="text-xl font-bold text-purple-600">
                {cities.reduce((sum, city) => sum + city.activeHosts, 0)}
              </p>
              <p className="text-sm text-purple-600">Hôtes Actifs</p>
            </div>
            <div className="text-center bg-amber-50 rounded-lg p-3">
              <p className="text-xl font-bold text-amber-600">
                {(cities.reduce((sum, city) => sum + city.occupancyRate, 0) / cities.length).toFixed(1)}%
              </p>
              <p className="text-sm text-amber-600">Occupation Moyenne</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
