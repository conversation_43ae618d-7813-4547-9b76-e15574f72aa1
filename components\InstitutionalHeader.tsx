'use client'

import Image from 'next/image'
import { Bell, Search, Menu, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useIsMobile } from '@/hooks/use-mobile'
import type { UserType } from '@/contexts/FormContext'

interface InstitutionalHeaderProps {
  className?: string
  showMobileActions?: boolean
  userType?: UserType
}

export function InstitutionalHeader({
  className = '',
  showMobileActions = false,
  userType = 'host'
}: InstitutionalHeaderProps) {
  const isMobile = useIsMobile()

  // For guest users, we don't show mobile actions regardless of the prop
  const shouldShowMobileActions = userType === 'host' && showMobileActions

  return (
    <div className={`bg-white border-b border-gray-100 ${className}`}>
      <div className="max-w-7xl mx-auto mobile-container py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Mobile Layout */}
          {isMobile ? (
            <>
              {/* Left side - Main logo and title */}
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <Image
                  src="/MINISTERE INTERIEUR.png"
                  alt="Ministère de l'Intérieur"
                  width={40}
                  height={40}
                  className="h-8 w-auto object-contain flex-shrink-0"
                  priority
                />
                <div className="min-w-0 flex-1">
                  <h1 className="text-sm font-bold text-gray-800 leading-tight truncate">
                    Royaume du Maroc
                  </h1>
                  <p className="text-xs text-gray-600 font-medium truncate">
                    Déclaration Locataires
                  </p>
                </div>
              </div>

              {/* Right side - Mobile actions (only for host users) */}
              {shouldShowMobileActions && (
                <div className="flex items-center space-x-2 flex-shrink-0">
                  <Button variant="ghost" size="sm" className="touch-target">
                    <Search className="touch-icon" />
                  </Button>
                  <Button variant="ghost" size="sm" className="touch-target relative">
                    <Bell className="touch-icon" />
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-institutional-accent rounded-full"></div>
                  </Button>
                  <Button variant="ghost" size="sm" className="touch-target">
                    <User className="touch-icon" />
                  </Button>
                </div>
              )}
            </>
          ) : (
            /* Desktop/Tablet Layout */
            <>
              {/* Left side - All logos */}
              <div className="flex items-center space-x-3">
                <Image
                  src="/MINISTERE INTERIEUR.png"
                  alt="Ministère de l'Intérieur"
                  width={48}
                  height={48}
                  className="h-10 w-auto object-contain"
                  priority
                />
                <div className="h-6 w-px bg-gray-200"></div>
                <Image
                  src="/Patch_of_the_DGSN - Copie.png"
                  alt="DGSN"
                  width={48}
                  height={48}
                  className="h-10 w-auto object-contain"
                  priority
                />
                <div className="h-6 w-px bg-gray-200"></div>
                <Image
                  src="/Royal_Moroccan_Gendarmerie.png"
                  alt="Gendarmerie Royale"
                  width={48}
                  height={48}
                  className="h-10 w-auto object-contain"
                  priority
                />
              </div>

              {/* Right side - Title */}
              <div className="text-right">
                <h1 className="text-lg font-bold text-gray-800 leading-tight">
                  Royaume du Maroc
                </h1>
                <p className="text-sm text-gray-600 font-medium">
                  Système de Déclaration des Locataires
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
