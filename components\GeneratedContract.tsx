'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import Image from 'next/image'
import { Card, CardContent } from "@/components/ui/card"

interface GeneratedContractProps {
  logement: string
  voyageur: string
}

export function GeneratedContract({ logement, voyageur }: GeneratedContractProps) {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = async () => {
    setIsDownloading(true)
    // Implement PDF generation and download logic here
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulating download
    setIsDownloading(false)
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="space-y-6 p-6">
        <h2 className="text-2xl font-bold text-center">Contrat de Location</h2>
        
        <div className="space-y-4">
          <p><strong>Logement:</strong> {logement}</p>
          <p><strong>Locataire:</strong> {voyageur}</p>
          <p><strong>Date de début:</strong> {new Date().toLocaleDateString()}</p>
          <p><strong>Date de fin:</strong> {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
        </div>

        <div className="space-y-2">
          <h3 className="text-xl font-semibold">Clauses générales:</h3>
          <ol className="list-decimal list-inside space-y-1">
            <li>Le locataire s'engage à respecter les règles de copropriété</li>
            <li>Le locataire doit maintenir le logement en bon état</li>
            <li>Les animaux ne sont pas autorisés sauf accord préalable</li>
            <li>Le nombre d'occupants ne peut excéder celui indiqué au contrat</li>
            <li>Les nuisances sonores sont interdites entre 22h et 7h</li>
            <li>Le locataire doit souscrire une assurance habitation</li>
            <li>L'état des lieux sera effectué à l'entrée et à la sortie</li>
            <li>Le dépôt de garantie sera restitué dans les délais légaux</li>
          </ol>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Pièce d'identité du locataire:</h3>
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <Image src="/placeholder.svg?height=200&width=320" alt="ID Recto" width={320} height={200} className="rounded-lg" />
            <Image src="/placeholder.svg?height=200&width=320" alt="ID Verso" width={320} height={200} className="rounded-lg" />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-xl font-semibold">Signature du locataire:</h3>
          <Image src="/placeholder.svg?height=100&width=300" alt="Signature" width={300} height={100} className="rounded-lg" />
        </div>

        <div className="flex justify-end space-x-4">
          <Button onClick={handlePrint}>Imprimer</Button>
          <Button onClick={handleDownload} disabled={isDownloading}>
            {isDownloading ? 'Téléchargement...' : 'Télécharger PDF'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

