'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useRouter } from "next/navigation"
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play, 
  RotateCcw,
  FileText,
  Camera,
  Shield,
  Download,
  UserPlus,
  AlertTriangle
} from 'lucide-react'
import { GuestDataStorage } from '@/utils/dataStorage'
import { DataEncryption } from '@/utils/encryption'
import { performQuickLivenessCheck } from '@/utils/livenessDetection'
import { pdfRetryManager } from '@/utils/networkRetry'

interface TestResult {
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  message?: string
  duration?: number
  details?: any
}

export default function TestGuestJourneyPage() {
  const router = useRouter()
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Data Storage Initialization', status: 'pending' },
    { name: 'Accommodation Code Validation', status: 'pending' },
    { name: 'Form Data Persistence', status: 'pending' },
    { name: 'AES Encryption/Decryption', status: 'pending' },
    { name: 'Liveness Detection Simulation', status: 'pending' },
    { name: 'Image Data Storage', status: 'pending' },
    { name: 'PDF Generation (Declaration)', status: 'pending' },
    { name: 'PDF Generation (Rental Agreement)', status: 'pending' },
    { name: 'Network Retry Logic', status: 'pending' },
    { name: 'Session Data Management', status: 'pending' }
  ])
  
  const [isRunning, setIsRunning] = useState(false)
  const [progress, setProgress] = useState(0)
  const [overallResult, setOverallResult] = useState<'pending' | 'passed' | 'failed'>('pending')

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ))
  }

  const runTest = async (index: number, testFn: () => Promise<any>) => {
    const startTime = Date.now()
    updateTest(index, { status: 'running' })
    
    try {
      const result = await testFn()
      const duration = Date.now() - startTime
      updateTest(index, { 
        status: 'passed', 
        message: 'Test passed successfully',
        duration,
        details: result
      })
      return true
    } catch (error) {
      const duration = Date.now() - startTime
      updateTest(index, { 
        status: 'failed', 
        message: error instanceof Error ? error.message : 'Test failed',
        duration
      })
      return false
    }
  }

  const testDataStorage = async () => {
    const testCode = 'T-123456'
    const guestData = GuestDataStorage.initializeGuestData(testCode)
    
    if (!guestData.guestId || guestData.accommodationCode !== testCode) {
      throw new Error('Guest data initialization failed')
    }
    
    GuestDataStorage.saveToSession(guestData)
    const retrieved = GuestDataStorage.getFromSession()
    
    if (retrieved.guestId !== guestData.guestId) {
      throw new Error('Data persistence failed')
    }
    
    return { guestId: guestData.guestId, accommodationCode: testCode }
  }

  const testAccommodationCode = async () => {
    const validCodes = ['T-123456', 'T-789012', 'T-345678']
    const invalidCodes = ['123456', 'T-12345', 'INVALID']
    
    // Test valid codes
    for (const code of validCodes) {
      const regex = /^T-\d{6}$/
      if (!regex.test(code)) {
        throw new Error(`Valid code ${code} failed validation`)
      }
    }
    
    // Test invalid codes
    for (const code of invalidCodes) {
      const regex = /^T-\d{6}$/
      if (regex.test(code)) {
        throw new Error(`Invalid code ${code} passed validation`)
      }
    }
    
    return { validCodes, invalidCodes }
  }

  const testFormPersistence = async () => {
    const testData = {
      nom: 'Test',
      prenom: 'User',
      dateNaissance: '1990-01-01',
      numeroPieceIdentite: 'AB123456'
    }
    
    GuestDataStorage.updatePersonalInfo(testData)
    const retrieved = GuestDataStorage.getFromSession()
    
    if (retrieved.personalInfo?.nom !== testData.nom) {
      throw new Error('Form data persistence failed')
    }
    
    return testData
  }

  const testEncryption = async () => {
    const testData = { sensitive: 'data', id: '12345' }
    const encrypted = await DataEncryption.encryptGuestData(testData)
    
    if (!encrypted.encryptedData || !encrypted.keyData || !encrypted.iv) {
      throw new Error('Encryption failed')
    }
    
    const decrypted = await DataEncryption.decryptGuestData(
      encrypted.encryptedData,
      encrypted.keyData,
      encrypted.iv
    )
    
    if (decrypted.sensitive !== testData.sensitive) {
      throw new Error('Decryption failed')
    }
    
    return { encrypted: !!encrypted.encryptedData, decrypted: decrypted.sensitive === testData.sensitive }
  }

  const testLivenessDetection = async () => {
    const result = await performQuickLivenessCheck()
    
    if (!result.faceDetected || !result.timestamp) {
      throw new Error('Liveness detection failed')
    }
    
    return {
      faceDetected: result.faceDetected,
      blinkDetected: result.blinkDetected,
      headMovement: result.headMovementDetected,
      score: result.overallScore
    }
  }

  const testImageStorage = async () => {
    const testImageData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
    
    const imageId = await GuestDataStorage.saveImageData(testImageData, 'selfie')
    const retrieved = GuestDataStorage.getImageData(imageId)
    
    if (retrieved !== testImageData) {
      throw new Error('Image storage failed')
    }
    
    return { imageId, stored: !!retrieved }
  }

  const testPDFGeneration = async () => {
    const testData = {
      nom: 'Test',
      prenom: 'User',
      dateNaissance: '1990-01-01',
      numeroPieceIdentite: 'AB123456'
    }
    
    const result = await pdfRetryManager.generatePDFWithRetry(testData)
    
    if (!result.success || !result.data) {
      throw new Error('PDF generation failed')
    }
    
    return { 
      success: result.success, 
      attempts: result.attempts,
      size: result.data.size 
    }
  }

  const testRentalAgreement = async () => {
    const testData = {
      nom: 'Test',
      prenom: 'User',
      dateArrivee: '2024-01-01',
      dateDepart: '2024-01-07'
    }
    
    const result = await pdfRetryManager.generatePDFWithRetry(
      testData, 
      '/api/generate-rental-agreement'
    )
    
    if (!result.success || !result.data) {
      throw new Error('Rental agreement generation failed')
    }
    
    return { 
      success: result.success, 
      attempts: result.attempts,
      size: result.data.size 
    }
  }

  const testNetworkRetry = async () => {
    // Simulate network failure and retry
    let attempts = 0
    const mockFetch = async () => {
      attempts++
      if (attempts < 3) {
        throw new Error('Network error')
      }
      return { ok: true, data: 'success' }
    }
    
    try {
      await mockFetch()
      await mockFetch()
      const result = await mockFetch()
      return { attempts, success: result.data === 'success' }
    } catch (error) {
      throw new Error(`Retry logic failed after ${attempts} attempts`)
    }
  }

  const testSessionManagement = async () => {
    // Test session data lifecycle
    const testData = { test: 'data' }
    GuestDataStorage.saveToSession(testData)
    
    const retrieved = GuestDataStorage.getFromSession()
    if (!retrieved.test) {
      throw new Error('Session save failed')
    }
    
    GuestDataStorage.clearSession()
    const cleared = GuestDataStorage.getFromSession()
    if (Object.keys(cleared).length > 0) {
      throw new Error('Session clear failed')
    }
    
    return { saved: true, cleared: true }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setProgress(0)
    setOverallResult('pending')
    
    const testFunctions = [
      testDataStorage,
      testAccommodationCode,
      testFormPersistence,
      testEncryption,
      testLivenessDetection,
      testImageStorage,
      testPDFGeneration,
      testRentalAgreement,
      testNetworkRetry,
      testSessionManagement
    ]
    
    let passed = 0
    let failed = 0
    
    for (let i = 0; i < testFunctions.length; i++) {
      const success = await runTest(i, testFunctions[i])
      if (success) {
        passed++
      } else {
        failed++
      }
      
      setProgress(((i + 1) / testFunctions.length) * 100)
      
      // Small delay between tests for better UX
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    setOverallResult(failed === 0 ? 'passed' : 'failed')
    setIsRunning(false)
  }

  const resetTests = () => {
    setTests(prev => prev.map(test => ({ 
      ...test, 
      status: 'pending' as const,
      message: undefined,
      duration: undefined,
      details: undefined
    })))
    setProgress(0)
    setOverallResult('pending')
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'failed': return <XCircle className="w-4 h-4 text-red-600" />
      case 'running': return <Clock className="w-4 h-4 text-blue-600 animate-spin" />
      default: return <div className="w-4 h-4 border border-gray-300 rounded-full" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return 'text-green-600 bg-green-50 border-green-200'
      case 'failed': return 'text-red-600 bg-red-50 border-red-200'
      case 'running': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card className="enhanced-card">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-3">
              <Shield className="w-8 h-8 text-institutional-primary" />
              Test de Parcours Invité
            </CardTitle>
            <p className="text-gray-600">
              Validation complète du système de télédéclaration
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progression des tests</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
            
            {/* Controls */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={runAllTests}
                disabled={isRunning}
                className="bg-institutional-primary hover:bg-green-600"
              >
                {isRunning ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Tests en cours...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Lancer tous les tests
                  </>
                )}
              </Button>
              
              <Button
                onClick={resetTests}
                disabled={isRunning}
                variant="outline"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Réinitialiser
              </Button>
              
              <Button
                onClick={() => router.push('/')}
                variant="outline"
              >
                Retour à l'accueil
              </Button>
            </div>
            
            {/* Overall Result */}
            {overallResult !== 'pending' && (
              <div className={`p-4 rounded-lg border-2 ${
                overallResult === 'passed' 
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <div className="flex items-center gap-2">
                  {overallResult === 'passed' ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <AlertTriangle className="w-5 h-5" />
                  )}
                  <span className="font-semibold">
                    {overallResult === 'passed' 
                      ? '✅ Tous les tests sont passés! Le parcours invité est opérationnel.'
                      : '❌ Certains tests ont échoué. Vérifiez l\'implémentation.'
                    }
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Results */}
        <div className="grid gap-4">
          {tests.map((test, index) => (
            <Card key={index} className={`border-2 ${getStatusColor(test.status)}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <h3 className="font-medium">{test.name}</h3>
                      {test.message && (
                        <p className="text-sm opacity-75">{test.message}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    {test.duration && (
                      <Badge variant="outline" className="text-xs">
                        {test.duration}ms
                      </Badge>
                    )}
                  </div>
                </div>
                
                {test.details && (
                  <div className="mt-3 p-2 bg-white/50 rounded text-xs">
                    <pre>{JSON.stringify(test.details, null, 2)}</pre>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
