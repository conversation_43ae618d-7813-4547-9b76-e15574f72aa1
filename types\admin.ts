export interface AdminHost {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  cin: string;
  ice: string;
  ville: string;
  region: string;
  adresse: string;
  dateInscription: string;
  statut: 'actif' | 'inactif';
  nombreLogements: number;
  totalDeclarations: number;
  derniereConnexion: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface AdminAccommodation {
  id: string;
  hostId: number;
  nom: string;
  type: string;
  ville: string;
  region: string;
  adresse: string;
  capacite: number;
  occupationActuelle: number;
  statut: 'occupé' | 'libre';
  tarif: number;
  codeLogement: string;
  dateCreation: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  amenities: string[];
  photos: string[];
  description: string;
  totalDeclarations: number;
}

export interface AdminDeclaration {
  id: number;
  guestId: string;
  accommodationId: string;
  hostId: number;

  // Guest personal info
  nom: string;
  prenom: string;
  dateNaissance: string;
  sexe: 'M' | 'F';
  nationalite: string;
  numeroPieceIdentite: string;
  typePieceIdentite: string;
  telephone: string;
  email: string;

  // Stay information
  dateArrivee: string;
  heureArrivee: string;
  dateDepart: string;
  heureDepart: string;
  motifSejour: string;
  nombrePersonnes: number;

  // Location info
  ville: string;
  region: string;
  nomLogement: string;
  adresseLogement: string;

  // Declaration info
  dateDeclaration: string;
  heureDeclaration: string;
  statut: 'en_cours' | 'validée' | 'expirée';

  // Verification status
  verificationStatut: 'en_attente' | 'vérifié' | 'rejeté';
  documentsVerifies: boolean;

  // Law Enforcement Specific Fields
  securityFlags: {
    isWatchListed: boolean;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    flaggedReasons: string[];
    lastSecurityCheck: string;
  };

  // Document verification
  documentVerification: {
    idPhotoUrl: string;
    idBackPhotoUrl?: string;
    signatureUrl: string;
    selfieUrl: string;
    biometricData?: {
      fingerprint?: string;
      faceRecognition?: string;
    };
    verificationHistory: {
      timestamp: string;
      verifiedBy: string;
      status: 'approved' | 'rejected' | 'pending';
      notes: string;
    }[];
  };

  // Additional security information
  travelHistory: {
    previousCountries: string[];
    entryPoints: string[];
    visaStatus: string;
    passportExpiry?: string;
  };

  // Emergency contacts
  emergencyContacts: {
    name: string;
    relationship: string;
    phone: string;
    address: string;
  }[];

  // Investigation notes (police use)
  investigationNotes: {
    id: string;
    timestamp: string;
    officer: string;
    note: string;
    category: 'general' | 'suspicious' | 'verified' | 'follow-up';
  }[];

  // Coordinates for mapping
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface CityStats {
  ville: string;
  region: string;
  totalAccommodations: number;
  totalDeclarations: number;
  activeHosts: number;
  occupancyRate: number;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface DashboardStats {
  totalDeclarations: {
    today: number;
    week: number;
    month: number;
    total: number;
  };
  totalAccommodations: number;
  totalHosts: number;
  occupancyRate: number;
  topCities: CityStats[];
  recentDeclarations: AdminDeclaration[];
}

export interface SearchFilters {
  searchTerm?: string;
  ville?: string;
  region?: string;
  statut?: string;
  dateDebut?: string;
  dateFin?: string;
  hostId?: number;
  accommodationId?: string;
}

export interface AdminSearchResult {
  type: 'host' | 'accommodation' | 'declaration' | 'guest';
  id: string | number;
  title: string;
  subtitle: string;
  details: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface MapMarker {
  id: string;
  type: 'accommodation' | 'declaration';
  coordinates: {
    lat: number;
    lng: number;
  };
  title: string;
  subtitle: string;
  details: any;
  count?: number;
}

export interface AdminNotification {
  id: string;
  type: 'warning' | 'info' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  dateRange: {
    start: string;
    end: string;
  };
  filters: SearchFilters;
  includePersonalData: boolean;
}
