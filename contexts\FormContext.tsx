'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import type { FormData, FormStep } from '../types/form'

export type UserType = 'guest' | 'host' | null

interface FormContextType {
  formData: Partial<FormData>;
  currentStep: FormStep;
  userType: UserType;
  updateFormData: (data: Partial<FormData>) => void;
  setUserType: (type: UserType) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const FormContext = createContext<FormContextType | undefined>(undefined)

export function FormProvider({ children }: { children: React.ReactNode }) {
  const [formData, setFormData] = useState<Partial<FormData>>({})
  const [currentStep, setCurrentStep] = useState<FormStep>(1)
  const [userType, setUserTypeState] = useState<UserType>(null)

  // Initialize user type from session storage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserType = sessionStorage.getItem('userType') as UserType
      if (storedUserType) {
        setUserTypeState(storedUserType)
      }
    }
  }, [])

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...data }))
  }

  const setUserType = (type: UserType) => {
    setUserTypeState(type)
    if (typeof window !== 'undefined') {
      if (type) {
        sessionStorage.setItem('userType', type)
      } else {
        sessionStorage.removeItem('userType')
      }
    }
  }

  const nextStep = () => {
    setCurrentStep(prev => (prev < 4 ? (prev + 1) as FormStep : prev))
  }

  const prevStep = () => {
    setCurrentStep(prev => (prev > 1 ? (prev - 1) as FormStep : prev))
  }

  return (
    <FormContext.Provider value={{
      formData,
      currentStep,
      userType,
      updateFormData,
      setUserType,
      nextStep,
      prevStep
    }}>
      {children}
    </FormContext.Provider>
  )
}

export const useForm = () => {
  const context = useContext(FormContext)
  if (!context) throw new Error('useForm must be used within FormProvider')
  return context
}

