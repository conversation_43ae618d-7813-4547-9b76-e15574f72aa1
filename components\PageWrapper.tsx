'use client'

import { ReactNode } from 'react'
import { InstitutionalHeader } from './InstitutionalHeader'
import { useUserType } from '@/hooks/use-user-type'

interface PageWrapperProps {
  children: ReactNode
  className?: string
  showMobileActions?: boolean
  forceUserType?: 'guest' | 'host'
}

export function PageWrapper({ 
  children, 
  className = "min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background",
  showMobileActions,
  forceUserType
}: PageWrapperProps) {
  const { userType } = useUserType()
  
  // Use forced user type if provided, otherwise use detected user type
  const effectiveUserType = forceUserType || userType || 'guest'
  
  // Determine if mobile actions should be shown
  const shouldShowMobileActions = showMobileActions !== undefined 
    ? showMobileActions 
    : effectiveUserType === 'host'

  return (
    <div className={className}>
      <InstitutionalHeader 
        userType={effectiveUserType} 
        showMobileActions={shouldShowMobileActions} 
      />
      {children}
    </div>
  )
}
