'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { useForm } from '@/contexts/FormContext'

export default function TestHeadersPage() {
  const { userType, setUserType } = useForm()
  const [currentDemo, setCurrentDemo] = useState<'guest' | 'host'>('guest')

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      {/* Demo Header */}
      <InstitutionalHeader 
        userType={currentDemo} 
        showMobileActions={currentDemo === 'host'} 
      />
      
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">
              🧪 Header Configuration Test
            </CardTitle>
            <p className="text-center text-gray-600">
              Test the different header configurations for guest and host user journeys
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Current Status */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">Current Status:</h3>
              <p className="text-blue-800">
                <strong>Context User Type:</strong> {userType || 'Not set'}<br/>
                <strong>Demo Header Type:</strong> {currentDemo}<br/>
                <strong>Mobile Actions Shown:</strong> {currentDemo === 'host' ? 'Yes' : 'No'}
              </p>
            </div>

            {/* Demo Controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Demo Header Types:</h3>
              <div className="flex gap-4">
                <Button
                  onClick={() => setCurrentDemo('guest')}
                  variant={currentDemo === 'guest' ? 'default' : 'outline'}
                  className="flex-1"
                >
                  👤 Guest Header (Minimal)
                </Button>
                <Button
                  onClick={() => setCurrentDemo('host')}
                  variant={currentDemo === 'host' ? 'default' : 'outline'}
                  className="flex-1"
                >
                  🏠 Host Header (Full Features)
                </Button>
              </div>
            </div>

            {/* Context Controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Set Context User Type:</h3>
              <div className="flex gap-4">
                <Button
                  onClick={() => setUserType('guest')}
                  variant={userType === 'guest' ? 'default' : 'outline'}
                  className="flex-1"
                >
                  Set as Guest
                </Button>
                <Button
                  onClick={() => setUserType('host')}
                  variant={userType === 'host' ? 'default' : 'outline'}
                  className="flex-1"
                >
                  Set as Host
                </Button>
                <Button
                  onClick={() => setUserType(null)}
                  variant={userType === null ? 'default' : 'outline'}
                  className="flex-1"
                >
                  Clear Type
                </Button>
              </div>
            </div>

            {/* Feature Comparison */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">👤 Guest Journey</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>✅ Institutional branding only</li>
                    <li>❌ No search functionality</li>
                    <li>❌ No notification bell</li>
                    <li>❌ No profile menu</li>
                    <li>🎯 Focused on declaration process</li>
                    <li>📱 Clean mobile interface</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">🏠 Host Journey</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>✅ Full institutional branding</li>
                    <li>✅ Search functionality</li>
                    <li>✅ Notification bell</li>
                    <li>✅ Profile menu</li>
                    <li>🎯 Complete management features</li>
                    <li>📱 Full mobile actions</li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Navigation Links */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Test Navigation:</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button variant="outline" onClick={() => window.location.href = '/'}>
                  🏠 Welcome (Guest)
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/login'}>
                  🔐 Login (Guest)
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/dashboard'}>
                  📊 Dashboard (Host)
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/gestion-logements'}>
                  🏢 Properties (Host)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
