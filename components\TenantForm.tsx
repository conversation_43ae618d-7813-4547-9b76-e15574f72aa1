'use client'

import { useForm } from '../contexts/FormContext'
import { PersonalInfoStep } from './steps/PersonalInfoStep'
import { TravelInfoStep } from './steps/TravelInfoStep'
import { ContactInfoStep } from './steps/ContactInfoStep'
import { SummaryStep } from './steps/SummaryStep'
import { Progress } from '@/components/ui/progress'

export function TenantForm() {
  const { currentStep } = useForm()

  const stepTitles = [
    'Informations personnelles',
    'Informations de voyage',
    'Informations de contact',
    'Résumé'
  ]

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Progress Header */}
      <div className="mb-8 bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {stepTitles[currentStep - 1]}
          </h2>
          <p className="text-sm text-gray-600">
            Étape {currentStep} sur 4
          </p>
        </div>

        <Progress
          value={(currentStep / 4) * 100}
          className="h-3 bg-gray-100 rounded-full"
        />

        <div className="flex justify-between mt-4">
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm transition-all duration-200 ${
                step <= currentStep
                  ? 'bg-institutional-primary text-white shadow-md'
                  : 'bg-gray-100 text-gray-400'
              }`}
            >
              {step}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        {currentStep === 1 && <PersonalInfoStep />}
        {currentStep === 2 && <TravelInfoStep />}
        {currentStep === 3 && <ContactInfoStep />}
        {currentStep === 4 && <SummaryStep />}
      </div>
    </div>
  )
}

