/**
 * Data storage utilities for guest declarations and documents
 */

import { DataEncryption, generateGuestId, generateFileName } from './encryption'

export interface GuestData {
  guestId: string
  accommodationCode: string
  personalInfo: {
    nom: string
    prenom: string
    dateNaissance: string
    sexe: string
    lieu: string
    numeroPieceIdentite: string
    typePieceIdentite: string
    pays: string
    villeResidence: string
    domicileHabituel: string
    categorieSocioPro: string
  }
  stayInfo: {
    dateArrivee: string
    dateDepart: string
    motifSejour: string
  }
  verification: {
    idRectoUrl?: string
    idVersoUrl?: string
    selfieUrl?: string
    signatureUrl?: string
    nfcData?: any
    livenessChecks?: {
      blinkDetected: boolean
      headMovementDetected: boolean
    }
  }
  timestamps: {
    created: string
    lastModified: string
    submitted?: string
  }
  status: 'draft' | 'verified' | 'submitted' | 'completed'
}

export class GuestDataStorage {
  /**
   * Save guest data to session storage (temporary)
   */
  static saveToSession(guestData: Partial<GuestData>): void {
    try {
      const existingData = this.getFromSession()
      const updatedData = { ...existingData, ...guestData }
      sessionStorage.setItem('guestDeclarationData', JSON.stringify(updatedData))
    } catch (error) {
      console.error('Error saving to session storage:', error)
    }
  }

  /**
   * Get guest data from session storage
   */
  static getFromSession(): Partial<GuestData> {
    try {
      const data = sessionStorage.getItem('guestDeclarationData')
      return data ? JSON.parse(data) : {}
    } catch (error) {
      console.error('Error reading from session storage:', error)
      return {}
    }
  }

  /**
   * Clear session storage
   */
  static clearSession(): void {
    try {
      sessionStorage.removeItem('guestDeclarationData')
    } catch (error) {
      console.error('Error clearing session storage:', error)
    }
  }

  /**
   * Initialize new guest data
   */
  static initializeGuestData(accommodationCode: string): GuestData {
    const guestId = generateGuestId()
    const now = new Date().toISOString()

    return {
      guestId,
      accommodationCode,
      personalInfo: {
        nom: '',
        prenom: '',
        dateNaissance: '',
        sexe: '',
        lieu: '',
        numeroPieceIdentite: '',
        typePieceIdentite: '',
        pays: '',
        villeResidence: '',
        domicileHabituel: '',
        categorieSocioPro: ''
      },
      stayInfo: {
        dateArrivee: '',
        dateDepart: '',
        motifSejour: ''
      },
      verification: {},
      timestamps: {
        created: now,
        lastModified: now
      },
      status: 'draft'
    }
  }

  /**
   * Update guest data with form information
   */
  static updatePersonalInfo(formData: any): void {
    const existingData = this.getFromSession()
    const updatedData: Partial<GuestData> = {
      ...existingData,
      personalInfo: {
        nom: formData.nom || '',
        prenom: formData.prenom || '',
        dateNaissance: formData.dateNaissance || '',
        sexe: formData.sexe || '',
        lieu: formData.lieu || '',
        numeroPieceIdentite: formData.numeroPieceIdentite || '',
        typePieceIdentite: formData.typePieceIdentite || '',
        pays: formData.pays || '',
        villeResidence: formData.villeResidence || '',
        domicileHabituel: formData.domicileHabituel || '',
        categorieSocioPro: formData.categorieSocioPro || ''
      },
      stayInfo: {
        dateArrivee: formData.dateArrivee || '',
        dateDepart: formData.dateDepart || '',
        motifSejour: formData.motifSejour || ''
      },
      timestamps: {
        ...existingData.timestamps,
        lastModified: new Date().toISOString()
      }
    }

    this.saveToSession(updatedData)
  }

  /**
   * Update verification data
   */
  static updateVerificationData(verificationData: Partial<GuestData['verification']>): void {
    const existingData = this.getFromSession()
    const updatedData: Partial<GuestData> = {
      ...existingData,
      verification: {
        ...existingData.verification,
        ...verificationData
      },
      timestamps: {
        ...existingData.timestamps,
        lastModified: new Date().toISOString()
      }
    }

    this.saveToSession(updatedData)
  }

  /**
   * Mark declaration as submitted
   */
  static markAsSubmitted(): void {
    const existingData = this.getFromSession()
    const updatedData: Partial<GuestData> = {
      ...existingData,
      status: 'submitted',
      timestamps: {
        ...existingData.timestamps,
        submitted: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }
    }

    this.saveToSession(updatedData)
  }

  /**
   * Save image data (base64) with encryption
   */
  static async saveImageData(imageData: string, type: 'recto' | 'verso' | 'selfie' | 'signature'): Promise<string> {
    try {
      const guestData = this.getFromSession()
      const guestId = guestData.guestId || generateGuestId()
      
      // In a real implementation, you would save this to a secure server
      // For now, we'll store it in session storage with a reference
      const imageId = `${type}_${Date.now()}_${guestId}`
      
      // Store the image data temporarily
      sessionStorage.setItem(`image_${imageId}`, imageData)
      
      // Update the guest data with the image reference
      const verificationUpdate: Partial<GuestData['verification']> = {}
      verificationUpdate[`${type}Url` as keyof GuestData['verification']] = imageId
      
      this.updateVerificationData(verificationUpdate)
      
      return imageId
    } catch (error) {
      console.error('Error saving image data:', error)
      throw error
    }
  }

  /**
   * Get image data by ID
   */
  static getImageData(imageId: string): string | null {
    try {
      return sessionStorage.getItem(`image_${imageId}`)
    } catch (error) {
      console.error('Error getting image data:', error)
      return null
    }
  }

  /**
   * Encrypt and prepare data for final submission
   */
  static async prepareForSubmission(): Promise<{
    encryptedData: string
    keyData: string
    iv: string
    guestId: string
  }> {
    const guestData = this.getFromSession()
    
    if (!guestData.guestId) {
      throw new Error('No guest data found')
    }

    // Encrypt the complete guest data
    const encryptionResult = await DataEncryption.encryptGuestData(guestData)
    
    return {
      ...encryptionResult,
      guestId: guestData.guestId
    }
  }

  /**
   * Generate file names for different document types
   */
  static generateFileNames(guestId: string) {
    return {
      declaration: generateFileName('declaration', guestId, 'pdf'),
      rentalAgreement: generateFileName('rental_agreement', guestId, 'pdf'),
      encryptedData: generateFileName('encrypted', guestId, 'enc'),
      jsonData: generateFileName('data', guestId, 'json')
    }
  }
}
