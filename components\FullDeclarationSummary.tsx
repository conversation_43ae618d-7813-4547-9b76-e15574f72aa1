import Image from 'next/image'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface FullDeclarationSummaryProps {
  declaration: any // Replace 'any' with a proper type if you have one
}

export function FullDeclarationSummary({ declaration }: FullDeclarationSummaryProps) {
  if (!declaration) {
    return <div>No declaration data available</div>;
  }
  return (
    <div className="container mx-auto py-8 logo-background-content">
      <Card className="w-full max-w-3xl mx-auto enhanced-card">
        <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-2xl font-semibold">
          Résumé Complet de la Déclaration
        </CardTitle>
        <Link href="/recherche-nuites" passHref>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
        </Link>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold">Nom</h3>
            <p>{declaration?.nom ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Prénom</h3>
            <p>{declaration?.prenom ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date de naissance</h3>
            <p>{declaration?.dateNaissance ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Numéro de pièce d&apos;identité</h3>
            <p>{declaration?.numeroPieceIdentite ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Type de pièce d&apos;identité</h3>
            <p>{declaration?.typePieceIdentite ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date d&apos;arrivée</h3>
            <p>{declaration?.dateArrivee ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date de départ</h3>
            <p>{declaration?.dateDepart ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Logement</h3>
            <p>{declaration?.nomLogement ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Ville</h3>
            <p>{declaration?.ville ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Nombre d'enfants</h3>
            <p>{declaration?.enfants ?? 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-semibold">Date de traitement</h3>
            <p>{declaration?.dateTraitement ?? 'Not specified'}</p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold">Photos d&apos;identité</h3>
          <div className="flex flex-wrap gap-4">
            <div>
              <p className="mb-2">Recto</p>
              <Image 
                src={declaration?.idRectoUrl ?? "/placeholder.svg?height=200&width=320"} 
                alt="ID Recto" 
                width={320} 
                height={200} 
                className="rounded-lg" 
              />
            </div>
            <div>
              <p className="mb-2">Verso</p>
              <Image 
                src={declaration?.idVersoUrl ?? "/placeholder.svg?height=200&width=320"} 
                alt="ID Verso" 
                width={320} 
                height={200} 
                className="rounded-lg" 
              />
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Signature du voyageur</h3>
          <Image 
            src={declaration?.signatureUrl ?? "/placeholder.svg?height=100&width=300"} 
            alt="Signature" 
            width={300} 
            height={100} 
            className="rounded-lg" 
          />
        </div>
      </CardContent>
    </Card>
    </div>
  )
}

