'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  Settings, User, Bell, Shield, Palette, Globe, Database,
  Save, Eye, EyeOff, Smartphone, Mail, Lock, AlertTriangle
} from 'lucide-react'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export default function ParametresPage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [settings, setSettings] = useState({
    // Profile settings
    name: 'Marie Dubois',
    email: '<EMAIL>',
    phone: '+33 6 12 34 56 78',
    language: 'fr',
    timezone: 'Europe/Paris',
    
    // Notification settings
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    weeklyReports: true,
    monthlyReports: true,
    
    // Security settings
    twoFactorAuth: false,
    sessionTimeout: '30',
    loginAlerts: true,
    
    // Appearance settings
    theme: 'light',
    compactMode: false,
    showAnimations: true,
    
    // Privacy settings
    dataSharing: false,
    analytics: true,
    cookies: true
  })

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = () => {
    // Here you would typically save to your backend
    console.log('Saving settings:', settings)
    // Show success message
  }

  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Paramètres
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos préférences et paramètres de compte
                  </p>
                </div>
                <Button 
                  className="bg-institutional-primary hover:bg-green-600 text-white"
                  onClick={handleSave}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Sauvegarder
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              {/* Main Content */}
              <Card className="enhanced-card">
                <CardContent className="p-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <div className="border-b border-gray-200 px-6 pt-6">
                      <TabsList className="grid w-full grid-cols-5 bg-gray-100 rounded-lg p-1">
                        <TabsTrigger value="profile">Profil</TabsTrigger>
                        <TabsTrigger value="notifications">Notifications</TabsTrigger>
                        <TabsTrigger value="security">Sécurité</TabsTrigger>
                        <TabsTrigger value="appearance">Apparence</TabsTrigger>
                        <TabsTrigger value="privacy">Confidentialité</TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="profile" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Informations Personnelles</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="name">Nom complet</Label>
                              <Input
                                id="name"
                                value={settings.name}
                                onChange={(e) => handleSettingChange('name', e.target.value)}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor="email">Email</Label>
                              <Input
                                id="email"
                                type="email"
                                value={settings.email}
                                onChange={(e) => handleSettingChange('email', e.target.value)}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor="phone">Téléphone</Label>
                              <Input
                                id="phone"
                                value={settings.phone}
                                onChange={(e) => handleSettingChange('phone', e.target.value)}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor="language">Langue</Label>
                              <Select value={settings.language} onValueChange={(value) => handleSettingChange('language', value)}>
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="fr">Français</SelectItem>
                                  <SelectItem value="en">English</SelectItem>
                                  <SelectItem value="es">Español</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="md:col-span-2">
                              <Label htmlFor="timezone">Fuseau horaire</Label>
                              <Select value={settings.timezone} onValueChange={(value) => handleSettingChange('timezone', value)}>
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Europe/Paris">Europe/Paris (GMT+1)</SelectItem>
                                  <SelectItem value="Europe/London">Europe/London (GMT+0)</SelectItem>
                                  <SelectItem value="America/New_York">America/New_York (GMT-5)</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Changer le Mot de Passe</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="currentPassword">Mot de passe actuel</Label>
                              <div className="relative mt-1">
                                <Input
                                  id="currentPassword"
                                  type={showPassword ? "text" : "password"}
                                  placeholder="Mot de passe actuel"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3"
                                  onClick={() => setShowPassword(!showPassword)}
                                >
                                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                </Button>
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="newPassword">Nouveau mot de passe</Label>
                              <Input
                                id="newPassword"
                                type="password"
                                placeholder="Nouveau mot de passe"
                                className="mt-1"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="notifications" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Préférences de Notification</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Mail className="w-5 h-5 text-gray-600" />
                                <div>
                                  <p className="font-medium text-gray-900">Notifications par email</p>
                                  <p className="text-sm text-gray-600">Recevoir les notifications importantes par email</p>
                                </div>
                              </div>
                              <Switch
                                checked={settings.emailNotifications}
                                onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Smartphone className="w-5 h-5 text-gray-600" />
                                <div>
                                  <p className="font-medium text-gray-900">Notifications push</p>
                                  <p className="text-sm text-gray-600">Recevoir des notifications sur votre appareil</p>
                                </div>
                              </div>
                              <Switch
                                checked={settings.pushNotifications}
                                onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Smartphone className="w-5 h-5 text-gray-600" />
                                <div>
                                  <p className="font-medium text-gray-900">Notifications SMS</p>
                                  <p className="text-sm text-gray-600">Recevoir des SMS pour les alertes urgentes</p>
                                </div>
                              </div>
                              <Switch
                                checked={settings.smsNotifications}
                                onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
                              />
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rapports Automatiques</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Rapports hebdomadaires</p>
                                <p className="text-sm text-gray-600">Recevoir un résumé chaque semaine</p>
                              </div>
                              <Switch
                                checked={settings.weeklyReports}
                                onCheckedChange={(checked) => handleSettingChange('weeklyReports', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Rapports mensuels</p>
                                <p className="text-sm text-gray-600">Recevoir un rapport détaillé chaque mois</p>
                              </div>
                              <Switch
                                checked={settings.monthlyReports}
                                onCheckedChange={(checked) => handleSettingChange('monthlyReports', checked)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="security" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sécurité du Compte</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Shield className="w-5 h-5 text-gray-600" />
                                <div>
                                  <p className="font-medium text-gray-900">Authentification à deux facteurs</p>
                                  <p className="text-sm text-gray-600">Sécuriser votre compte avec 2FA</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={settings.twoFactorAuth}
                                  onCheckedChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                                />
                                {settings.twoFactorAuth && (
                                  <Badge className="bg-green-100 text-green-800 border-green-200">Activé</Badge>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Lock className="w-5 h-5 text-gray-600" />
                                <div>
                                  <p className="font-medium text-gray-900">Alertes de connexion</p>
                                  <p className="text-sm text-gray-600">Être notifié des nouvelles connexions</p>
                                </div>
                              </div>
                              <Switch
                                checked={settings.loginAlerts}
                                onCheckedChange={(checked) => handleSettingChange('loginAlerts', checked)}
                              />
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sessions</h3>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="sessionTimeout">Délai d'expiration de session (minutes)</Label>
                              <Select value={settings.sessionTimeout} onValueChange={(value) => handleSettingChange('sessionTimeout', value)}>
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="15">15 minutes</SelectItem>
                                  <SelectItem value="30">30 minutes</SelectItem>
                                  <SelectItem value="60">1 heure</SelectItem>
                                  <SelectItem value="120">2 heures</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="appearance" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Thème</h3>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="theme">Apparence</Label>
                              <Select value={settings.theme} onValueChange={(value) => handleSettingChange('theme', value)}>
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="light">Clair</SelectItem>
                                  <SelectItem value="dark">Sombre</SelectItem>
                                  <SelectItem value="auto">Automatique</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Interface</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Mode compact</p>
                                <p className="text-sm text-gray-600">Affichage plus dense des informations</p>
                              </div>
                              <Switch
                                checked={settings.compactMode}
                                onCheckedChange={(checked) => handleSettingChange('compactMode', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Animations</p>
                                <p className="text-sm text-gray-600">Activer les animations de l'interface</p>
                              </div>
                              <Switch
                                checked={settings.showAnimations}
                                onCheckedChange={(checked) => handleSettingChange('showAnimations', checked)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="privacy" className="p-6 space-y-6">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Confidentialité des Données</h3>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Partage de données</p>
                                <p className="text-sm text-gray-600">Autoriser le partage de données anonymisées</p>
                              </div>
                              <Switch
                                checked={settings.dataSharing}
                                onCheckedChange={(checked) => handleSettingChange('dataSharing', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Analytics</p>
                                <p className="text-sm text-gray-600">Collecter des données d'utilisation pour améliorer l'application</p>
                              </div>
                              <Switch
                                checked={settings.analytics}
                                onCheckedChange={(checked) => handleSettingChange('analytics', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-gray-900">Cookies</p>
                                <p className="text-sm text-gray-600">Autoriser les cookies pour améliorer l'expérience</p>
                              </div>
                              <Switch
                                checked={settings.cookies}
                                onCheckedChange={(checked) => handleSettingChange('cookies', checked)}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="w-5 h-5 text-red-600" />
                            <p className="font-medium text-red-900">Zone de Danger</p>
                          </div>
                          <p className="text-sm text-red-700 mb-4">
                            Ces actions sont irréversibles. Assurez-vous de comprendre les conséquences.
                          </p>
                          <div className="space-y-2">
                            <Button variant="destructive" size="sm">
                              Supprimer toutes les données
                            </Button>
                            <Button variant="destructive" size="sm">
                              Supprimer le compte
                            </Button>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </div>
  )
}
