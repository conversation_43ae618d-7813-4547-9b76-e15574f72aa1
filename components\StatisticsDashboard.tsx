'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, BarChart2, BedDouble, Calendar, Home, Percent, TrendingUp, Users, Wifi, Wind, ChefHat, Car, MapPin, Download, RefreshCw, Filter } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Legend,
  ComposedChart,
} from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { RevPARDashboard } from './RevPARDashboard'
import { RateAnalysisDashboard } from './RateAnalysisDashboard'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { SummaryStats } from "./SummaryStats"
import { MapView } from './MapView'

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#FF99E6']

export function StatisticsDashboard() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [timeRange, setTimeRange] = useState('year')
  const [isLoading, setIsLoading] = useState(false)
  const averageRevenue = 825000; // Example value, adjust as needed

  const revenueByPropertyType = [
    { type: 'Apartment', revenue: 550000 },  // 50,000 * 11
    { type: 'House', revenue: 825000 },      // 75,000 * 11
    { type: 'Villa', revenue: 1100000 },     // 100,000 * 11
    { type: 'Condo', revenue: 660000 },      // 60,000 * 11
  ];

  const monthlyRevenue = [
    { month: 'Jan', revenue: 550000 },
    { month: 'Feb', revenue: 605000 },
    { month: 'Mar', revenue: 660000 },
    { month: 'Apr', revenue: 715000 },
    { month: 'May', revenue: 770000 },
    { month: 'Jun', revenue: 825000 },
    { month: 'Jul', revenue: 880000 },
    { month: 'Aug', revenue: 935000 },
    { month: 'Sep', revenue: 880000 },
    { month: 'Oct', revenue: 825000 },
    { month: 'Nov', revenue: 770000 },
    { month: 'Dec', revenue: 715000 },
  ];

  const revenueByBedroom = [
    { bedrooms: '1', revenue: 440000 },
    { bedrooms: '2', revenue: 660000 },
    { bedrooms: '3', revenue: 880000 },
    { bedrooms: '4+', revenue: 1100000 },
  ];

  const revenueByRealEstateType = [
    { name: 'Residential', revenue: 2200000 },
    { name: 'Commercial', revenue: 1650000 },
    { name: 'Industrial', revenue: 1100000 },
    { name: 'Land', revenue: 550000 },
  ];

  const revenueDistribution = [
    { range: '0-1000', count: 50 },
    { range: '1001-2000', count: 100 },
    { range: '2001-3000', count: 150 },
    { range: '3001-4000', count: 120 },
    { range: '4001-5000', count: 80 },
    { range: '5001+', count: 50 },
  ];

  // New data for Occupancy tab
  const occupancyRateData = [
    { month: 'Jan', rate: 65 },
    { month: 'Feb', rate: 70 },
    { month: 'Mar', rate: 75 },
    { month: 'Apr', rate: 80 },
    { month: 'May', rate: 85 },
    { month: 'Jun', rate: 90 },
    { month: 'Jul', rate: 95 },
    { month: 'Aug', rate: 100 },
    { month: 'Sep', rate: 90 },
    { month: 'Oct', rate: 85 },
    { month: 'Nov', rate: 75 },
    { month: 'Dec', rate: 70 },
  ];

  const lengthOfStayData = [
    { length: '1-3 nights', percentage: 30 },
    { length: '4-7 nights', percentage: 40 },
    { length: '8-14 nights', percentage: 20 },
    { length: '15-30 nights', percentage: 8 },
    { length: '30+ nights', percentage: 2 },
  ];

  const bookingLeadTimeData = [
    { leadTime: 'Same day', percentage: 5 },
    { leadTime: '1-7 days', percentage: 25 },
    { leadTime: '8-30 days', percentage: 40 },
    { leadTime: '31-90 days', percentage: 20 },
    { leadTime: '90+ days', percentage: 10 },
  ];

  const monthlyOccupancyData = [
    { month: 'Jan', occupiedDays: 20 },
    { month: 'Feb', occupiedDays: 22 },
    { month: 'Mar', occupiedDays: 24 },
    { month: 'Apr', occupiedDays: 25 },
    { month: 'May', occupiedDays: 27 },
    { month: 'Jun', occupiedDays: 28 },
    { month: 'Jul', occupiedDays: 30 },
    { month: 'Aug', occupiedDays: 31 },
    { month: 'Sep', occupiedDays: 28 },
    { month: 'Oct', occupiedDays: 26 },
    { month: 'Nov', occupiedDays: 23 },
    { month: 'Dec', occupiedDays: 21 },
  ];

  const bedroomDistributionData = [
    { bedrooms: '1', percentage: 30 },
    { bedrooms: '2', percentage: 40 },
    { bedrooms: '3', percentage: 20 },
    { bedrooms: '4+', percentage: 10 },
  ];

  const demandOverTimeData = [
    { month: 'Jan', demand: 70 },
    { month: 'Feb', demand: 75 },
    { month: 'Mar', demand: 80 },
    { month: 'Apr', demand: 85 },
    { month: 'May', demand: 90 },
    { month: 'Jun', demand: 95 },
    { month: 'Jul', demand: 100 },
    { month: 'Aug', demand: 100 },
    { month: 'Sep', demand: 95 },
    { month: 'Oct', demand: 90 },
    { month: 'Nov', demand: 80 },
    { month: 'Dec', demand: 75 },
  ];

  const futureReservationsData = [
    { month: 'Next Month', bookings: 100 },
    { month: '2 Months', bookings: 80 },
    { month: '3 Months', bookings: 60 },
    { month: '4 Months', bookings: 40 },
    { month: '5 Months', bookings: 30 },
    { month: '6+ Months', bookings: 20 },
  ];

  const listingsByRentalChannel = [
    { name: 'Direct', value: 35 },
    { name: 'Airbnb', value: 45 },
    { name: 'Booking', value: 20 },
  ];

  const listingsByRentalSize = [
    { name: '1 Bedroom', value: 30 },
    { name: '2 Bedrooms', value: 35 },
    { name: '3 Bedrooms', value: 20 },
    { name: '4+ Bedrooms', value: 15 },
  ];

  const rentalGrowthData = [
    { month: 'Jan 2021', value: 100 },
    { month: 'Jul 2021', value: 120 },
    { month: 'Jan 2022', value: 150 },
    { month: 'Jul 2022', value: 180 },
    { month: 'Jan 2023', value: 200 },
    { month: 'Jul 2023', value: 220 },
  ];

  const listingsByAvailability = [
    { name: '1-90 nights', value: 25 },
    { name: '91-180 nights', value: 35 },
    { name: '181-270 nights', value: 25 },
    { name: '271-365 nights', value: 15 },
  ];

  const listingsByRentalType = [
    { name: 'Shared Room', value: 10 },
    { name: 'Private Room', value: 30 },
    { name: 'Entire Home', value: 60 },
  ];

  const amenitiesData = [
    { name: 'Heating', value: 95 },
    { name: 'Pool', value: 45 },
    { name: 'Dryer', value: 15 },
    { name: 'Cable TV', value: 7 },
    { name: 'Hot Tub', value: 6 },
    { name: 'Kitchen', value: 92 },
    { name: 'Wifi', value: 98 },
    { name: 'AC', value: 85 },
  ];

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      // Simulate data refresh
      await new Promise(resolve => setTimeout(resolve, 1500))
    } catch (error) {
      console.error('Error refreshing data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        {/* Enhanced Mobile-First Header */}
        <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Back Button and Title */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push('/dashboard')}
                className="touch-target text-gray-600 hover:text-institutional-primary"
                disabled={isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isMobile ? 'Retour' : 'Retour au dashboard'}
              </Button>

              <div>
                <h1 className="mobile-text-xl font-bold text-gray-900">
                  {isMobile ? 'Statistiques' : 'Statistiques du Marché'}
                </h1>
                <p className="text-gray-600 text-sm sm:text-base mt-1">
                  Analyse des performances
                </p>
              </div>
            </div>

            {/* Controls */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              {/* Time Range Selector */}
              <Select value={timeRange} onValueChange={setTimeRange} disabled={isLoading}>
                <SelectTrigger className="mobile-input mobile-card w-full sm:w-[180px]">
                  <SelectValue placeholder="Période" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="year">Dernière année</SelectItem>
                  <SelectItem value="month">Dernier mois</SelectItem>
                  <SelectItem value="week">Dernière semaine</SelectItem>
                </SelectContent>
              </Select>

              {/* Action Buttons */}
              <div className="flex gap-2 sm:gap-3">
                <Button
                  variant="outline"
                  size={isMobile ? "default" : "sm"}
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target flex-1 sm:flex-initial"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  {isMobile ? 'Actualiser' : 'Actualiser'}
                </Button>

                <Button
                  variant="outline"
                  size={isMobile ? "default" : "sm"}
                  className="mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200 touch-target flex-1 sm:flex-initial"
                  disabled={isLoading}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isMobile ? 'Export' : 'Exporter'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Summary Stats */}
        <div className="animate-float-up-delay-1">
          <SummaryStats />
        </div>

        {/* Enhanced Mobile-First Tabs */}
        <Tabs defaultValue="listings" className="space-y-4 sm:space-y-6 animate-float-up-delay-2">
          <div className="bg-white/98 backdrop-blur-2xl shadow-lg border border-white/20 mobile-card p-1">
            <TabsList className={`grid w-full ${isMobile ? 'grid-cols-3' : 'grid-cols-6'} bg-transparent gap-1`}>
              <TabsTrigger
                value="listings"
                className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
              >
                {isMobile ? 'Annonces' : 'Annonces'}
              </TabsTrigger>
              <TabsTrigger
                value="occupancy"
                className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
              >
                {isMobile ? 'Occupation' : 'Occupation'}
              </TabsTrigger>
              <TabsTrigger
                value="revenue"
                className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
              >
                {isMobile ? 'Revenus' : 'Revenus'}
              </TabsTrigger>
              {!isMobile && (
                <>
                  <TabsTrigger
                    value="rates"
                    className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
                  >
                    Tarifs
                  </TabsTrigger>
                  <TabsTrigger
                    value="revpar"
                    className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
                  >
                    RevPAR
                  </TabsTrigger>
                  <TabsTrigger
                    value="map"
                    className="mobile-button data-[state=active]:bg-institutional-primary data-[state=active]:text-white text-gray-700 hover:bg-gray-100 transition-all duration-200"
                  >
                    Carte
                  </TabsTrigger>
                </>
              )}
            </TabsList>
          </div>

          <TabsContent value="listings" className="space-y-6">
            {/* Enhanced Mobile-First Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mobile-gap">
              <Card className="stats-card group animate-slide-up-from-bottom">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Home className="h-6 w-6 text-white" />
                  </div>
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                    +2 ce mois
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-gray-900 group-hover:text-institutional-primary transition-colors">9</div>
                  <p className="text-sm font-medium text-gray-600">Total des Annonces Actives</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-institutional-primary to-green-600 h-2 rounded-full w-4/5 transition-all duration-500"></div>
                  </div>
                </div>
              </Card>

              <Card className="stats-card group animate-slide-up-from-bottom" style={{ animationDelay: '100ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Percent className="h-6 w-6 text-white" />
                  </div>
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                    +5% vs mois dernier
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors">82%</div>
                  <p className="text-sm font-medium text-gray-600">Taux d'Occupation Moyen</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full w-4/5 transition-all duration-500"></div>
                  </div>
                </div>
              </Card>

              <Card className="stats-card group animate-slide-up-from-bottom" style={{ animationDelay: '200ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                    +12% vs mois dernier
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">€125k</div>
                  <p className="text-sm font-medium text-gray-600">Revenus Totaux</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full w-5/6 transition-all duration-500"></div>
                  </div>
                </div>
              </Card>

              <Card className="stats-card group animate-slide-up-from-bottom" style={{ animationDelay: '300ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                    +8 cette semaine
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors">24</div>
                  <p className="text-sm font-medium text-gray-600">Réservations Actives</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gradient-to-r from-purple-500 to-violet-600 h-2 rounded-full w-3/4 transition-all duration-500"></div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Enhanced Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 mobile-gap">
              <Card className="bg-white/98 backdrop-blur-2xl shadow-lg border border-white/20 mobile-card hover:shadow-xl transition-all duration-300 animate-slide-up-from-bottom">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <BarChart2 className="w-5 h-5 text-institutional-primary" />
                    Annonces par Canal de Location
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      value: {
                        label: "Pourcentage",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className={`${isMobile ? 'h-[250px]' : 'h-[300px]'}`}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={listingsByRentalChannel}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={isMobile ? 60 : 80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {listingsByRentalChannel.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <ChartTooltip content={<ChartTooltipContent />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>

              <Card className="bg-white/98 backdrop-blur-2xl shadow-lg border border-white/20 mobile-card hover:shadow-xl transition-all duration-300 animate-slide-up-from-bottom" style={{ animationDelay: '100ms' }}>
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <BedDouble className="w-5 h-5 text-institutional-primary" />
                    Annonces par Taille de Location
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      value: {
                        label: "Pourcentage",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className={`${isMobile ? 'h-[250px]' : 'h-[300px]'}`}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={listingsByRentalSize}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={isMobile ? 60 : 80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {listingsByRentalSize.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <ChartTooltip content={<ChartTooltipContent />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>

            {/* Additional Charts - Mobile Responsive Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 mobile-gap">
              <Card>
              <CardHeader>
                <CardTitle>Croissance des Locations (3 dernières années)</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    value: {
                      label: "Croissance",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={rentalGrowthData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line type="monotone" dataKey="value" stroke="var(--color-value)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Annonces par Disponibilité Annuelle</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    value: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={listingsByAvailability}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {listingsByAvailability.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Annonces par Type de Location</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    value: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={listingsByRentalType}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {listingsByRentalType.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Équipements des Locations</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    value: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart
                      layout="vertical"
                      data={amenitiesData}
                      margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" domain={[0, 100]} />
                      <YAxis dataKey="name" type="category" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="value" fill="var(--color-value)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="occupancy">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Taux d'Occupation</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    rate: {
                      label: "Taux d'occupation",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={occupancyRateData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line type="monotone" dataKey="rate" stroke="var(--color-rate)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Durée du Séjour</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    percentage: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={lengthOfStayData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="percentage"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {lengthOfStayData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Délai de Réservation</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    percentage: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={bookingLeadTimeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="leadTime" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="percentage" fill="var(--color-percentage)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Jours Occupés par Mois</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    occupiedDays: {
                      label: "Jours occupés",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={monthlyOccupancyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="occupiedDays" fill="var(--color-occupiedDays)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Distribution des Chambres</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    percentage: {
                      label: "Pourcentage",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={bedroomDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="percentage"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {bedroomDistributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Évolution de la Demande</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    demand: {
                      label: "Demande",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={demandOverTimeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line type="monotone" dataKey="demand" stroke="var(--color-demand)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Réservations Futures</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    bookings: {
                      label: "Réservations",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={futureReservationsData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="bookings" fill="var(--color-bookings)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenu Moyen par Annonce</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">{averageRevenue.toLocaleString()} MAD</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenus par Type de Propriété</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenus",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[200px]"
                >
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={revenueByPropertyType}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="type" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="revenue" fill="var(--color-revenue)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Revenus Mensuels des Annonces</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenus",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={monthlyRevenue}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line type="monotone" dataKey="revenue" stroke="var(--color-revenue)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenus par Nombre de Chambres</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenus",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[200px]"
                >
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={revenueByBedroom}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="bedrooms" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="revenue" fill="var(--color-revenue)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenus par Type de Bien Immobilier</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenus",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[200px]"
                >
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={revenueByRealEstateType}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {revenueByRealEstateType.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Distribution des Revenus des Annonces</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    count: {
                      label: "Nombre d'annonces",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={revenueDistribution}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="count" fill="var(--color-count)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rates">
          <RateAnalysisDashboard />
        </TabsContent>

        <TabsContent value="revpar">
          <RevPARDashboard />
        </TabsContent>

        <TabsContent value="map">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                Carte des Annonces
              </CardTitle>
            </CardHeader>
            <CardContent>
              <MapView />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Cancellation and Minimum Stay Policies */}
      <Card className="mb-6 mt-6">
        <CardHeader>
          <CardTitle>Politiques de Location</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">Politique d'Annulation</h3>
              <div className="flex w-full h-4 rounded-full overflow-hidden">
                <div className="bg-yellow-200 w-[18.9%]" title="Autre (18.9%)" />
                <div className="bg-purple-200 w-[44.8%]" />
                <div className="bg-blue-200 w-[21.6%]" title="Modérée (21.6%)" />
                <div className="bg-green-200 w-[14.4%]" title="Stricte (14.4%)" />
                <div className="bg-orange-200 w-[0.3%]" title="Super stricte (0.3%)" />
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Séjour Minimum</h3>
              <div className="flex w-full h-4 rounded-full overflow-hidden">
                <div className="bg-green-200 w-[47.3%]" title="1 Nuit (47.3%)" />
                <div className="bg-pink-200 w-[35.4%]" title="2 Nuits (35.4%)" />
                <div className="bg-blue-200 w-[12.5%]" title="3 Nuits (12.5%)" />
                <div className="bg-yellow-200 w-[3.5%]" title="4-6 Nuits (3.5%)" />
                <div className="bg-purple-200 w-[0.9%]" title="7-29 Nuits (0.9%)" />
                <div className="bg-red-200 w-[0.3%]" title="30+ Nuits (0.3%)" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}

