'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Upload } from 'lucide-react'
import { useRouter } from 'next/navigation'

// Mock data for the import report
const mockImportReports = [
  { id: 1, date: '2023-07-01', fileName: 'nuites-juillet.csv', status: 'Succès' },
  { id: 2, date: '2023-08-15', fileName: 'nuites-aout.xlsx', status: 'Erreur' },
  { id: 3, date: '2023-09-03', fileName: 'nuites-septembre.csv', status: 'En cours' },
]

export function ImportNuites() {
  const router = useRouter()
  const [file, setFile] = useState<File | null>(null)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFile(event.target.files[0])
    }
  }

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    // Here you would handle the file upload
    console.log('File to upload:', file)
    // After upload, you might want to show a success message or redirect
  }

  return (
    <div className="container mx-auto py-8">
      <Button
        variant="outline"
        size="sm"
        onClick={() => router.push('/dashboard')}
        className="mb-4"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Retour au tableau de bord
      </Button>

      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-semibold flex items-center gap-2">
              <Upload className="h-6 w-6" />
              Importer des nuités
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="file-upload">Fichier à importer</Label>
                <Input
                  id="file-upload"
                  type="file"
                  onChange={handleFileChange}
                  accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                />
              </div>
              <Button type="submit" className="w-full">
                Importer
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-semibold">
              Rapport d'imports de fichiers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date d'import</TableHead>
                  <TableHead>Nom du fichier</TableHead>
                  <TableHead>Statut</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockImportReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>{report.date}</TableCell>
                    <TableCell>{report.fileName}</TableCell>
                    <TableCell>{report.status}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

