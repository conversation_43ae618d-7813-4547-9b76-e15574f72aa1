import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'

import { Calendar, ChevronLeft, ChevronRight, Plus, Clock } from 'lucide-react'

export default function CalendrierPage() {
  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          <DashboardSidebar />
          <div className="flex-1 bg-gray-50 min-h-full logo-background-content">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-6">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Calendrier des Réservations
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Gérez vos réservations et disponibilités
                  </p>
                </div>
                <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Nouvelle Réservation
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container py-6 space-y-6">
              {/* Calendar Header */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Juin 2024
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <ChevronLeft className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-2 mb-4">
                    {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map((day) => (
                      <div key={day} className="p-2 text-center text-sm font-semibold text-gray-600">
                        {day}
                      </div>
                    ))}
                  </div>
                  
                  {/* Calendar Days */}
                  <div className="grid grid-cols-7 gap-2">
                    {Array.from({ length: 30 }, (_, i) => i + 1).map((day) => (
                      <div
                        key={day}
                        className={`p-3 text-center text-sm border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          day === 15 ? 'bg-institutional-primary text-white' : 
                          day === 20 || day === 25 ? 'bg-red-100 text-red-700' : 
                          'bg-white'
                        }`}
                      >
                        {day}
                        {day === 20 && <div className="text-xs mt-1">Réservé</div>}
                        {day === 25 && <div className="text-xs mt-1">Check-out</div>}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Events */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Événements à venir
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Check-in - Appartement A1</h3>
                        <p className="text-sm text-gray-600">20 Juin 2024 - 15:00</p>
                        <p className="text-sm text-gray-500">Client: Jean Dupont</p>
                      </div>
                      <div className="text-right">
                        <span className="inline-block px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
                          Confirmé
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Check-out - Studio B2</h3>
                        <p className="text-sm text-gray-600">25 Juin 2024 - 11:00</p>
                        <p className="text-sm text-gray-500">Client: Marie Martin</p>
                      </div>
                      <div className="text-right">
                        <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                          En cours
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">Maintenance - Villa C3</h3>
                        <p className="text-sm text-gray-600">28 Juin 2024 - 09:00</p>
                        <p className="text-sm text-gray-500">Nettoyage approfondi</p>
                      </div>
                      <div className="text-right">
                        <span className="inline-block px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded">
                          Planifié
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      <MobileBottomNav />
    </div>
  )
}
