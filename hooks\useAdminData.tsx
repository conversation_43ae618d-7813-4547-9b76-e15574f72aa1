'use client'

import { useState, useEffect } from 'react'
import { adminHosts, adminAccommodations, adminDeclarations, moroccanCities } from '@/utils/mockData'
import { DashboardStats, CityStats } from '@/types/admin'

export function useAdminData() {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalDeclarations: {
      today: 0,
      week: 0,
      month: 0,
      total: 0
    },
    totalAccommodations: 0,
    totalHosts: 0,
    occupancyRate: 0,
    topCities: [],
    recentDeclarations: []
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading time
    const loadData = async () => {
      setIsLoading(true)
      
      // Calculate date ranges
      const now = new Date()
      const today = now.toISOString().split('T')[0]
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      // Calculate declaration stats
      const todayDeclarations = adminDeclarations.filter(d => d.dateDeclaration === today).length
      const weekDeclarations = adminDeclarations.filter(d => d.dateDeclaration >= weekAgo).length
      const monthDeclarations = adminDeclarations.filter(d => d.dateDeclaration >= monthAgo).length

      // Calculate occupancy rate
      const occupiedAccommodations = adminAccommodations.filter(acc => acc.statut === 'occupé').length
      const occupancyRate = Math.round((occupiedAccommodations / adminAccommodations.length) * 100)

      // Calculate city statistics
      const cityStatsMap = new Map<string, CityStats>()
      
      // Initialize cities
      moroccanCities.forEach(city => {
        cityStatsMap.set(city.name, {
          ville: city.name,
          region: city.region,
          totalAccommodations: 0,
          totalDeclarations: 0,
          activeHosts: 0,
          occupancyRate: 0,
          coordinates: { lat: city.lat, lng: city.lng }
        })
      })

      // Count accommodations per city
      adminAccommodations.forEach(acc => {
        const cityStats = cityStatsMap.get(acc.ville)
        if (cityStats) {
          cityStats.totalAccommodations++
        }
      })

      // Count declarations per city
      adminDeclarations.forEach(decl => {
        const cityStats = cityStatsMap.get(decl.ville)
        if (cityStats) {
          cityStats.totalDeclarations++
        }
      })

      // Count active hosts per city
      adminHosts.forEach(host => {
        if (host.statut === 'actif') {
          const cityStats = cityStatsMap.get(host.ville)
          if (cityStats) {
            cityStats.activeHosts++
          }
        }
      })

      // Calculate occupancy rate per city
      cityStatsMap.forEach((cityStats, cityName) => {
        const cityAccommodations = adminAccommodations.filter(acc => acc.ville === cityName)
        const occupiedInCity = cityAccommodations.filter(acc => acc.statut === 'occupé').length
        cityStats.occupancyRate = cityAccommodations.length > 0 
          ? Math.round((occupiedInCity / cityAccommodations.length) * 100)
          : 0
      })

      // Convert to array and sort by total accommodations
      const topCities = Array.from(cityStatsMap.values())
        .filter(city => city.totalAccommodations > 0)
        .sort((a, b) => b.totalAccommodations - a.totalAccommodations)
        .slice(0, 15)

      // Get recent declarations
      const recentDeclarations = adminDeclarations
        .sort((a, b) => new Date(b.dateDeclaration).getTime() - new Date(a.dateDeclaration).getTime())
        .slice(0, 10)

      setDashboardStats({
        totalDeclarations: {
          today: todayDeclarations,
          week: weekDeclarations,
          month: monthDeclarations,
          total: adminDeclarations.length
        },
        totalAccommodations: adminAccommodations.length,
        totalHosts: adminHosts.filter(h => h.statut === 'actif').length,
        occupancyRate,
        topCities,
        recentDeclarations
      })

      setIsLoading(false)
    }

    loadData()
  }, [])

  return {
    dashboardStats,
    isLoading,
    adminHosts,
    adminAccommodations,
    adminDeclarations
  }
}
