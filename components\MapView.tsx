'use client'

import { useEffect } from 'react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'

// Mock data for listings
const listings = [
  { id: 1, name: "Villa Marrakech", lat: 31.6295, lng: -7.9811, price: 1500 },
  { id: 2, name: "Appartement Casablanca", lat: 33.5731, lng: -7.5898, price: 800 },
  { id: 3, name: "Riad Fès", lat: 34.0333, lng: -5.0000, price: 1200 },
  { id: 4, name: "<PERSON><PERSON> Tang<PERSON>", lat: 35.7595, lng: -5.8340, price: 950 },
  { id: 5, name: "Villa Agadir", lat: 30.4278, lng: -9.5981, price: 1100 },
]

export function MapView() {
  useEffect(() => {
    // Fix for the marker icon issue in react-leaflet
    delete (L.Icon.Default.prototype as any)._getIconUrl
    L.Icon.Default.mergeOptions({
      iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
      iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    })
  }, [])

  return (
    <div className="h-[600px] w-full">
      <MapContainer center={[31.7917, -7.0926]} zoom={6} style={{ height: '100%', width: '100%' }}>
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {listings.map((listing) => (
          <Marker key={listing.id} position={[listing.lat, listing.lng]}>
            <Popup>
              <div>
                <h3 className="font-semibold">{listing.name}</h3>
                <p>{listing.price} MAD / nuit</p>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  )
}

