'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { PageWrapper } from '@/components/PageWrapper'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  User, Save, X, Calendar, MapPin, Phone, Mail, FileText,
  Building2, Users, ArrowLeft
} from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function AjouterLocatairePage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    nationality: '',
    dateOfBirth: '',
    placeOfBirth: '',
    
    // Identity Documents
    documentType: '',
    documentNumber: '',
    documentIssueDate: '',
    documentExpiryDate: '',
    
    // Address
    address: '',
    city: '',
    postalCode: '',
    country: '',
    
    // Stay Information
    property: '',
    checkInDate: '',
    checkOutDate: '',
    numberOfGuests: '1',
    purpose: '',
    
    // Additional Information
    notes: ''
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically submit to your backend
    console.log('Submitting tenant data:', formData)
    // Show success message and redirect
    router.push('/locataires')
  }

  const handleCancel = () => {
    router.push('/locataires')
  }

  return (
    <PageWrapper
      forceUserType="host"
      className="min-h-screen bg-gray-50 logo-background"
      showMobileActions={true}
    >
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push('/locataires')}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Retour
                  </Button>
                  <div>
                    <h1 className="mobile-text-xl font-bold text-gray-900">
                      Ajouter un Locataire
                    </h1>
                    <p className="text-sm text-gray-600 mt-1">
                      Enregistrez un nouveau locataire dans le système
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-2" />
                    Annuler
                  </Button>
                  <Button 
                    className="bg-institutional-primary hover:bg-green-600 text-white"
                    onClick={handleSubmit}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Enregistrer
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Personal Information */}
                <Card className="enhanced-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Informations Personnelles
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">Prénom *</Label>
                        <Input
                          id="firstName"
                          value={formData.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Nom *</Label>
                        <Input
                          id="lastName"
                          value={formData.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Téléphone *</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="nationality">Nationalité *</Label>
                        <Select value={formData.nationality} onValueChange={(value) => handleInputChange('nationality', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Sélectionner la nationalité" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="française">Française</SelectItem>
                            <SelectItem value="espagnole">Espagnole</SelectItem>
                            <SelectItem value="italienne">Italienne</SelectItem>
                            <SelectItem value="allemande">Allemande</SelectItem>
                            <SelectItem value="britannique">Britannique</SelectItem>
                            <SelectItem value="autre">Autre</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="dateOfBirth">Date de naissance</Label>
                        <Input
                          id="dateOfBirth"
                          type="date"
                          value={formData.dateOfBirth}
                          onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Identity Documents */}
                <Card className="enhanced-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Pièce d'Identité
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="documentType">Type de document *</Label>
                        <Select value={formData.documentType} onValueChange={(value) => handleInputChange('documentType', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Sélectionner le type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="carte-identite">Carte d'identité</SelectItem>
                            <SelectItem value="passeport">Passeport</SelectItem>
                            <SelectItem value="permis-conduire">Permis de conduire</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="documentNumber">Numéro du document *</Label>
                        <Input
                          id="documentNumber"
                          value={formData.documentNumber}
                          onChange={(e) => handleInputChange('documentNumber', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="documentIssueDate">Date de délivrance</Label>
                        <Input
                          id="documentIssueDate"
                          type="date"
                          value={formData.documentIssueDate}
                          onChange={(e) => handleInputChange('documentIssueDate', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="documentExpiryDate">Date d'expiration</Label>
                        <Input
                          id="documentExpiryDate"
                          type="date"
                          value={formData.documentExpiryDate}
                          onChange={(e) => handleInputChange('documentExpiryDate', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Address */}
                <Card className="enhanced-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      Adresse de Résidence
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <Label htmlFor="address">Adresse complète</Label>
                        <Input
                          id="address"
                          value={formData.address}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="city">Ville</Label>
                          <Input
                            id="city"
                            value={formData.city}
                            onChange={(e) => handleInputChange('city', e.target.value)}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="postalCode">Code postal</Label>
                          <Input
                            id="postalCode"
                            value={formData.postalCode}
                            onChange={(e) => handleInputChange('postalCode', e.target.value)}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="country">Pays</Label>
                          <Input
                            id="country"
                            value={formData.country}
                            onChange={(e) => handleInputChange('country', e.target.value)}
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Stay Information */}
                <Card className="enhanced-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="w-5 h-5" />
                      Informations du Séjour
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="property">Propriété *</Label>
                        <Select value={formData.property} onValueChange={(value) => handleInputChange('property', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Sélectionner la propriété" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="appartement-a1">Appartement A1</SelectItem>
                            <SelectItem value="studio-b2">Studio B2</SelectItem>
                            <SelectItem value="appartement-c3">Appartement C3</SelectItem>
                            <SelectItem value="villa-d4">Villa D4</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="numberOfGuests">Nombre de personnes</Label>
                        <Select value={formData.numberOfGuests} onValueChange={(value) => handleInputChange('numberOfGuests', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 personne</SelectItem>
                            <SelectItem value="2">2 personnes</SelectItem>
                            <SelectItem value="3">3 personnes</SelectItem>
                            <SelectItem value="4">4 personnes</SelectItem>
                            <SelectItem value="5">5 personnes</SelectItem>
                            <SelectItem value="6">6+ personnes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="checkInDate">Date d'arrivée *</Label>
                        <Input
                          id="checkInDate"
                          type="date"
                          value={formData.checkInDate}
                          onChange={(e) => handleInputChange('checkInDate', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="checkOutDate">Date de départ *</Label>
                        <Input
                          id="checkOutDate"
                          type="date"
                          value={formData.checkOutDate}
                          onChange={(e) => handleInputChange('checkOutDate', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <Label htmlFor="purpose">Motif du séjour</Label>
                        <Select value={formData.purpose} onValueChange={(value) => handleInputChange('purpose', value)}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Sélectionner le motif" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="tourisme">Tourisme</SelectItem>
                            <SelectItem value="affaires">Affaires</SelectItem>
                            <SelectItem value="famille">Visite familiale</SelectItem>
                            <SelectItem value="etudes">Études</SelectItem>
                            <SelectItem value="medical">Médical</SelectItem>
                            <SelectItem value="autre">Autre</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Information */}
                <Card className="enhanced-card">
                  <CardHeader>
                    <CardTitle>Informations Complémentaires</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <Label htmlFor="notes">Notes</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        rows={3}
                        placeholder="Informations supplémentaires..."
                        className="mt-1"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Submit Buttons */}
                <div className="flex justify-end gap-4 pb-6">
                  <Button type="button" variant="outline" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-2" />
                    Annuler
                  </Button>
                  <Button 
                    type="submit"
                    className="bg-institutional-primary hover:bg-green-600 text-white"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Enregistrer le Locataire
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </PageWrapper>
  )
}
