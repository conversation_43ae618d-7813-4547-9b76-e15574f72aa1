'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { useIsMobile } from '@/hooks/use-mobile'
import Image from 'next/image'
import { GuestDataStorage } from '@/utils/dataStorage'
import { retryPDFGeneration, pdfRetryManager } from '@/utils/networkRetry'
import {
  FileDown,
  FileText,
  Loader2,
  CheckCircle,
  User,
  Calendar,
  CreditCard,
  Camera,
  Pen,
  Shield,
  ArrowLeft,
  Download,
  Eye,
  MapPin,
  Clock,
  UserPlus,
  Home,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'

// Mock data - replace this with actual data passed from previous steps
const mockData = {
  nom: "Doe",
  prenom: "John",
  dateNaissance: "1990-01-01",
  numeroPieceIdentite: "AB123456",
  typePieceIdentite: "Passport",
  dateArrivee: "2023-07-01",
  dateDepart: "2023-07-15",
  motifSejour: "Tourisme",
  idRectoUrl: "/placeholder.svg?height=200&width=320",
  idVersoUrl: "/placeholder.svg?height=200&width=320",
  signature: "/placeholder.svg?height=100&width=300",
  selfie: "/placeholder.svg?height=200&width=200"
}

export function SummarySelfCheckIn() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isGeneratingRental, setIsGeneratingRental] = useState(false)
  const [guestData, setGuestData] = useState<any>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [networkError, setNetworkError] = useState(false)

  useEffect(() => {
    // Load guest data from storage
    const data = GuestDataStorage.getFromSession()
    setGuestData(data)
  }, [])

  const handleDownloadPDF = async (withRetry = false) => {
    setIsGeneratingPDF(true)
    setNetworkError(false)

    try {
      // Use actual guest data or fallback to mock data
      const dataToSubmit = guestData || mockData

      // Mark as submitted
      GuestDataStorage.markAsSubmitted()

      // Use the retry manager for robust PDF generation
      const result = await pdfRetryManager.generatePDFWithRetry(dataToSubmit)

      if (result.success && result.data) {
        const blob = result.data
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `declaration_locataire_${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        // Reset retry count on success
        setRetryCount(0)
        alert(`Déclaration PDF générée avec succès! (${result.attempts} tentative${result.attempts > 1 ? 's' : ''})`)
      } else {
        throw result.error || new Error('PDF generation failed')
      }
    } catch (error) {
      console.error('Error generating PDF:', error)
      setNetworkError(true)
      setRetryCount(prev => prev + 1)

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
      alert(`Erreur lors de la génération du PDF: ${errorMessage}. Veuillez réessayer.`)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleDownloadRentalAgreement = async () => {
    setIsGeneratingRental(true)
    try {
      // Use actual guest data or fallback to mock data
      const dataToSubmit = guestData || mockData

      // Use the retry manager for robust rental agreement generation
      const result = await pdfRetryManager.generatePDFWithRetry(
        dataToSubmit,
        '/api/generate-rental-agreement'
      )

      if (result.success && result.data) {
        const blob = result.data
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `contrat_location_${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        alert(`Contrat de location généré avec succès! (${result.attempts} tentative${result.attempts > 1 ? 's' : ''})`)
      } else {
        throw result.error || new Error('Rental agreement generation failed')
      }
    } catch (error) {
      console.error('Error generating rental agreement:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
      alert(`Erreur lors de la génération du contrat: ${errorMessage}. Veuillez réessayer.`)
    } finally {
      setIsGeneratingRental(false)
    }
  }

  const handleAddAnotherGuest = () => {
    // Clear current session data but keep accommodation code
    const accommodationCode = guestData?.accommodationCode || sessionStorage.getItem('accommodationCode')
    GuestDataStorage.clearSession()

    if (accommodationCode) {
      sessionStorage.setItem('accommodationCode', accommodationCode)
    }

    // Navigate back to the start of the guest journey
    router.push('/self-check-in')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        <div className="max-w-4xl mx-auto space-y-6">

          {/* Enhanced Mobile-First Header */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={() => router.push('/verification-self-check-in')}
                  className="touch-target text-gray-600 hover:text-institutional-primary"
                  disabled={isGeneratingPDF}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {isMobile ? 'Retour' : 'Retour à la vérification'}
                </Button>

                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="mobile-text-xl font-bold text-gray-900">
                      {isMobile ? 'Résumé Check-in' : 'Résumé de la Déclaration Self Check-in'}
                    </h1>
                    <p className="text-gray-600 text-sm sm:text-base">
                      Vérification terminée avec succès
                    </p>
                  </div>
                </div>
              </div>

              {/* Status Badge */}
              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200 w-fit">
                <CheckCircle className="w-3 h-3 mr-1" />
                Vérification complète
              </Badge>
            </div>
          </div>

          {/* Enhanced Summary Card */}
          <Card className="enhanced-card animate-slide-up-from-bottom">
            <CardHeader className="mobile-padding pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
                  <Shield className="w-5 h-5 text-institutional-primary" />
                  Informations Vérifiées
                </CardTitle>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-600 font-medium">Validé</span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="mobile-padding pt-0 space-y-6">
              {/* Enhanced Personal Information Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <User className="form-section-icon" />
                  <h3 className="form-section-title">Informations Personnelles</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                    Vérifiées
                  </Badge>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">NOM</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.nom}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">PRÉNOM</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.prenom}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">DATE DE NAISSANCE</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.dateNaissance}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">MOTIF DU SÉJOUR</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.motifSejour}</p>
                  </div>
                </div>
              </div>

              {/* Enhanced Identity Document Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <CreditCard className="form-section-icon" />
                  <h3 className="form-section-title">Pièce d'Identité</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                    Vérifiée
                  </Badge>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">TYPE DE DOCUMENT</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.typePieceIdentite}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">NUMÉRO</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.numeroPieceIdentite}</p>
                  </div>
                </div>
              </div>

              {/* Enhanced Stay Dates Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <Calendar className="form-section-icon" />
                  <h3 className="form-section-title">Dates de Séjour</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-blue-50 text-blue-700">
                    Confirmées
                  </Badge>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">ARRIVÉE</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.dateArrivee}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-500">DÉPART</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">{mockData.dateDepart}</p>
                  </div>
                </div>
              </div>

              {/* Enhanced Document Photos Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <Camera className="form-section-icon" />
                  <h3 className="form-section-title">Photos d'Identité</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                    Capturées
                  </Badge>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 mobile-gap">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Recto du document</span>
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                        ✓ Validé
                      </Badge>
                    </div>
                    <div className="relative group">
                      <Image
                        src={mockData.idRectoUrl}
                        alt="ID Recto"
                        width={isMobile ? 300 : 320}
                        height={200}
                        className="w-full mobile-card shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      />
                      <div className="absolute inset-0 bg-green-500/10 mobile-card opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <Eye className="w-8 h-8 text-green-600" />
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Verso du document</span>
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                        ✓ Validé
                      </Badge>
                    </div>
                    <div className="relative group">
                      <Image
                        src={mockData.idVersoUrl}
                        alt="ID Verso"
                        width={isMobile ? 300 : 320}
                        height={200}
                        className="w-full mobile-card shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      />
                      <div className="absolute inset-0 bg-green-500/10 mobile-card opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <Eye className="w-8 h-8 text-green-600" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Signature Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <Pen className="form-section-icon" />
                  <h3 className="form-section-title">Signature du Voyageur</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                    Signée
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Signature électronique</span>
                    <div className="flex items-center gap-2">
                      <Clock className="w-3 h-3 text-gray-500" />
                      <span className="text-xs text-gray-500">Signée aujourd'hui</span>
                    </div>
                  </div>
                  <div className="relative group">
                    <Image
                      src={mockData.signature}
                      alt="Signature"
                      width={isMobile ? 280 : 300}
                      height={100}
                      className="w-full max-w-md mobile-card shadow-lg group-hover:shadow-xl transition-shadow duration-300 bg-white"
                    />
                    <div className="absolute inset-0 bg-blue-500/10 mobile-card opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <Eye className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Selfie Section */}
              <div className="form-section">
                <div className="form-section-header">
                  <User className="form-section-icon" />
                  <h3 className="form-section-title">Photo du Voyageur</h3>
                  <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                    Capturée
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Selfie de vérification</span>
                    <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                      ✓ Correspondance validée
                    </Badge>
                  </div>
                  <div className="flex justify-center">
                    <div className="relative group">
                      <Image
                        src={mockData.selfie}
                        alt="Selfie"
                        width={200}
                        height={200}
                        className="mobile-card shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      />
                      <div className="absolute inset-0 bg-purple-500/10 mobile-card opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <Eye className="w-8 h-8 text-purple-600" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </CardContent>
          </Card>

          {/* Enhanced Action Buttons */}
          <Card className="enhanced-card animate-slide-up-from-bottom" style={{ animationDelay: '200ms' }}>
            <CardContent className="mobile-padding">
              <div className="space-y-4">
                <div className="text-center space-y-2">
                  <h3 className="text-lg font-bold text-gray-900">Prochaines Étapes</h3>
                  <p className="text-gray-600 text-sm">
                    Votre vérification est terminée. Vous pouvez maintenant télécharger votre déclaration ou générer un contrat.
                  </p>
                </div>

                {/* Network Error Warning */}
                {networkError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-red-800 text-sm font-medium">Erreur de connexion</p>
                        <p className="text-red-600 text-xs mt-1">
                          {retryCount > 0 ? `Tentative ${retryCount}/3 en cours...` : 'Vérifiez votre connexion internet'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <Button
                    variant="outline"
                    onClick={() => handleDownloadPDF(true)}
                    disabled={isGeneratingPDF || isGeneratingRental}
                    className="mobile-button btn-outline touch-target"
                  >
                    {isGeneratingPDF ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {retryCount > 0 ? `Tentative ${retryCount}/3...` : 'Génération...'}
                      </>
                    ) : networkError ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Réessayer PDF
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        {isMobile ? 'Déclaration PDF' : 'Télécharger Déclaration'}
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleDownloadRentalAgreement}
                    disabled={isGeneratingPDF || isGeneratingRental}
                    className="mobile-button btn-primary touch-target"
                  >
                    {isGeneratingRental ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Génération...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        {isMobile ? 'Contrat Location' : 'Contrat de Location'}
                      </>
                    )}
                  </Button>
                </div>

                {/* Add Another Guest Section */}
                <div className="pt-4 border-t border-gray-100">
                  <div className="text-center space-y-3">
                    <h4 className="font-medium text-gray-900">Autres actions</h4>
                    <Button
                      onClick={handleAddAnotherGuest}
                      disabled={isGeneratingPDF || isGeneratingRental}
                      variant="outline"
                      className="mobile-button border-2 border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white touch-target"
                    >
                      <UserPlus className="h-4 w-4 mr-2" />
                      Ajouter un autre voyageur
                    </Button>
                    <p className="text-xs text-gray-500">
                      Déclarer un autre voyageur pour le même hébergement
                    </p>
                  </div>
                </div>

                {/* Additional Actions */}
                <div className="pt-4 border-t border-gray-100">
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push('/dashboard')}
                      disabled={isGeneratingPDF}
                      className="mobile-button text-gray-600 hover:text-institutional-primary touch-target"
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Retour au tableau de bord
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      disabled={isGeneratingPDF}
                      className="mobile-button text-gray-600 hover:text-institutional-primary touch-target"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Prévisualiser la déclaration
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Success Message */}
          <div className="text-center space-y-2 animate-slide-up-from-bottom" style={{ animationDelay: '300ms' }}>
            <div className="flex items-center justify-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-green-600 font-medium">Vérification terminée avec succès</span>
            </div>
            <p className="text-gray-500 text-sm">
              Toutes les informations ont été vérifiées et validées.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

