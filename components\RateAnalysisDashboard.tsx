'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Bar, Composed<PERSON>hart, Bar<PERSON>hart, Legend } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

// Average Daily Rate Data
const averageDailyRateData = [
  { month: 'Dec 2023', rate: 350 * 11 },
  { month: 'Jan 2024', rate: 355 * 11 },
  { month: 'Feb 2024', rate: 340 * 11 },
  { month: 'Mar 2024', rate: 335 * 11 },
  { month: 'Apr 2024', rate: 345 * 11 },
  { month: 'May 2024', rate: 360 * 11 },
  { month: 'Jun 2024', rate: 380 * 11 },
  { month: 'Jul 2024', rate: 375 * 11 },
  { month: 'Aug 2024', rate: 400 * 11 },
  { month: 'Sep 2024', rate: 425 * 11 },
  { month: 'Oct 2024', rate: 390 * 11 },
  { month: 'Nov 2024', rate: 370 * 11 },
]

// Rate by Bedroom Data
const rateByBedroomData = [
  { month: 'Dec 2023', '1bed': 400 * 11, '2bed': 600 * 11, '3bed': 800 * 11, '4bed': 1000 * 11, '5bed': 1200 * 11, '6plusBed': 1400 * 11 },
  { month: 'Jan 2024', '1bed': 410 * 11, '2bed': 610 * 11, '3bed': 810 * 11, '4bed': 1010 * 11, '5bed': 1210 * 11, '6plusBed': 1410 * 11 },
  { month: 'Feb 2024', '1bed': 405 * 11, '2bed': 605 * 11, '3bed': 805 * 11, '4bed': 1005 * 11, '5bed': 1205 * 11, '6plusBed': 1405 * 11 },
  { month: 'Mar 2024', '1bed': 400 * 11, '2bed': 600 * 11, '3bed': 800 * 11, '4bed': 1000 * 11, '5bed': 1200 * 11, '6plusBed': 1400 * 11 },
  { month: 'Apr 2024', '1bed': 420 * 11, '2bed': 620 * 11, '3bed': 820 * 11, '4bed': 1020 * 11, '5bed': 1220 * 11, '6plusBed': 1420 * 11 },
  { month: 'May 2024', '1bed': 430 * 11, '2bed': 630 * 11, '3bed': 830 * 11, '4bed': 1030 * 11, '5bed': 1230 * 11, '6plusBed': 1430 * 11 },
  { month: 'Jun 2024', '1bed': 450 * 11, '2bed': 650 * 11, '3bed': 850 * 11, '4bed': 1050 * 11, '5bed': 1250 * 11, '6plusBed': 1450 * 11 },
  { month: 'Jul 2024', '1bed': 460 * 11, '2bed': 660 * 11, '3bed': 860 * 11, '4bed': 1060 * 11, '5bed': 1260 * 11, '6plusBed': 1460 * 11 },
  { month: 'Aug 2024', '1bed': 470 * 11, '2bed': 670 * 11, '3bed': 870 * 11, '4bed': 1070 * 11, '5bed': 1270 * 11, '6plusBed': 1470 * 11 },
  { month: 'Sep 2024', '1bed': 480 * 11, '2bed': 680 * 11, '3bed': 880 * 11, '4bed': 1080 * 11, '5bed': 1280 * 11, '6plusBed': 1480 * 11 },
  { month: 'Oct 2024', '1bed': 460 * 11, '2bed': 660 * 11, '3bed': 860 * 11, '4bed': 1060 * 11, '5bed': 1260 * 11, '6plusBed': 1460 * 11 },
  { month: 'Nov 2024', '1bed': 450 * 11, '2bed': 650 * 11, '3bed': 850 * 11, '4bed': 1050 * 11, '5bed': 1250 * 11, '6plusBed': 1450 * 11 },
]

// Daily Rate Data (30 days of data for demonstration)
const dailyRateData = Array.from({ length: 30 }, (_, i) => ({
  date: `Day ${i + 1}`,
  rate: (300 + Math.random() * 100 + Math.sin(i / 3) * 50) * 11,
}))

// Rate by Price Tier Data
const ratePriceTierData = [
  { month: 'Dec 2023', budget: 200 * 11, economy: 300 * 11, moderate: 400 * 11, upscale: 500 * 11, luxury: 600 * 11 },
  { month: 'Jan 2024', budget: 210 * 11, economy: 310 * 11, moderate: 410 * 11, upscale: 510 * 11, luxury: 610 * 11 },
  { month: 'Feb 2024', budget: 205 * 11, economy: 305 * 11, moderate: 405 * 11, upscale: 505 * 11, luxury: 605 * 11 },
  { month: 'Mar 2024', budget: 200 * 11, economy: 300 * 11, moderate: 400 * 11, upscale: 500 * 11, luxury: 600 * 11 },
  { month: 'Apr 2024', budget: 220 * 11, economy: 320 * 11, moderate: 420 * 11, upscale: 520 * 11, luxury: 620 * 11 },
  { month: 'May 2024', budget: 230 * 11, economy: 330 * 11, moderate: 430 * 11, upscale: 530 * 11, luxury: 630 * 11 },
  { month: 'Jun 2024', budget: 240 * 11, economy: 340 * 11, moderate: 440 * 11, upscale: 540 * 11, luxury: 640 * 11 },
  { month: 'Jul 2024', budget: 250 * 11, economy: 350 * 11, moderate: 450 * 11, upscale: 550 * 11, luxury: 650 * 11 },
  { month: 'Aug 2024', budget: 260 * 11, economy: 360 * 11, moderate: 460 * 11, upscale: 560 * 11, luxury: 660 * 11 },
  { month: 'Sep 2024', budget: 270 * 11, economy: 370 * 11, moderate: 470 * 11, upscale: 570 * 11, luxury: 670 * 11 },
  { month: 'Oct 2024', budget: 260 * 11, economy: 360 * 11, moderate: 460 * 11, upscale: 560 * 11, luxury: 660 * 11 },
  { month: 'Nov 2024', budget: 250 * 11, economy: 350 * 11, moderate: 450 * 11, upscale: 550 * 11, luxury: 650 * 11 },
]

// Willingness to Pay Data
const willingnessToPayData = Array.from({ length: 30 }, (_, i) => ({
  date: `Day ${i + 1}`,
  bookings: Math.floor(Math.random() * 50) + 20,
  averageRate: (275 + Math.sin(i / 3) * 25) * 11,
}))

export function RateAnalysisDashboard() {
  return (
    <div className="space-y-6">
      {/* Average Daily Rate Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Taux Journalier Moyen</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              rate: {
                label: "Taux",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={averageDailyRateData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tickFormatter={(value) => value.split(' ')[0]}
                />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="rate"
                  stroke="var(--color-rate)"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Rate by Bedroom Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Taux par Nombre de Chambres</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              '1bed': {
                label: "1 Chambre",
                color: "hsl(var(--chart-1))",
              },
              '2bed': {
                label: "2 Chambres",
                color: "hsl(var(--chart-2))",
              },
              '3bed': {
                label: "3 Chambres",
                color: "hsl(var(--chart-3))",
              },
              '4bed': {
                label: "4 Chambres",
                color: "hsl(var(--chart-4))",
              },
              '5bed': {
                label: "5 Chambres",
                color: "hsl(var(--chart-5))",
              },
              '6plusBed': {
                label: "6+ Chambres",
                color: "hsl(var(--chart-6))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={rateByBedroomData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tickFormatter={(value) => value.split(' ')[0]}
                />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line type="monotone" dataKey="1bed" stroke="var(--color-1bed)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="2bed" stroke="var(--color-2bed)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="3bed" stroke="var(--color-3bed)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="4bed" stroke="var(--color-4bed)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="5bed" stroke="var(--color-5bed)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="6plusBed" stroke="var(--color-6plusBed)" strokeWidth={2} dot={{ r: 2 }} />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Daily Rate Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Taux Journalier</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              rate: {
                label: "Taux",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={dailyRateData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date"
                  interval={2}
                />
                <YAxis domain={['auto', 'auto']} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="rate"
                  stroke="var(--color-rate)"
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Rate by Price Tier Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Taux par Catégorie de Prix</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              budget: {
                label: "Budget",
                color: "hsl(var(--chart-1))",
              },
              economy: {
                label: "Économique",
                color: "hsl(var(--chart-2))",
              },
              moderate: {
                label: "Modéré",
                color: "hsl(var(--chart-3))",
              },
              upscale: {
                label: "Haut de gamme",
                color: "hsl(var(--chart-4))",
              },
              luxury: {
                label: "Luxe",
                color: "hsl(var(--chart-5))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={ratePriceTierData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tickFormatter={(value) => value.split(' ')[0]}
                />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line type="monotone" dataKey="budget" stroke="var(--color-budget)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="economy" stroke="var(--color-economy)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="moderate" stroke="var(--color-moderate)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="upscale" stroke="var(--color-upscale)" strokeWidth={2} dot={{ r: 2 }} />
                <Line type="monotone" dataKey="luxury" stroke="var(--color-luxury)" strokeWidth={2} dot={{ r: 2 }} />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Willingness to Pay Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Disposition à Payer</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              bookings: {
                label: "Réservations",
                color: "hsl(var(--chart-1))",
              },
              averageRate: {
                label: "Taux Moyen",
                color: "hsl(var(--chart-2))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={willingnessToPayData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date"
                  interval={2}
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar
                  yAxisId="left"
                  dataKey="bookings"
                  fill="var(--color-bookings)"
                  opacity={0.8}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="averageRate"
                  stroke="var(--color-averageRate)"
                  strokeWidth={2}
                  dot={false}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  )
}

