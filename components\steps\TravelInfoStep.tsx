'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm as useReactForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useForm } from '../../contexts/FormContext'
import type { TravelInfo } from '../../types/form'

const travelInfoSchema = z.object({
  arrivalDate: z.string().min(1, 'Arrival date is required'),
  departureDate: z.string().min(1, 'Departure date is required'),
  idType: z.string().min(1, 'ID type is required'),
  idNumber: z.string().min(1, 'ID number is required'),
  nationality: z.string().min(1, 'Nationality is required'),
  purposeOfStay: z.string().min(1, 'Purpose of stay is required'),
})

export function TravelInfoStep() {
  const { updateFormData, nextStep, prevStep } = useForm()
  const form = useReactForm<TravelInfo>({
    resolver: zodResolver(travelInfoSchema),
  })

  const onSubmit = (data: TravelInfo) => {
    updateFormData(data)
    nextStep()
  }

  return (
    <div className="p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="arrivalDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Date d'arrivée *</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      type="date"
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="departureDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Date de départ *</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      type="date"
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="idType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Type de pièce d'identité *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors">
                        <SelectValue placeholder="Sélectionner un type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="passport">Passeport</SelectItem>
                      <SelectItem value="national-id">Carte d'identité nationale</SelectItem>
                      <SelectItem value="driving-license">Permis de conduire</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="idNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Numéro de pièce d'identité *</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez le numéro"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="nationality"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Nationalité *</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez votre nationalité"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="purposeOfStay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Motif du séjour *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors">
                        <SelectValue placeholder="Sélectionner un motif" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="tourism">Tourisme</SelectItem>
                      <SelectItem value="business">Affaires</SelectItem>
                      <SelectItem value="family">Famille</SelectItem>
                      <SelectItem value="study">Études</SelectItem>
                      <SelectItem value="other">Autre</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-between pt-6 border-t border-gray-100">
            <Button 
              type="button"
              variant="outline"
              onClick={prevStep}
              className="h-12 px-8 border-2 border-gray-300 hover:bg-gray-50 rounded-xl font-semibold"
            >
              Précédent
            </Button>
            <Button 
              type="submit"
              className="h-12 px-8 bg-institutional-primary hover:bg-green-600 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 font-semibold"
            >
              Continuer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
