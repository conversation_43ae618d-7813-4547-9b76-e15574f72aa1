/**
 * Test script for PDF generation endpoints
 * Run this in browser console to test PDF generation
 */

async function testPDFGeneration() {
  console.log('🧪 Testing PDF Generation...')
  
  const testData = {
    nom: '<PERSON><PERSON>',
    prenom: '<PERSON>',
    dateNaissance: '1985-03-15',
    sexe: 'M',
    lieu: 'Paris',
    numeroPieceIdentite: 'AB123456',
    typePieceIdentite: 'Carte Nationale d\'Identité',
    pays: 'France',
    villeResidence: 'Paris',
    domicileHabituel: '123 Rue de la République, 75001 Paris',
    categorieSocioPro: 'Employé',
    dateArrivee: '2024-01-15',
    dateDepart: '2024-01-22',
    motifSejour: 'Tourisme'
  }

  // Test declaration PDF
  try {
    console.log('📄 Testing declaration PDF generation...')
    const declarationResponse = await fetch('/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    })

    if (declarationResponse.ok) {
      const blob = await declarationResponse.blob()
      console.log('✅ Declaration PDF generated successfully!', {
        size: blob.size,
        type: blob.type
      })
      
      // Download the PDF for testing
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = 'test_declaration.pdf'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
    } else {
      const errorText = await declarationResponse.text()
      console.error('❌ Declaration PDF generation failed:', declarationResponse.status, errorText)
    }
  } catch (error) {
    console.error('❌ Declaration PDF test error:', error)
  }

  // Test rental agreement PDF
  try {
    console.log('📄 Testing rental agreement PDF generation...')
    const rentalResponse = await fetch('/api/generate-rental-agreement', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    })

    if (rentalResponse.ok) {
      const blob = await rentalResponse.blob()
      console.log('✅ Rental agreement PDF generated successfully!', {
        size: blob.size,
        type: blob.type
      })
      
      // Download the PDF for testing
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = 'test_rental_agreement.pdf'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
    } else {
      const errorText = await rentalResponse.text()
      console.error('❌ Rental agreement PDF generation failed:', rentalResponse.status, errorText)
    }
  } catch (error) {
    console.error('❌ Rental agreement PDF test error:', error)
  }

  console.log('🎉 PDF generation tests completed!')
}

// Auto-run if in browser
if (typeof window !== 'undefined' && window.location) {
  console.log('PDF Generation Tester loaded. Run: testPDFGeneration()')
  window.testPDFGeneration = testPDFGeneration
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testPDFGeneration }
}
