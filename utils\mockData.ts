// Moroccan cities with coordinates
export const moroccanCities = [
  { name: "Casablanca", lat: 33.5731, lng: -7.5898, region: "Casablanca-Settat" },
  { name: "Rabat", lat: 34.0209, lng: -6.8416, region: "Rabat-Salé-Kénitra" },
  { name: "Marrakech", lat: 31.6295, lng: -7.9811, region: "Marrakech-Safi" },
  { name: "<PERSON><PERSON>", lat: 34.0333, lng: -5.0000, region: "Fès-Meknès" },
  { name: "<PERSON><PERSON>", lat: 35.7595, lng: -5.8340, region: "Tanger-Tétouan-Al Hoceïma" },
  { name: "A<PERSON><PERSON>", lat: 30.4278, lng: -9.5981, region: "Souss-Massa" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", lat: 33.8935, lng: -5.5473, region: "Fès-Meknès" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", lat: 34.6814, lng: -1.9086, region: "Oriental" },
  { name: "Kenitra", lat: 34.2610, lng: -6.5802, region: "Rabat-Salé-Kénitra" },
  { name: "Tétouan", lat: 35.5889, lng: -5.3626, region: "Tanger-Tétouan-<PERSON>ma" },
  { name: "Safi", lat: 32.2994, lng: -9.2372, region: "Marrakech-Safi" },
  { name: "Mohammedia", lat: 33.6866, lng: -7.3830, region: "Casablanca-Settat" },
  { name: "Khouribga", lat: 32.8811, lng: -6.9063, region: "Béni Mellal-Khénifra" },
  { name: "El Jadida", lat: 33.2316, lng: -8.5007, region: "Casablanca-Settat" },
  { name: "Béni Mellal", lat: 32.3373, lng: -6.3498, region: "Béni Mellal-Khénifra" },
  { name: "Nador", lat: 35.1681, lng: -2.9287, region: "Oriental" },
  { name: "Taza", lat: 34.2133, lng: -4.0103, region: "Fès-Meknès" },
  { name: "Settat", lat: 33.0013, lng: -7.6217, region: "Casablanca-Settat" },
  { name: "Larache", lat: 35.1932, lng: -6.1563, region: "Tanger-Tétouan-Al Hoceïma" },
  { name: "Ksar El Kebir", lat: 35.0017, lng: -5.9058, region: "Tanger-Tétouan-Al Hoceïma" },
  { name: "Khemisset", lat: 33.8244, lng: -6.0661, region: "Rabat-Salé-Kénitra" },
  { name: "Guelmim", lat: 28.9870, lng: -10.0574, region: "Guelmim-Oued Noun" },
  { name: "Berrechid", lat: 33.2650, lng: -7.5800, region: "Casablanca-Settat" },
  { name: "Ouarzazate", lat: 30.9335, lng: -6.9370, region: "Drâa-Tafilalet" },
  { name: "Tiznit", lat: 29.6974, lng: -9.7316, region: "Souss-Massa" },
  { name: "Errachidia", lat: 31.9314, lng: -4.4244, region: "Drâa-Tafilalet" },
  { name: "Essaouira", lat: 31.5085, lng: -9.7595, region: "Marrakech-Safi" },
  { name: "Dakhla", lat: 23.7185, lng: -15.9570, region: "Dakhla-Oued Ed-Dahab" },
  { name: "Laâyoune", lat: 27.1253, lng: -13.1625, region: "Laâyoune-Sakia El Hamra" },
  { name: "Ifrane", lat: 33.5228, lng: -5.1106, region: "Fès-Meknès" }
];

// Generate comprehensive mock data for admin dashboard
function generateRandomDate(start: Date, end: Date): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString().split('T')[0];
}

function generateRandomTime(): string {
  const hours = Math.floor(Math.random() * 24).toString().padStart(2, '0');
  const minutes = Math.floor(Math.random() * 60).toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

function generateICE(): string {
  return Math.floor(Math.random() * 900000000 + 100000000).toString();
}

function generateCIN(): string {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const letter1 = letters[Math.floor(Math.random() * letters.length)];
  const letter2 = letters[Math.floor(Math.random() * letters.length)];
  const numbers = Math.floor(Math.random() * 900000 + 100000);
  return `${letter1}${letter2}${numbers}`;
}

function generatePhone(): string {
  const prefixes = ['06', '07', '05'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const number = Math.floor(Math.random() * 90000000 + 10000000);
  return `${prefix}${number}`;
}

// Host data with multiple accommodations
export const adminHosts = Array.from({ length: 50 }, (_, i) => {
  const city = moroccanCities[Math.floor(Math.random() * moroccanCities.length)];
  const accommodationCount = Math.floor(Math.random() * 8) + 1; // 1-8 accommodations per host

  return {
    id: i + 1,
    nom: `Host${i + 1}`,
    prenom: `Prénom${i + 1}`,
    email: `host${i + 1}@example.ma`,
    telephone: generatePhone(),
    cin: generateCIN(),
    ice: generateICE(),
    ville: city.name,
    region: city.region,
    adresse: `${Math.floor(Math.random() * 999) + 1} Rue ${i + 1}, ${city.name}`,
    dateInscription: generateRandomDate(new Date(2020, 0, 1), new Date()),
    statut: Math.random() > 0.1 ? 'actif' : 'inactif',
    nombreLogements: accommodationCount,
    totalDeclarations: Math.floor(Math.random() * 200) + 10,
    derniereConnexion: generateRandomDate(new Date(2024, 0, 1), new Date()),
    coordinates: {
      lat: city.lat + (Math.random() - 0.5) * 0.1,
      lng: city.lng + (Math.random() - 0.5) * 0.1
    }
  };
});

// Accommodation types
const accommodationTypes = [
  'Villa', 'Appartement', 'Riad', 'Maison', 'Studio', 'Loft', 'Chalet', 'Dar'
];

// Generate accommodations linked to hosts
export const adminAccommodations = adminHosts.flatMap(host =>
  Array.from({ length: host.nombreLogements }, (_, i) => {
    const type = accommodationTypes[Math.floor(Math.random() * accommodationTypes.length)];
    const capacity = Math.floor(Math.random() * 8) + 2; // 2-10 people
    const currentOccupancy = Math.floor(Math.random() * capacity);

    return {
      id: `${host.id}-${i + 1}`,
      hostId: host.id,
      nom: `${type} ${host.nom} ${i + 1}`,
      type,
      ville: host.ville,
      region: host.region,
      adresse: `${Math.floor(Math.random() * 999) + 1} ${type} Street, ${host.ville}`,
      capacite: capacity,
      occupationActuelle: currentOccupancy,
      statut: currentOccupancy > 0 ? 'occupé' : 'libre',
      tarif: Math.floor(Math.random() * 2000) + 300, // 300-2300 MAD
      codeLogement: `ACC${host.id}${(i + 1).toString().padStart(2, '0')}`,
      dateCreation: generateRandomDate(new Date(2020, 0, 1), new Date()),
      coordinates: {
        lat: host.coordinates.lat + (Math.random() - 0.5) * 0.02,
        lng: host.coordinates.lng + (Math.random() - 0.5) * 0.02
      },
      amenities: ['WiFi', 'Climatisation', 'Parking', 'Cuisine équipée'].filter(() => Math.random() > 0.3),
      photos: [`/accommodation-${Math.floor(Math.random() * 10) + 1}.jpg`],
      description: `Magnifique ${type.toLowerCase()} situé au cœur de ${host.ville}`,
      totalDeclarations: Math.floor(Math.random() * 50) + 1
    };
  })
);

// Helper functions for law enforcement data
function generateSecurityFlags() {
  const isWatchListed = Math.random() < 0.05; // 5% chance
  const riskLevels = ['low', 'medium', 'high', 'critical'];
  const riskLevel = isWatchListed ? riskLevels[Math.floor(Math.random() * 4)] : 'low';
  const flaggedReasons = isWatchListed ?
    ['Previous incident', 'Document irregularity', 'Travel pattern alert'].filter(() => Math.random() > 0.5) :
    [];

  return {
    isWatchListed,
    riskLevel,
    flaggedReasons,
    lastSecurityCheck: generateRandomDate(new Date(2024, 0, 1), new Date())
  };
}

function generateDocumentVerification(guestId: string) {
  return {
    idPhotoUrl: `/documents/id_${guestId}_front.jpg`,
    idBackPhotoUrl: `/documents/id_${guestId}_back.jpg`,
    signatureUrl: `/documents/signature_${guestId}.jpg`,
    selfieUrl: `/documents/selfie_${guestId}.jpg`,
    biometricData: Math.random() > 0.7 ? {
      fingerprint: `FP_${guestId}_${Math.random().toString(36).substr(2, 9)}`,
      faceRecognition: `FR_${guestId}_${Math.random().toString(36).substr(2, 9)}`
    } : undefined,
    verificationHistory: [
      {
        timestamp: generateRandomDate(new Date(2024, 0, 1), new Date()),
        verifiedBy: `Officer${Math.floor(Math.random() * 50) + 1}`,
        status: ['approved', 'rejected', 'pending'][Math.floor(Math.random() * 3)] as 'approved' | 'rejected' | 'pending',
        notes: 'Document verification completed'
      }
    ]
  };
}

function generateTravelHistory() {
  const countries = ['France', 'Espagne', 'Allemagne', 'Italie', 'Belgique', 'Pays-Bas'];
  const entryPoints = ['Aéroport Mohammed V', 'Port de Tanger', 'Aéroport de Marrakech', 'Frontière Ceuta'];

  return {
    previousCountries: countries.filter(() => Math.random() > 0.6),
    entryPoints: [entryPoints[Math.floor(Math.random() * entryPoints.length)]],
    visaStatus: Math.random() > 0.8 ? 'Tourist Visa' : 'No Visa Required',
    passportExpiry: Math.random() > 0.5 ? generateRandomDate(new Date(), new Date(2030, 0, 1)) : undefined
  };
}

function generateEmergencyContacts() {
  return [
    {
      name: `Contact${Math.floor(Math.random() * 100)}`,
      relationship: ['Époux/Épouse', 'Parent', 'Ami', 'Collègue'][Math.floor(Math.random() * 4)],
      phone: generatePhone(),
      address: `${Math.floor(Math.random() * 999) + 1} Rue Contact, Ville`
    }
  ];
}

function generateInvestigationNotes(guestId: string) {
  const noteCount = Math.floor(Math.random() * 3);
  return Array.from({ length: noteCount }, (_, i) => ({
    id: `NOTE_${guestId}_${i + 1}`,
    timestamp: generateRandomDate(new Date(2024, 0, 1), new Date()),
    officer: `Officer${Math.floor(Math.random() * 20) + 1}`,
    note: [
      'Documents verified successfully',
      'Guest cooperative during check-in',
      'No suspicious activity observed',
      'Follow-up required for travel history',
      'Additional verification completed'
    ][Math.floor(Math.random() * 5)],
    category: ['general', 'suspicious', 'verified', 'follow-up'][Math.floor(Math.random() * 4)] as 'general' | 'suspicious' | 'verified' | 'follow-up'
  }));
}

// Generate guest declarations
export const adminDeclarations = Array.from({ length: 500 }, (_, i) => {
  const accommodation = adminAccommodations[Math.floor(Math.random() * adminAccommodations.length)];
  const host = adminHosts.find(h => h.id === accommodation.hostId)!;
  const checkIn = generateRandomDate(new Date(2024, 0, 1), new Date());
  const checkInDate = new Date(checkIn);
  const checkOutDate = new Date(checkInDate.getTime() + (Math.floor(Math.random() * 14) + 1) * 24 * 60 * 60 * 1000);
  const checkOut = checkOutDate.toISOString().split('T')[0];
  const guestId = `GUEST${(i + 1).toString().padStart(4, '0')}`;

  return {
    id: i + 1,
    guestId,
    accommodationId: accommodation.id,
    hostId: host.id,

    // Guest personal info
    nom: `Nom${i + 1}`,
    prenom: `Prénom${i + 1}`,
    dateNaissance: generateRandomDate(new Date(1950, 0, 1), new Date(2005, 0, 1)),
    sexe: Math.random() > 0.5 ? 'M' : 'F',
    nationalite: Math.random() > 0.8 ? 'Française' : 'Marocaine',
    numeroPieceIdentite: generateCIN(),
    typePieceIdentite: 'CIN',
    telephone: generatePhone(),
    email: `guest${i + 1}@example.com`,

    // Stay information
    dateArrivee: checkIn,
    heureArrivee: generateRandomTime(),
    dateDepart: checkOut,
    heureDepart: generateRandomTime(),
    motifSejour: ['Tourisme', 'Affaires', 'Famille', 'Études'][Math.floor(Math.random() * 4)],
    nombrePersonnes: Math.floor(Math.random() * 4) + 1,

    // Location info
    ville: accommodation.ville,
    region: accommodation.region,
    nomLogement: accommodation.nom,
    adresseLogement: accommodation.adresse,

    // Declaration info
    dateDeclaration: generateRandomDate(new Date(checkInDate.getTime() - 7 * 24 * 60 * 60 * 1000), checkInDate),
    heureDeclaration: generateRandomTime(),
    statut: ['en_cours', 'validée', 'expirée'][Math.floor(Math.random() * 3)],

    // Verification status
    verificationStatut: ['en_attente', 'vérifié', 'rejeté'][Math.floor(Math.random() * 3)],
    documentsVerifies: Math.random() > 0.2,

    // Law Enforcement Specific Fields
    securityFlags: generateSecurityFlags(),
    documentVerification: generateDocumentVerification(guestId),
    travelHistory: generateTravelHistory(),
    emergencyContacts: generateEmergencyContacts(),
    investigationNotes: generateInvestigationNotes(guestId),

    // Coordinates for mapping
    coordinates: accommodation.coordinates
  };
});

// Legacy data for backward compatibility
export const accommodations = adminAccommodations.slice(0, 5).map(acc => ({
  id: parseInt(acc.id.split('-')[0]),
  name: acc.nom,
  city: acc.ville
}));

export const mockData = adminDeclarations.slice(0, 5).map(decl => ({
  id: decl.id,
  nomLogement: decl.nomLogement,
  ville: decl.ville,
  prenom: decl.prenom,
  nom: decl.nom,
  enfants: Math.floor(Math.random() * 3),
  dateArrivee: decl.dateArrivee,
  dateDepart: decl.dateDepart,
  dateTraitement: decl.dateDeclaration,
}));

