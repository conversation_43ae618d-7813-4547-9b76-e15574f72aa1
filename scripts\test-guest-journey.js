/**
 * Test script for Guest Journey validation
 * Run this in browser console to test the complete flow
 */

class GuestJourneyTester {
  constructor() {
    this.testResults = []
    this.currentStep = 0
    this.steps = [
      'Welcome Page Load',
      'QR Code Scanner',
      'Accommodation Code Input',
      'Form Data Entry',
      'Verification Steps',
      'Data Storage',
      'PDF Generation',
      'Summary Display'
    ]
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`
    console.log(logEntry)
    this.testResults.push({ timestamp, type, message })
  }

  async testWelcomePage() {
    this.log('Testing Welcome Page...', 'test')
    
    try {
      // Check if welcome page elements exist
      const qrButton = document.querySelector('button:contains("Scanner avec QR Code")')
      const codeButton = document.querySelector('button:contains("Code d\'hébergement")')
      
      if (qrButton && codeButton) {
        this.log('✅ Welcome page buttons found', 'success')
        return true
      } else {
        this.log('❌ Welcome page buttons not found', 'error')
        return false
      }
    } catch (error) {
      this.log(`❌ Welcome page test failed: ${error.message}`, 'error')
      return false
    }
  }

  async testDataStorage() {
    this.log('Testing Data Storage utilities...', 'test')
    
    try {
      // Test if GuestDataStorage is available
      if (typeof window.GuestDataStorage === 'undefined') {
        // Try to import it
        const { GuestDataStorage } = await import('../utils/dataStorage.ts')
        window.GuestDataStorage = GuestDataStorage
      }

      // Test data initialization
      const testCode = 'T-123456'
      const guestData = window.GuestDataStorage.initializeGuestData(testCode)
      
      if (guestData && guestData.guestId && guestData.accommodationCode === testCode) {
        this.log('✅ Guest data initialization works', 'success')
        
        // Test data saving
        window.GuestDataStorage.saveToSession(guestData)
        const retrieved = window.GuestDataStorage.getFromSession()
        
        if (retrieved.guestId === guestData.guestId) {
          this.log('✅ Data storage and retrieval works', 'success')
          return true
        } else {
          this.log('❌ Data retrieval failed', 'error')
          return false
        }
      } else {
        this.log('❌ Guest data initialization failed', 'error')
        return false
      }
    } catch (error) {
      this.log(`❌ Data storage test failed: ${error.message}`, 'error')
      return false
    }
  }

  async testEncryption() {
    this.log('Testing Encryption utilities...', 'test')
    
    try {
      // Test if DataEncryption is available
      if (typeof window.DataEncryption === 'undefined') {
        const { DataEncryption } = await import('../utils/encryption.ts')
        window.DataEncryption = DataEncryption
      }

      // Test encryption/decryption
      const testData = { name: 'Test User', id: '123456' }
      const encrypted = await window.DataEncryption.encryptGuestData(testData)
      
      if (encrypted.encryptedData && encrypted.keyData && encrypted.iv) {
        this.log('✅ Data encryption works', 'success')
        
        // Test decryption
        const decrypted = await window.DataEncryption.decryptGuestData(
          encrypted.encryptedData,
          encrypted.keyData,
          encrypted.iv
        )
        
        if (decrypted.name === testData.name && decrypted.id === testData.id) {
          this.log('✅ Data decryption works', 'success')
          return true
        } else {
          this.log('❌ Data decryption failed', 'error')
          return false
        }
      } else {
        this.log('❌ Data encryption failed', 'error')
        return false
      }
    } catch (error) {
      this.log(`❌ Encryption test failed: ${error.message}`, 'error')
      return false
    }
  }

  async testAPIEndpoints() {
    this.log('Testing API endpoints...', 'test')
    
    try {
      // Test PDF generation endpoint
      const testData = {
        nom: 'Test',
        prenom: 'User',
        dateNaissance: '1990-01-01',
        numeroPieceIdentite: 'TEST123'
      }

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      })

      if (response.ok) {
        this.log('✅ PDF generation endpoint works', 'success')
      } else {
        this.log(`❌ PDF generation failed: ${response.status}`, 'error')
        return false
      }

      // Test rental agreement endpoint
      const rentalResponse = await fetch('/api/generate-rental-agreement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      })

      if (rentalResponse.ok) {
        this.log('✅ Rental agreement endpoint works', 'success')
        return true
      } else {
        this.log(`❌ Rental agreement failed: ${rentalResponse.status}`, 'error')
        return false
      }
    } catch (error) {
      this.log(`❌ API endpoint test failed: ${error.message}`, 'error')
      return false
    }
  }

  async testDataFolderStructure() {
    this.log('Testing Data folder structure...', 'test')
    
    try {
      // Check if data folder structure exists by testing file paths
      const expectedPaths = [
        '/data/README.md',
        '/data/declarations/.gitkeep',
        '/data/images/id_documents/.gitkeep',
        '/data/images/selfies/.gitkeep',
        '/data/images/signatures/.gitkeep',
        '/data/rental_agreements/.gitkeep',
        '/data/encrypted/.gitkeep',
        '/data/temp/.gitkeep'
      ]

      let pathsExist = 0
      for (const path of expectedPaths) {
        try {
          const response = await fetch(path, { method: 'HEAD' })
          if (response.ok || response.status === 404) {
            pathsExist++
          }
        } catch (e) {
          // Path might not be accessible via HTTP, which is expected
          pathsExist++
        }
      }

      if (pathsExist >= expectedPaths.length * 0.8) {
        this.log('✅ Data folder structure appears to be in place', 'success')
        return true
      } else {
        this.log('❌ Data folder structure incomplete', 'error')
        return false
      }
    } catch (error) {
      this.log(`❌ Data folder test failed: ${error.message}`, 'error')
      return false
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Guest Journey Tests...', 'info')
    
    const tests = [
      { name: 'Welcome Page', fn: () => this.testWelcomePage() },
      { name: 'Data Storage', fn: () => this.testDataStorage() },
      { name: 'Encryption', fn: () => this.testEncryption() },
      { name: 'API Endpoints', fn: () => this.testAPIEndpoints() },
      { name: 'Data Folder Structure', fn: () => this.testDataFolderStructure() }
    ]

    let passed = 0
    let failed = 0

    for (const test of tests) {
      this.log(`Running ${test.name} test...`, 'test')
      try {
        const result = await test.fn()
        if (result) {
          passed++
        } else {
          failed++
        }
      } catch (error) {
        this.log(`Test ${test.name} threw error: ${error.message}`, 'error')
        failed++
      }
    }

    this.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`, 'info')
    
    if (failed === 0) {
      this.log('🎉 All tests passed! Guest journey is ready.', 'success')
    } else {
      this.log('⚠️ Some tests failed. Please check the implementation.', 'warning')
    }

    return { passed, failed, results: this.testResults }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.type === 'success').length,
        failed: this.testResults.filter(r => r.type === 'error').length,
        warnings: this.testResults.filter(r => r.type === 'warning').length
      },
      details: this.testResults
    }

    console.table(report.summary)
    return report
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.GuestJourneyTester = GuestJourneyTester
}

// Auto-run if in browser
if (typeof window !== 'undefined' && window.location) {
  console.log('Guest Journey Tester loaded. Run: new GuestJourneyTester().runAllTests()')
}
