import { NextRequest, NextResponse } from 'next/server';
import { generateSimplePDFContent } from '@/utils/pdfGenerator';

export async function POST(req: NextRequest) {
  try {
    // Validate request
    if (!req.body) {
      return new NextResponse('Request body is required', { status: 400 });
    }

    const data = await req.json();

    // Validate data
    if (!data || typeof data !== 'object') {
      return new NextResponse('Invalid data format', { status: 400 });
    }

    // Try PDFKit first, fallback to simple PDF generation
    let pdfBuffer: Buffer;

    try {
      // Use dynamic import to avoid bundling issues
      const PDFDocument = (await import('pdfkit')).default;

      // Create a new PDF document with minimal configuration
      const doc = new PDFDocument({
        margin: 50,
        bufferPages: true
      });
      let buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {});

      // Add content to the PDF
      doc.fontSize(18).text('DÉCLARATION DE SÉJOUR TOURISTIQUE', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12);

      // Add timestamp
      doc.text(`Généré le: ${new Date().toLocaleDateString('fr-FR')}`, { align: 'right' });
      doc.moveDown();

      // Add data fields in a structured way
      doc.fontSize(14).text('INFORMATIONS PERSONNELLES:', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Nom: ${data.nom || 'Non renseigné'}`);
      doc.text(`Prénom: ${data.prenom || 'Non renseigné'}`);
      doc.text(`Date de naissance: ${data.dateNaissance || 'Non renseignée'}`);
      doc.text(`Sexe: ${data.sexe || 'Non renseigné'}`);
      doc.moveDown();

      doc.fontSize(14).text('PIÈCE D\'IDENTITÉ:', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Type: ${data.typePieceIdentite || 'Non renseigné'}`);
      doc.text(`Numéro: ${data.numeroPieceIdentite || 'Non renseigné'}`);
      doc.moveDown();

      doc.fontSize(14).text('SÉJOUR:', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Date d'arrivée: ${data.dateArrivee || 'Non renseignée'}`);
      doc.text(`Date de départ: ${data.dateDepart || 'Non renseignée'}`);
      doc.text(`Motif: ${data.motifSejour || 'Non renseigné'}`);

      // Finalize the PDF
      doc.end();

      // Combine the PDF buffers
      pdfBuffer = Buffer.concat(buffers);

    } catch (pdfkitError) {
      console.warn('PDFKit failed, using fallback PDF generation:', pdfkitError);

      // Fallback to simple PDF generation
      const simplePDFContent = generateSimplePDFContent(data, 'declaration');
      pdfBuffer = Buffer.from(simplePDFContent, 'utf-8');
    }

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename=declaration_locataire.pdf',
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    console.error('PDF generation error:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}

