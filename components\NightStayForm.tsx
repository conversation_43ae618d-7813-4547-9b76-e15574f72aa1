'use client'

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useIsMobile } from '@/hooks/use-mobile'
import { Camera, ArrowLeft, ArrowRight, Calendar, User, MapPin, Briefcase, Home, Globe, CreditCard } from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { OCRScanner } from './OCRScanner'
import { GuestDataStorage } from '@/utils/dataStorage'

const formSchema = z.object({
  dateArrivee: z.string().min(1, "La date d'arrivée est requise"),
  dateDepart: z.string().min(1, "La date de départ est requise"),
  numeroPieceIdentite: z.string().min(1, "Le numéro de pièce d'identité est requis"),
  typePieceIdentite: z.string().min(1, "Le type de pièce d'identité est requis"),
  nom: z.string().min(1, "Le nom est requis"),
  prenom: z.string().min(1, "Le prénom est requis"),
  sexe: z.string().min(1, "Le sexe est requis"),
  dateNaissance: z.string().min(1, "La date de naissance est requise"),
  lieu: z.string().min(1, "Le lieu est requis"),
  categorieSocioPro: z.string().min(1, "La catégorie socio-professionnelle est requise"),
  domicileHabituel: z.string().min(1, "Le domicile habituel est requis"),
  pays: z.string().min(1, "Le pays est requis"),
  villeResidence: z.string().min(1, "La ville de résidence est requise"),
  motifSejour: z.string().min(1, "Le motif du séjour est requis"),
})

export function NightStayForm() {
  const router = useRouter()
  const [showScanner, setShowScanner] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [accommodationCode, setAccommodationCode] = useState<string>('')
  const isMobile = useIsMobile()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dateArrivee: "",
      dateDepart: "",
      numeroPieceIdentite: "",
      typePieceIdentite: "",
      nom: "",
      prenom: "",
      sexe: "",
      dateNaissance: "",
      lieu: "",
      categorieSocioPro: "",
      domicileHabituel: "",
      pays: "",
      villeResidence: "",
      motifSejour: "",
    },
  })

  // Check for accommodation code from QR scan or manual entry
  useEffect(() => {
    const storedCode = sessionStorage.getItem('accommodationCode')
    if (storedCode) {
      setAccommodationCode(storedCode)
      // Initialize guest data with accommodation code
      const guestData = GuestDataStorage.initializeGuestData(storedCode)
      GuestDataStorage.saveToSession(guestData)
      // Don't clear the code yet - keep it for potential additional guests
    }

    // Load existing form data if available
    const existingData = GuestDataStorage.getFromSession()
    if (existingData.personalInfo) {
      // Populate form with existing data
      Object.entries(existingData.personalInfo).forEach(([key, value]) => {
        if (value) {
          form.setValue(key as any, value as string)
        }
      })

      if (existingData.stayInfo) {
        Object.entries(existingData.stayInfo).forEach(([key, value]) => {
          if (value) {
            form.setValue(key as any, value as string)
          }
        })
      }
    }
  }, [])

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)
    try {
      console.log(values)

      // Save form data to storage
      GuestDataStorage.updatePersonalInfo(values)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Navigate to verification
      router.push('/verification-self-check-in')
    } catch (error) {
      console.error('Error submitting form:', error)
      // TODO: Add toast notification for error handling
      alert('Une erreur est survenue lors de la soumission du formulaire. Veuillez réessayer.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleScanComplete = (data: any) => {
    // Handle OCR scan results
    console.log('OCR scan completed:', data)

    // Populate form fields with the scanned data
    if (data) {
      const formUpdates = {
        nom: data.nom || '',
        prenom: data.prenom || '',
        sexe: data.sexe || '',
        dateNaissance: data.dateNaissance || '',
        lieu: data.lieu || '',
        domicileHabituel: data.domicileActuel || '',
        villeResidence: data.ville || '',
        numeroPieceIdentite: data.numeroPieceIdentite || '',
        typePieceIdentite: data.typePieceIdentite || ''
      }

      // Update form
      Object.entries(formUpdates).forEach(([key, value]) => {
        if (value) {
          form.setValue(key as any, value)
        }
      })

      // Save to storage
      GuestDataStorage.updatePersonalInfo(formUpdates)
    }

    setShowScanner(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">

        <Card className="w-full max-w-2xl mx-auto bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card animate-slide-up-from-bottom">
          <CardHeader className="mobile-padding pb-4 sm:pb-6 relative">
            <div className="flex items-center gap-3 mb-4">
              <Button
                variant="ghost"
                onClick={() => router.push('/dashboard')}
                className="touch-target text-gray-600 hover:text-institutional-primary p-2"
                disabled={isLoading}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <CardTitle className="mobile-text-xl font-bold text-gray-900 flex-1">
                Self Check-in
              </CardTitle>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-1">
                  {isMobile ? 'Déclaration Nuités' : 'Renseignement des Nuités'}
                </h2>
                <p className="text-gray-600 text-sm sm:text-base mt-2">
                  Remplissez les informations du séjour
                </p>
                {accommodationCode && (
                  <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-green-800 text-xs font-medium">
                      Code d'hébergement: <code className="font-mono">{accommodationCode}</code>
                    </p>
                  </div>
                )}
              </div>

              {/* Enhanced Scan Button */}
              <Button
                variant="outline"
                size={isMobile ? "default" : "sm"}
                onClick={() => setShowScanner(true)}
                disabled={isLoading}
                className="mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200 touch-target"
              >
                <Camera className="w-4 h-4 mr-2" />
                {isMobile ? 'Scanner Document' : 'Scan Rapide'}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="mobile-padding pt-0">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 sm:space-y-8 animate-float-up-delay-1">

                {/* Enhanced Section 1: Dates de séjour */}
                <div className="form-section">
                  <div className="form-section-header">
                    <Calendar className="form-section-icon" />
                    <h3 className="form-section-title">Dates de séjour</h3>
                    <Badge variant="outline" className="ml-auto text-xs bg-blue-50 text-blue-700">
                      Obligatoire
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                    <FormField
                      control={form.control}
                      name="dateArrivee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Date d'arrivée</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dateDepart"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Date de départ</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Enhanced Section 2: Pièce d'identité */}
                <div className="form-section">
                  <div className="form-section-header">
                    <CreditCard className="form-section-icon" />
                    <h3 className="form-section-title">Pièce d'identité</h3>
                    <Badge variant="outline" className="ml-auto text-xs bg-blue-50 text-blue-700">
                      Obligatoire
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">

                    <FormField
                      control={form.control}
                      name="typePieceIdentite"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Type de pièce d'identité</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                            <FormControl>
                              <SelectTrigger className="mobile-input mobile-card">
                                <SelectValue placeholder="Sélectionner un type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="passport">Passeport</SelectItem>
                              <SelectItem value="cni">Carte Nationale d'Identité</SelectItem>
                              <SelectItem value="carte-sejour">Carte de Séjour</SelectItem>
                              <SelectItem value="autre">Autre</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="numeroPieceIdentite"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Numéro de pièce d'identité</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Ex: AB123456"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Enhanced Section 3: Informations personnelles */}
                <div className="form-section">
                  <div className="form-section-header">
                    <User className="form-section-icon" />
                    <h3 className="form-section-title">Informations personnelles</h3>
                    <Badge variant="outline" className="ml-auto text-xs bg-blue-50 text-blue-700">
                      Obligatoire
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">

                    <FormField
                      control={form.control}
                      name="nom"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Nom</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Nom de famille"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="prenom"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Prénom</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Prénom"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sexe"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Sexe</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                            <FormControl>
                              <SelectTrigger className="mobile-input mobile-card">
                                <SelectValue placeholder="Sélectionner" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="M">Masculin</SelectItem>
                              <SelectItem value="F">Féminin</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dateNaissance"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Date de naissance</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lieu"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Lieu de naissance</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Ville de naissance"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="categorieSocioPro"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Catégorie socio-professionnelle</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                            <FormControl>
                              <SelectTrigger className="mobile-input mobile-card">
                                <SelectValue placeholder="Sélectionner une catégorie" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="cadre">Cadre</SelectItem>
                              <SelectItem value="employe">Employé</SelectItem>
                              <SelectItem value="ouvrier">Ouvrier</SelectItem>
                              <SelectItem value="etudiant">Étudiant</SelectItem>
                              <SelectItem value="retraite">Retraité</SelectItem>
                              <SelectItem value="autre">Autre</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Enhanced Section 4: Adresse et résidence */}
                <div className="form-section">
                  <div className="form-section-header">
                    <Home className="form-section-icon" />
                    <h3 className="form-section-title">Adresse et résidence</h3>
                    <Badge variant="outline" className="ml-auto text-xs bg-blue-50 text-blue-700">
                      Obligatoire
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                    <FormField
                      control={form.control}
                      name="pays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <Globe className="w-4 h-4 text-institutional-primary" />
                            Pays de résidence
                          </FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                            <FormControl>
                              <SelectTrigger className="mobile-input mobile-card">
                                <SelectValue placeholder="Sélectionner un pays" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="maroc">Maroc</SelectItem>
                              <SelectItem value="france">France</SelectItem>
                              <SelectItem value="espagne">Espagne</SelectItem>
                              <SelectItem value="allemagne">Allemagne</SelectItem>
                              <SelectItem value="italie">Italie</SelectItem>
                              <SelectItem value="autre">Autre</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="villeResidence"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-institutional-primary" />
                            Ville de résidence
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Ville de résidence"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="domicileHabituel"
                      render={({ field }) => (
                        <FormItem className="sm:col-span-2">
                          <FormLabel className="text-sm font-medium text-gray-700">Domicile habituel</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Adresse complète du domicile"
                              className="mobile-input mobile-card"
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Enhanced Section 5: Motif du séjour */}
                <div className="form-section">
                  <div className="form-section-header">
                    <Briefcase className="form-section-icon" />
                    <h3 className="form-section-title">Motif du séjour</h3>
                    <Badge variant="outline" className="ml-auto text-xs bg-green-50 text-green-700">
                      Optionnel
                    </Badge>
                  </div>

                  <FormField
                    control={form.control}
                    name="motifSejour"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700">Motif du séjour</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Ex: Tourisme, Affaires, Famille..."
                            className="mobile-input mobile-card"
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Enhanced Mobile-First Form Actions */}
                <div className="flex justify-end pt-6 border-t border-gray-100">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target flex-1 sm:flex-initial order-1 sm:order-2 relative overflow-hidden group"
                  >
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Traitement...
                      </>
                    ) : (
                      <>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                        <span className="relative z-10">Suivant</span>
                        <ArrowRight className="w-4 h-4 ml-2 relative z-10" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* OCR Scanner Component */}
        <OCRScanner
          open={showScanner}
          onOpenChange={setShowScanner}
          onScanComplete={handleScanComplete}
        />
      </div>
    </div>
  )
}

