'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Building2, Users, MapPin, Eye, Filter,
  Layers, ZoomIn, ZoomOut, Search, AlertTriangle, Shield
} from 'lucide-react'
import { adminAccommodations, adminDeclarations } from '@/utils/mockData'
import { AdminAccommodation, AdminDeclaration } from '@/types/admin'

// Dynamically import map components to avoid SSR issues
const MapContainer = dynamic(
  () => import('react-leaflet').then((mod) => mod.MapContainer),
  { ssr: false }
)
const TileLayer = dynamic(
  () => import('react-leaflet').then((mod) => mod.TileLayer),
  { ssr: false }
)
const Marker = dynamic(
  () => import('react-leaflet').then((mod) => mod.Marker),
  { ssr: false }
)
const Popup = dynamic(
  () => import('react-leaflet').then((mod) => mod.Popup),
  { ssr: false }
)

interface MapMarkerData {
  id: string
  type: 'accommodation' | 'cluster'
  coordinates: { lat: number; lng: number }
  title: string
  subtitle: string
  count: number
  details: any
}

export function AdminMap() {
  const [mapReady, setMapReady] = useState(false)
  const [selectedLayer, setSelectedLayer] = useState<'accommodations' | 'declarations' | 'both'>('both')
  const [markers, setMarkers] = useState<MapMarkerData[]>([])
  const [selectedMarker, setSelectedMarker] = useState<MapMarkerData | null>(null)
  const [addressSearch, setAddressSearch] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)

  useEffect(() => {
    setMapReady(true)
    
    // Process accommodations data for map markers
    const accommodationMarkers: MapMarkerData[] = adminAccommodations.map(acc => ({
      id: acc.id,
      type: 'accommodation' as const,
      coordinates: acc.coordinates,
      title: acc.nom,
      subtitle: `${acc.ville} - ${acc.type}`,
      count: acc.occupationActuelle,
      details: acc
    }))

    setMarkers(accommodationMarkers)
  }, [])

  const handleMarkerClick = (marker: MapMarkerData) => {
    setSelectedMarker(marker)
  }

  const handleAddressSearch = async () => {
    if (!addressSearch.trim()) return

    setIsSearching(true)

    // Simulate address search (in production, use a geocoding service)
    const mockResults = [
      { address: addressSearch, lat: 33.5731, lng: -7.5898, city: 'Casablanca' },
      { address: `${addressSearch}, Rabat`, lat: 34.0209, lng: -6.8416, city: 'Rabat' },
      { address: `${addressSearch}, Marrakech`, lat: 31.6295, lng: -7.9811, city: 'Marrakech' }
    ]

    setTimeout(() => {
      setSearchResults(mockResults)
      setIsSearching(false)
    }, 1000)
  }

  const getMarkerColor = (marker: MapMarkerData) => {
    if (marker.type === 'accommodation') {
      const acc = marker.details as AdminAccommodation
      return acc.statut === 'occupé' ? '#ef4444' : '#10b981' // red for occupied, green for free
    }
    return '#3b82f6' // blue for declarations
  }

  if (!mapReady) {
    return (
      <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-institutional-primary mx-auto mb-2"></div>
          <p className="text-gray-600">Chargement de la carte...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Address Search */}
      <Card className="bg-white border-0 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Search className="w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Rechercher une adresse (ex: Avenue Mohammed V, Casablanca)"
                  value={addressSearch}
                  onChange={(e) => setAddressSearch(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddressSearch()}
                  className="border-0 focus:ring-0 focus:border-0"
                />
              </div>
            </div>
            <Button
              onClick={handleAddressSearch}
              disabled={isSearching || !addressSearch.trim()}
              size="sm"
            >
              {isSearching ? 'Recherche...' : 'Rechercher'}
            </Button>
          </div>

          {searchResults.length > 0 && (
            <div className="mt-3 space-y-2">
              <p className="text-sm font-medium text-gray-700">Résultats de recherche:</p>
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                  onClick={() => {
                    // In production, center map on this location
                    setSearchResults([])
                    setAddressSearch('')
                  }}
                >
                  <div>
                    <p className="text-sm font-medium">{result.address}</p>
                    <p className="text-xs text-gray-500">{result.city}</p>
                  </div>
                  <MapPin className="w-4 h-4 text-institutional-primary" />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Map Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Button
            variant={selectedLayer === 'accommodations' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedLayer('accommodations')}
            className="flex items-center gap-2"
          >
            <Building2 className="w-4 h-4" />
            Logements
          </Button>
          <Button
            variant={selectedLayer === 'declarations' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedLayer('declarations')}
            className="flex items-center gap-2"
          >
            <Users className="w-4 h-4" />
            Déclarations
          </Button>
          <Button
            variant={selectedLayer === 'both' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedLayer('both')}
            className="flex items-center gap-2"
          >
            <Layers className="w-4 h-4" />
            Tout
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Libre
          </Badge>
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
            Occupé
          </Badge>
        </div>
      </div>

      {/* Map Container */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div className="lg:col-span-3">
          <div className="h-96 lg:h-[500px] rounded-lg overflow-hidden border border-gray-200">
            <MapContainer
              center={[31.7917, -7.0926]} // Center of Morocco
              zoom={6}
              style={{ height: '100%', width: '100%' }}
              className="z-0"
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              
              {markers.map((marker) => (
                <Marker
                  key={marker.id}
                  position={[marker.coordinates.lat, marker.coordinates.lng]}
                  eventHandlers={{
                    click: () => handleMarkerClick(marker)
                  }}
                >
                  <Popup>
                    <div className="p-2 min-w-[200px]">
                      <h3 className="font-semibold text-gray-900 mb-2">{marker.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{marker.subtitle}</p>
                      
                      {marker.type === 'accommodation' && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Capacité:</span>
                            <span className="text-xs font-medium">{marker.details.capacite} personnes</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Occupation:</span>
                            <span className="text-xs font-medium">{marker.details.occupationActuelle} personnes</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Statut:</span>
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                marker.details.statut === 'occupé' 
                                  ? 'bg-red-50 text-red-700 border-red-200' 
                                  : 'bg-green-50 text-green-700 border-green-200'
                              }`}
                            >
                              {marker.details.statut}
                            </Badge>
                          </div>
                          <Button size="sm" className="w-full mt-2">
                            <Eye className="w-3 h-3 mr-1" />
                            Voir détails
                          </Button>
                        </div>
                      )}
                    </div>
                  </Popup>
                </Marker>
              ))}
            </MapContainer>
          </div>
        </div>

        {/* Sidebar with details */}
        <div className="space-y-4">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Statistiques de la Carte</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total logements:</span>
                  <span className="font-medium">{adminAccommodations.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Logements occupés:</span>
                  <span className="font-medium text-red-600">
                    {adminAccommodations.filter(acc => acc.statut === 'occupé').length}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Logements libres:</span>
                  <span className="font-medium text-green-600">
                    {adminAccommodations.filter(acc => acc.statut === 'libre').length}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {selectedMarker && (
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Détails Sélectionnés</h3>
                <div className="space-y-2">
                  <p className="text-sm font-medium">{selectedMarker.title}</p>
                  <p className="text-xs text-gray-600">{selectedMarker.subtitle}</p>
                  
                  {selectedMarker.type === 'accommodation' && (
                    <div className="space-y-2 pt-2 border-t border-gray-100">
                      <div className="text-xs">
                        <span className="text-gray-500">Adresse: </span>
                        <span>{selectedMarker.details.adresse}</span>
                      </div>
                      <div className="text-xs">
                        <span className="text-gray-500">Code: </span>
                        <span className="font-mono">{selectedMarker.details.codeLogement}</span>
                      </div>
                      <div className="text-xs">
                        <span className="text-gray-500">Tarif: </span>
                        <span>{selectedMarker.details.tarif} MAD/nuit</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
