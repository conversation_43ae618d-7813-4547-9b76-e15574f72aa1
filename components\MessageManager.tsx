import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

const messages = [
  {
    id: 1,
    platform: 'Airbnb',
    sender: '<PERSON>',
    initials: 'JD',
    content: 'Bonjour, est-ce que la propriété est disponible...',
    date: '2023-07-01',
    status: 'Airbnb',
    bgColor: 'bg-green-100',
    textColor: 'text-green-700'
  },
  {
    id: 2,
    platform: 'Booking',
    sender: '<PERSON>',
    initials: 'J<PERSON>',
    content: 'J\'ai une question concernant le check-in...',
    date: '2023-06-30',
    status: 'Booking',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-700'
  },
  {
    id: 3,
    platform: 'VRBO',
    sender: '<PERSON>',
    initials: '<PERSON><PERSON>',
    content: '<PERSON>uvez-vous me donner plus d\'informations sur...',
    date: '2023-06-29',
    status: 'VRBO',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-700'
  },
]

export function MessageManager() {
  return (
    <div className="space-y-3">
      {messages.map((message) => (
        <div key={message.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold ${message.bgColor} ${message.textColor}`}>
            {message.initials}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <p className="text-sm font-medium text-gray-900 truncate">{message.sender}</p>
              <Badge
                variant="secondary"
                className={`text-xs ${message.bgColor} ${message.textColor} border-0`}
              >
                {message.status}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 truncate">{message.content}</p>
            <p className="text-xs text-gray-500 mt-1">{message.date}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

