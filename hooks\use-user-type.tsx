'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useForm } from '@/contexts/FormContext'

// Routes that should use guest user type
const GUEST_ROUTES = [
  '/',
  '/self-check-in',
  '/verification-self-check-in',
  '/summary-self-check-in',
  '/login',
  '/signup'
]

// Routes that should use host user type
const HOST_ROUTES = [
  '/dashboard',
  '/gestion-logements',
  '/ajouter-logement',
  '/modifier-logement',
  '/locataires',
  '/ajouter-locataire',
  '/renseignement-nuites',
  '/recherche-nuites',
  '/import-nuites',
  '/statistiques-generales',
  '/rapports',
  '/facturation',
  '/communication',
  '/calendrier',
  '/parametres',
  '/aide',
  '/archives',
  '/donnees',
  '/notifications',
  '/societe',
  '/summary',
  '/declaration',
  '/verification',
  '/contrat-genere',
  '/creer-contrat',
  '/generer-contrat'
]

export function useUserType() {
  const pathname = usePathname()
  const { userType, setUserType } = useForm()

  useEffect(() => {
    // Don't override if user type is already set and matches the route type
    const isGuestRoute = GUEST_ROUTES.includes(pathname)
    const isHostRoute = HOST_ROUTES.includes(pathname)

    if (isGuestRoute && userType !== 'guest') {
      setUserType('guest')
    } else if (isHostRoute && userType !== 'host') {
      setUserType('host')
    }
  }, [pathname, userType, setUserType])

  return { userType, setUserType }
}
