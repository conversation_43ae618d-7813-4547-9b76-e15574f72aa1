'use client'

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, Building2, Users, TrendingUp, 
  Calendar, Clock, CheckCircle, AlertTriangle 
} from 'lucide-react'
import { DashboardStats } from '@/types/admin'

interface AdminStatsCardsProps {
  stats: DashboardStats
}

export function AdminStatsCards({ stats }: AdminStatsCardsProps) {
  const statsCards = [
    {
      title: "Déclarations Aujourd'hui",
      value: stats.totalDeclarations.today,
      change: "+12%",
      changeType: "positive" as const,
      icon: Calendar,
      color: "bg-blue-500",
      description: "Nouvelles déclarations"
    },
    {
      title: "Déclarations Cette Semaine",
      value: stats.totalDeclarations.week,
      change: "+8%",
      changeType: "positive" as const,
      icon: Clock,
      color: "bg-green-500",
      description: "Total hebdomadaire"
    },
    {
      title: "Déclarations Ce Mois",
      value: stats.totalDeclarations.month,
      change: "+15%",
      changeType: "positive" as const,
      icon: FileText,
      color: "bg-purple-500",
      description: "Total mensuel"
    },
    {
      title: "Total Logements",
      value: stats.totalAccommodations,
      change: "+3",
      changeType: "positive" as const,
      icon: Building2,
      color: "bg-amber-500",
      description: "Logements enregistrés"
    },
    {
      title: "Hôtes Actifs",
      value: stats.totalHosts,
      change: "+2",
      changeType: "positive" as const,
      icon: Users,
      color: "bg-indigo-500",
      description: "Hôtes avec logements"
    },
    {
      title: "Taux d'Occupation",
      value: `${stats.occupancyRate}%`,
      change: "+5%",
      changeType: "positive" as const,
      icon: TrendingUp,
      color: "bg-emerald-500",
      description: "Logements occupés"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {statsCards.map((stat, index) => (
        <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center shadow-lg`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <Badge 
                variant="outline" 
                className={`${
                  stat.changeType === 'positive' 
                    ? 'bg-green-50 text-green-700 border-green-200' 
                    : 'bg-red-50 text-red-700 border-red-200'
                }`}
              >
                {stat.change}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-900">
                {typeof stat.value === 'number' ? stat.value.toLocaleString('fr-FR') : stat.value}
              </p>
              <p className="text-sm font-medium text-gray-900">{stat.title}</p>
              <p className="text-xs text-gray-500">{stat.description}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
