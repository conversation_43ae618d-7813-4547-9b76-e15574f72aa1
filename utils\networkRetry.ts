/**
 * Network Retry Utilities for handling submission failures
 * Implements exponential backoff and intelligent retry logic
 */

export interface RetryOptions {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  exponentialBase: number
  jitter: boolean
  retryCondition?: (error: any) => boolean
}

export interface RetryResult<T> {
  success: boolean
  data?: T
  error?: any
  attempts: number
  totalTime: number
}

export class NetworkRetryManager {
  private defaultOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    exponentialBase: 2,
    jitter: true,
    retryCondition: (error) => this.isRetryableError(error)
  }

  /**
   * Execute a function with retry logic
   */
  async executeWithRetry<T>(
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<RetryResult<T>> {
    const opts = { ...this.defaultOptions, ...options }
    const startTime = Date.now()
    let lastError: any
    
    for (let attempt = 0; attempt <= opts.maxRetries; attempt++) {
      try {
        const result = await fn()
        return {
          success: true,
          data: result,
          attempts: attempt + 1,
          totalTime: Date.now() - startTime
        }
      } catch (error) {
        lastError = error
        
        // Don't retry if this is the last attempt or error is not retryable
        if (attempt === opts.maxRetries || !opts.retryCondition!(error)) {
          break
        }
        
        // Calculate delay with exponential backoff
        const delay = this.calculateDelay(attempt, opts)
        await this.sleep(delay)
      }
    }
    
    return {
      success: false,
      error: lastError,
      attempts: opts.maxRetries + 1,
      totalTime: Date.now() - startTime
    }
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number, options: RetryOptions): number {
    const exponentialDelay = options.baseDelay * Math.pow(options.exponentialBase, attempt)
    const delay = Math.min(exponentialDelay, options.maxDelay)
    
    if (options.jitter) {
      // Add random jitter (±25%)
      const jitterRange = delay * 0.25
      const jitter = (Math.random() - 0.5) * 2 * jitterRange
      return Math.max(0, delay + jitter)
    }
    
    return delay
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors
    if (error.name === 'NetworkError' || error.message?.includes('network')) {
      return true
    }
    
    // Timeout errors
    if (error.name === 'TimeoutError' || error.message?.includes('timeout')) {
      return true
    }
    
    // HTTP status codes that are retryable
    if (error.status) {
      const retryableStatuses = [408, 429, 500, 502, 503, 504]
      return retryableStatuses.includes(error.status)
    }
    
    // Fetch API errors
    if (error instanceof TypeError && error.message?.includes('fetch')) {
      return true
    }
    
    return false
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Specialized retry manager for PDF generation
 */
export class PDFGenerationRetryManager extends NetworkRetryManager {
  async generatePDFWithRetry(
    data: any,
    endpoint: string = '/api/generate-pdf'
  ): Promise<RetryResult<Blob>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`)
        ;(error as any).status = response.status
        throw error
      }

      return await response.blob()
    }, {
      maxRetries: 5, // More retries for PDF generation
      baseDelay: 2000, // Longer base delay
      retryCondition: (error) => {
        // Don't retry on client errors (4xx) except 408 and 429
        if (error.status >= 400 && error.status < 500) {
          return error.status === 408 || error.status === 429
        }
        return this.isRetryableError(error)
      }
    })
  }
}

/**
 * Specialized retry manager for data submission
 */
export class DataSubmissionRetryManager extends NetworkRetryManager {
  async submitDataWithRetry(
    data: any,
    endpoint: string
  ): Promise<RetryResult<any>> {
    return this.executeWithRetry(async () => {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`)
        ;(error as any).status = response.status
        throw error
      }

      return await response.json()
    }, {
      maxRetries: 3,
      baseDelay: 1500,
      retryCondition: (error) => {
        // Be more conservative with data submission retries
        if (error.status >= 400 && error.status < 500) {
          // Only retry on specific client errors
          return [408, 429].includes(error.status)
        }
        return this.isRetryableError(error)
      }
    })
  }
}

/**
 * Queue manager for handling multiple retry operations
 */
export class RetryQueue {
  private queue: Array<{
    id: string
    fn: () => Promise<any>
    options: Partial<RetryOptions>
    resolve: (value: any) => void
    reject: (error: any) => void
  }> = []
  
  private processing = false
  private retryManager = new NetworkRetryManager()

  /**
   * Add operation to retry queue
   */
  async add<T>(
    id: string,
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({ id, fn, options, resolve, reject })
      this.processQueue()
    })
  }

  /**
   * Process the retry queue
   */
  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return
    }

    this.processing = true

    while (this.queue.length > 0) {
      const operation = this.queue.shift()!
      
      try {
        const result = await this.retryManager.executeWithRetry(
          operation.fn,
          operation.options
        )
        
        if (result.success) {
          operation.resolve(result.data)
        } else {
          operation.reject(result.error)
        }
      } catch (error) {
        operation.reject(error)
      }
    }

    this.processing = false
  }

  /**
   * Get queue status
   */
  getStatus(): { pending: number, processing: boolean } {
    return {
      pending: this.queue.length,
      processing: this.processing
    }
  }

  /**
   * Clear the queue
   */
  clear(): void {
    this.queue.forEach(operation => {
      operation.reject(new Error('Queue cleared'))
    })
    this.queue = []
  }
}

// Global instances
export const globalRetryManager = new NetworkRetryManager()
export const pdfRetryManager = new PDFGenerationRetryManager()
export const dataRetryManager = new DataSubmissionRetryManager()
export const globalRetryQueue = new RetryQueue()

/**
 * Utility functions for common retry scenarios
 */
export async function retryFetch(
  url: string,
  options: RequestInit = {},
  retryOptions: Partial<RetryOptions> = {}
): Promise<Response> {
  const result = await globalRetryManager.executeWithRetry(
    () => fetch(url, options),
    retryOptions
  )
  
  if (result.success) {
    return result.data!
  } else {
    throw result.error
  }
}

export async function retryPDFGeneration(
  data: any,
  endpoint: string = '/api/generate-pdf'
): Promise<Blob> {
  const result = await pdfRetryManager.generatePDFWithRetry(data, endpoint)
  
  if (result.success) {
    return result.data!
  } else {
    throw result.error
  }
}

export async function retryDataSubmission(
  data: any,
  endpoint: string
): Promise<any> {
  const result = await dataRetryManager.submitDataWithRetry(data, endpoint)
  
  if (result.success) {
    return result.data!
  } else {
    throw result.error
  }
}
