import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Euro, Percent, Calendar, TrendingUp } from 'lucide-react'

export function SummaryStats() {
  // These values should be calculated or fetched from your data source
  const annualRevenue = 1250000 * 11  // Convert to MAD
  const occupancyRate = 75
  const averageDailyRate = 250 * 11    // Convert to MAD
  const revPAR = 187.5 * 11           // Convert to MAD

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Revenu Annuel
          </CardTitle>
          <Euro className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{annualRevenue.toLocaleString()} MAD</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Taux d'Occupation
          </CardTitle>
          <Percent className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{occupancyRate}%</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Tarif Journalier Moyen
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{averageDailyRate} MAD</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            RevPAR
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{revPAR} MAD</div>
        </CardContent>
      </Card>
    </div>
  )
}

