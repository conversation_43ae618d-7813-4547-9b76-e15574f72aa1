@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Sidebar Styles - Merged from sidebar.css */

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: tabular-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Moroccan Institutional Colors */
  .text-institutional-primary {
    color: hsl(var(--institutional-primary));
  }

  .text-institutional-secondary {
    color: hsl(var(--institutional-secondary));
  }

  .text-institutional-accent {
    color: hsl(var(--institutional-accent));
  }

  .text-institutional-gold {
    color: hsl(var(--institutional-gold));
  }

  .text-institutional-royal {
    color: hsl(var(--institutional-royal));
  }

  .bg-institutional-primary {
    background-color: hsl(var(--institutional-primary));
  }

  .bg-institutional-accent {
    background-color: hsl(var(--institutional-accent));
  }

  .bg-institutional-gold {
    background-color: hsl(var(--institutional-gold));
  }

  .border-institutional-primary {
    border-color: hsl(var(--institutional-primary));
  }

  /* Professional shadows */
  .shadow-professional {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-professional-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Clean focus styles */
  .focus-ring-institutional {
    @apply focus:ring-2 focus:ring-institutional-primary focus:ring-opacity-50 focus:border-institutional-primary;
  }

  /* Enhanced Typography */
  .text-sidebar-primary {
    color: hsl(var(--sidebar-primary));
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .text-sidebar-secondary {
    color: hsl(var(--sidebar-foreground) / 0.7);
    font-weight: 500;
    letter-spacing: 0.015em;
  }

  .text-sidebar-muted {
    color: hsl(var(--sidebar-foreground) / 0.6);
    font-weight: 500;
    letter-spacing: 0.02em;
  }

  /* Enhanced Sidebar Styles */
  .sidebar-section-header {
    @apply text-xs font-bold text-sidebar-foreground/60 uppercase tracking-widest;
  }

  .sidebar-menu-item {
    @apply font-semibold text-sm tracking-wide;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-menu-item:hover {
    transform: translateX(2px);
  }

  .sidebar-menu-item.active {
    @apply text-institutional-primary font-bold;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1), 0 2px 4px rgba(59, 130, 246, 0.1);
  }

  .sidebar-badge {
    @apply text-xs font-bold px-2 py-1 rounded-md;
  }

  /* Additional Sidebar Enhancements */
  .badge-pulse {
    animation: pulse 2s infinite;
  }

  .collapsible-content {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-active-gradient {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  }

  .sidebar-menu-button {
    position: relative;
    overflow: hidden;
  }

  .sidebar-menu-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .sidebar-menu-button:hover::before {
    left: 100%;
  }

  .quick-action-button {
    position: relative;
    overflow: hidden;
  }

  .quick-action-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  .quick-action-button:hover::after {
    width: 300px;
    height: 300px;
  }

  .sidebar-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  .user-profile {
    transition: all 0.2s ease-in-out;
  }

  .user-profile:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .stats-section {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  }

  .sidebar-icon {
    transition: all 0.2s ease-in-out;
  }

  .sidebar-menu-button:hover .sidebar-icon {
    transform: scale(1.1);
  }

  .section-label {
    transition: all 0.2s ease-in-out;
  }

  .section-label:hover {
    color: #374151;
    transform: translateX(2px);
  }

  .badge-new {
    animation: bounce 1s infinite;
  }

  .sidebar-menu-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .sidebar-loading {
    animation: shimmer 1.5s infinite;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
  }

  /* Enhanced animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUpFromBottom {
    from {
      opacity: 0;
      transform: translateY(100px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes floatUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -8px, 0);
    }
    70% {
      transform: translate3d(0, -4px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-up-from-bottom {
    animation: slideUpFromBottom 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-float-up {
    animation: floatUp 0.6s ease-out;
  }

  .animate-float-up-delay-1 {
    animation: floatUp 0.6s ease-out 0.1s both;
  }

  .animate-float-up-delay-2 {
    animation: floatUp 0.6s ease-out 0.2s both;
  }

  .animate-float-up-delay-3 {
    animation: floatUp 0.6s ease-out 0.3s both;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--institutional-primary));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--institutional-primary) / 0.8);
  }

  /* Enhanced Mobile-First Responsive Utilities */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }

  .mobile-card {
    @apply rounded-2xl sm:rounded-3xl shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .mobile-button {
    @apply h-12 sm:h-14 text-base font-medium rounded-xl transition-all duration-200;
  }

  .mobile-input {
    @apply h-12 sm:h-14 text-base rounded-xl border-gray-200 focus:border-institutional-primary focus:ring-institutional-primary/20;
  }

  .mobile-text-lg {
    @apply text-lg sm:text-xl lg:text-2xl font-semibold;
  }

  .mobile-text-xl {
    @apply text-xl sm:text-2xl lg:text-3xl font-bold;
  }

  .mobile-grid-cols-1 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .mobile-grid-cols-2 {
    @apply grid-cols-2 sm:grid-cols-3 lg:grid-cols-4;
  }

  .mobile-gap {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  .mobile-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .mobile-margin {
    @apply m-4 sm:m-6 lg:m-8;
  }

  /* Enhanced Card Styles */
  .enhanced-card {
    @apply bg-white/95 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] mobile-card;
  }

  .stats-card {
    @apply enhanced-card p-6 text-center space-y-3;
  }

  .property-card {
    @apply enhanced-card overflow-hidden;
  }

  .form-card {
    @apply enhanced-card p-6 space-y-6;
  }

  /* Enhanced Touch-friendly interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  .touch-button {
    @apply h-12 px-6 text-base font-medium rounded-xl touch-manipulation transition-all duration-200;
  }

  .touch-icon {
    @apply w-6 h-6 sm:w-5 sm:h-5;
  }

  /* Enhanced Button Styles */
  .btn-primary {
    @apply bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300;
  }

  .btn-secondary {
    @apply border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200;
  }

  .btn-outline {
    @apply border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200;
  }

  /* Enhanced Form Styles */
  .form-section {
    @apply space-y-4 p-6 bg-gray-50/50 mobile-card border border-gray-100;
  }

  .form-section-header {
    @apply flex items-center gap-3 mb-4 pb-3 border-b border-gray-200;
  }

  .form-section-title {
    @apply text-lg font-semibold text-gray-900;
  }

  .form-section-icon {
    @apply w-5 h-5 text-institutional-primary;
  }

  /* Mobile-optimized scrollbar */
  .mobile-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .mobile-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .mobile-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--institutional-primary) / 0.3);
    border-radius: 2px;
  }

  .mobile-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--institutional-primary) / 0.5);
  }

  /* Safe area support for devices with notches */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  .pb-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  /* Enhanced Progress Indicators */
  .progress-step {
    @apply relative flex flex-col items-center gap-2 min-w-0 flex-shrink-0;
  }

  .progress-step-icon {
    @apply w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center transition-all duration-300 border-2;
  }

  .progress-step-active {
    @apply bg-institutional-primary text-white border-institutional-primary shadow-lg scale-110;
  }

  .progress-step-completed {
    @apply bg-green-500 text-white border-green-500;
  }

  .progress-step-inactive {
    @apply bg-gray-100 text-gray-400 border-gray-200;
  }

  .progress-step-title {
    @apply text-sm font-medium text-center leading-tight;
  }

  .progress-step-title-active {
    @apply text-institutional-primary;
  }

  .progress-step-title-inactive {
    @apply text-gray-500;
  }

  /* Enhanced Status Badges */
  .status-badge-active {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .status-badge-inactive {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .status-badge-pending {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
  }

  /* Enhanced Animations */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  /* Logo Background Utilities */
  .logo-background {
    position: relative;
  }

  .logo-background::before {
    content: '';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    height: 90vh;
    background-image: url('/IMG_9135-Photoroom.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.05;
    z-index: -1;
    pointer-events: none;
  }

  .logo-background-large {
    position: relative;
  }

  .logo-background-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/IMG_9135-Photoroom.png');
    background-size: 600px 600px;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.06;
    z-index: 0;
    pointer-events: none;
  }

  .logo-background-small {
    position: relative;
  }

  .logo-background-small::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/IMG_9135-Photoroom.png');
    background-size: 200px 200px;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.10;
    z-index: 0;
    pointer-events: none;
  }

  .logo-background-corner {
    position: relative;
  }

  .logo-background-corner::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 150px;
    height: 150px;
    background-image: url('/IMG_9135-Photoroom.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.12;
    z-index: 0;
    pointer-events: none;
  }

  .logo-background-content {
    position: relative;
    z-index: 1;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .mobile-optimized {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }

    .mobile-scroll {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
    }

    .mobile-grid-responsive {
      @apply grid-cols-1;
    }

    .mobile-text-responsive {
      @apply text-sm;
    }

    .mobile-padding-responsive {
      @apply p-4;
    }

    /* Disable hover effects on mobile */
    .hover-lift:hover {
      transform: none;
      box-shadow: none;
    }

    .hover-scale:hover {
      transform: none;
    }

    /* Mobile logo background adjustments */
    .logo-background::before {
      background-size: 250px 250px;
      opacity: 0.06;
    }

    .logo-background-large::before {
      background-size: 350px 350px;
      opacity: 0.04;
    }

    .logo-background-small::before {
      background-size: 150px 150px;
      opacity: 0.08;
    }

    .logo-background-corner::before {
      width: 100px;
      height: 100px;
      top: 10px;
      right: 10px;
      opacity: 0.10;
    }

    /* Mobile sidebar adjustments */
    .sidebar-menu-item:hover {
      transform: none;
    }

    .sidebar-menu-button::before {
      display: none;
    }
  }

  /* Dark mode sidebar support */
  @media (prefers-color-scheme: dark) {
    .sidebar-header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .stats-section {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .user-profile:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;
    /* Clean Moroccan Government Colors */
    --primary: 142 69% 58%; /* Professional Green */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 142 69% 58%;
    /* Professional Chart Colors */
    --chart-1: 142 69% 58%; /* Professional Green */
    --chart-2: 0 72% 51%; /* Moroccan Red */
    --chart-3: 43 96% 56%; /* Gold */
    --chart-4: 220 100% 25%; /* Royal Blue */
    --chart-5: 27 87% 67%; /* Warm accent */
    --radius: 0.75rem;
    --sidebar-background: 255 255 255; /* pure white */
    --sidebar-foreground: 30 41 59; /* slate-800 */
    --sidebar-primary: 142 69% 58%; /* institutional-primary */
    --sidebar-primary-foreground: 255 255 255; /* white */
    --sidebar-accent: 248 250 252; /* slate-50 */
    --sidebar-accent-foreground: 15 23 42; /* slate-900 */
    --sidebar-border: 226 232 240; /* slate-200 */
    --sidebar-ring: 142 69% 58%; /* institutional-primary */

    /* Clean Moroccan Institutional Colors */
    --institutional-primary: 142 69% 58%; /* Professional Green */
    --institutional-secondary: 215 25% 45%; /* Muted Blue-Gray */
    --institutional-accent: 0 72% 51%; /* Moroccan Red */
    --institutional-gold: 43 96% 56%; /* Gold */
    --institutional-royal: 220 100% 25%; /* Royal Blue */
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    /* Keep sidebar light even in dark mode for institutional consistency */
    --sidebar-background: 255 255 255; /* pure white */
    --sidebar-foreground: 30 41 59; /* slate-800 */
    --sidebar-primary: 142 69% 58%; /* institutional-primary */
    --sidebar-primary-foreground: 255 255 255; /* white */
    --sidebar-accent: 248 250 252; /* slate-50 */
    --sidebar-accent-foreground: 15 23 42; /* slate-900 */
    --sidebar-border: 226 232 240; /* slate-200 */
    --sidebar-ring: 142 69% 58%; /* institutional-primary */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
