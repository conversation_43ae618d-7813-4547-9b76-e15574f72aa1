'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { useRouter } from 'next/navigation'
import { Camera, AlertCircle, CheckCircle, Upload } from 'lucide-react'

interface SimpleQRScannerProps {
  onClose: () => void
}

export function SimpleQRScanner({ onClose }: SimpleQRScannerProps) {
  const [error, setError] = useState<string | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [cameraReady, setCameraReady] = useState(false)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const router = useRouter()

  useEffect(() => {
    return () => {
      // Cleanup camera stream on unmount
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  const startCamera = async () => {
    try {
      setError(null)
      setIsScanning(true)
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        streamRef.current = stream
        setCameraReady(true)
      }
    } catch (err: any) {
      console.error('Camera error:', err)
      if (err.name === 'NotAllowedError') {
        setError('Accès à la caméra refusé. Veuillez autoriser l\'accès à la caméra.')
      } else if (err.name === 'NotFoundError') {
        setError('Aucune caméra trouvée sur cet appareil.')
      } else {
        setError('Erreur lors de l\'accès à la caméra.')
      }
      setIsScanning(false)
    }
  }

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setCameraReady(false)
    setIsScanning(false)
  }

  const simulateQRScan = () => {
    setScanResult('SIMULATED_QR_CODE_12345')
    setIsScanning(false)
    setTimeout(() => {
      router.push('/self-check-in')
      onClose()
    }, 1500)
  }

  const handleManualEntry = () => {
    router.push('/self-check-in')
    onClose()
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Simulate QR code detection from uploaded image
      setScanResult('UPLOADED_QR_CODE_67890')
      setIsScanning(false)
      setTimeout(() => {
        router.push('/self-check-in')
        onClose()
      }, 1500)
    }
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg rounded-3xl border-0 shadow-2xl logo-background-content enhanced-card">
        <DialogHeader className="text-center pb-6">
          <DialogTitle className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-2">
            <Camera className="w-6 h-6 text-institutional-primary" />
            Scanner QR Code
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {scanResult ? 
              'QR code détecté avec succès! Redirection en cours...' :
              error ? 
                'Scanner indisponible. Utilisez les options alternatives ci-dessous.' :
                'Démarrez la caméra pour scanner un QR code'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="relative">
          {/* Camera/Scanner Container */}
          <div className="relative bg-gray-900 rounded-2xl overflow-hidden aspect-square">
            {cameraReady && !error && !scanResult ? (
              <>
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-cover rounded-2xl"
                />
                <canvas ref={canvasRef} className="hidden" />
                
                {/* Scanning Overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-48 border-2 border-institutional-primary rounded-2xl relative">
                    <div className="absolute -top-1 -left-1 w-6 h-6 border-l-4 border-t-4 border-institutional-primary rounded-tl-lg"></div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 border-r-4 border-t-4 border-institutional-primary rounded-tr-lg"></div>
                    <div className="absolute -bottom-1 -left-1 w-6 h-6 border-l-4 border-b-4 border-institutional-primary rounded-bl-lg"></div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 border-r-4 border-b-4 border-institutional-primary rounded-br-lg"></div>
                    <div className="absolute top-0 left-0 w-full h-1 bg-institutional-primary animate-pulse"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <p className="text-institutional-primary text-xs font-medium bg-white/90 px-2 py-1 rounded">
                        Positionnez le QR code ici
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : scanResult ? (
              <div className="absolute inset-0 bg-green-500/90 flex items-center justify-center rounded-2xl">
                <div className="text-center">
                  <CheckCircle className="w-16 h-16 text-white mx-auto mb-4" />
                  <p className="text-white text-lg font-semibold">QR Code détecté!</p>
                  <p className="text-white/80 text-sm">Redirection en cours...</p>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                {error ? (
                  <div className="text-center p-6">
                    <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                    <p className="text-white text-sm mb-2">Scanner indisponible</p>
                    <p className="text-white/70 text-xs">Utilisez les options ci-dessous</p>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-white text-sm">Cliquez pour démarrer</p>
                  </div>
                )}
              </div>
            )}

            {/* Status indicator */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex items-center gap-2 bg-black/70 backdrop-blur-sm rounded-full px-4 py-2">
                <div className={`w-2 h-2 rounded-full ${
                  scanResult ? 'bg-green-400' :
                  cameraReady ? 'bg-green-400 animate-pulse' : 
                  'bg-gray-400'
                }`}></div>
                <span className="text-white text-sm font-medium">
                  {scanResult ? 'Détecté!' :
                   cameraReady ? 'Prêt à scanner' : 
                   error ? 'Indisponible' :
                   'Arrêté'}
                </span>
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-800 text-sm font-medium mb-1">Scanner indisponible</p>
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 mt-6">
          {/* Primary Actions */}
          <div className="flex gap-3">
            {!cameraReady && !scanResult ? (
              <Button
                onClick={startCamera}
                disabled={isScanning}
                className="flex-1 h-12 bg-institutional-primary hover:bg-green-600 text-white rounded-xl font-semibold"
              >
                <Camera className="w-4 h-4 mr-2" />
                Démarrer la caméra
              </Button>
            ) : cameraReady && !scanResult ? (
              <Button
                onClick={stopCamera}
                className="flex-1 h-12 bg-red-600 hover:bg-red-700 text-white rounded-xl font-semibold"
              >
                Arrêter la caméra
              </Button>
            ) : scanResult ? (
              <Button
                onClick={handleManualEntry}
                className="flex-1 h-12 bg-green-600 hover:bg-green-700 text-white rounded-xl font-semibold"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Continuer
              </Button>
            ) : null}

            {/* Development Test Button */}
            {process.env.NODE_ENV === 'development' && !scanResult && (
              <Button
                onClick={simulateQRScan}
                variant="outline"
                className="h-12 px-4 border-2 border-blue-300 hover:bg-blue-50 rounded-xl font-semibold text-blue-600"
              >
                🧪 Test
              </Button>
            )}
          </div>

          {/* Alternative Options */}
          <div className="flex gap-3">
            <Button
              onClick={handleManualEntry}
              variant="outline"
              className="flex-1 h-12 border-2 border-gray-200 hover:bg-gray-50 rounded-xl font-semibold"
            >
              Saisie manuelle
            </Button>

            <label className="flex-1">
              <Button
                variant="outline"
                className="w-full h-12 border-2 border-gray-200 hover:bg-gray-50 rounded-xl font-semibold cursor-pointer"
                asChild
              >
                <span>
                  <Upload className="w-4 h-4 mr-2" />
                  Importer image
                </span>
              </Button>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>

            <Button
              onClick={onClose}
              variant="outline"
              className="h-12 px-6 border-2 border-gray-200 hover:bg-gray-50 rounded-xl font-semibold"
            >
              Annuler
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
