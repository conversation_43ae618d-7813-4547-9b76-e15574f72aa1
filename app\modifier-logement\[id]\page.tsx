import { ModifierLogementForm } from '@/components/ModifierLogementForm'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { Button } from "@/components/ui/button"
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function ModifierLogementPage({ params }: { params: { id: string } }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <div className="container mx-auto py-8 logo-background-content">
        <div className="flex items-center mb-6">
          <Link href="/gestion-logements" className="mr-4">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Modifier un Logement</h1>
        </div>
        <ModifierLogementForm id={params.id} />
      </div>
    </div>
  )
}

