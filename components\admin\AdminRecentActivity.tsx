'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Clock, FileText, CheckCircle, AlertTriangle, 
  User, MapPin, Calendar, Eye, ArrowRight 
} from 'lucide-react'
import { AdminDeclaration } from '@/types/admin'

interface AdminRecentActivityProps {
  declarations: AdminDeclaration[]
  onViewDetails?: (declaration: AdminDeclaration) => void
  onViewAllDeclarations?: () => void
}

export function AdminRecentActivity({ declarations, onViewDetails, onViewAllDeclarations }: AdminRecentActivityProps) {
  const recentDeclarations = declarations
    .sort((a, b) => new Date(b.dateDeclaration).getTime() - new Date(a.dateDeclaration).getTime())
    .slice(0, 10)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'validée': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'en_cours': return <Clock className="w-4 h-4 text-yellow-500" />
      case 'expirée': return <AlertTriangle className="w-4 h-4 text-red-500" />
      default: return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'validée': return 'bg-green-50 text-green-700 border-green-200'
      case 'en_cours': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'expirée': return 'bg-red-50 text-red-700 border-red-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Il y a moins d\'1h'
    if (diffInHours < 24) return `Il y a ${diffInHours}h`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `Il y a ${diffInDays}j`
    
    return date.toLocaleDateString('fr-FR', { 
      day: 'numeric', 
      month: 'short' 
    })
  }

  return (
    <Card className="bg-white border-0 shadow-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-institutional-primary" />
            Activité Récente
          </CardTitle>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Temps réel
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {recentDeclarations.map((declaration) => (
            <div
              key={declaration.id}
              className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {/* Status Icon */}
              <div className="flex-shrink-0 mt-1">
                {getStatusIcon(declaration.statut)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <p className="font-medium text-gray-900 truncate">
                    {declaration.prenom} {declaration.nom}
                  </p>
                  <Badge variant="outline" className={`text-xs ${getStatusColor(declaration.statut)}`}>
                    {declaration.statut}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <User className="w-3 h-3" />
                    <span className="truncate">ID: {declaration.guestId}</span>
                  </div>
                  
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{declaration.nomLogement}, {declaration.ville}</span>
                  </div>
                  
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Calendar className="w-3 h-3" />
                    <span>{declaration.dateArrivee} - {declaration.dateDepart}</span>
                  </div>
                </div>
              </div>

              {/* Time and Action */}
              <div className="flex flex-col items-end gap-2">
                <span className="text-xs text-gray-500">
                  {formatTimeAgo(declaration.dateDeclaration)}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => onViewDetails?.(declaration)}
                >
                  <Eye className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {recentDeclarations.length === 0 && (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Aucune activité récente</p>
          </div>
        )}

        {/* View All Button */}
        {recentDeclarations.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              className="w-full"
              size="sm"
              onClick={() => onViewAllDeclarations?.()}
            >
              Voir toutes les déclarations
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="bg-green-50 rounded-lg p-2">
              <p className="text-lg font-bold text-green-600">
                {recentDeclarations.filter(d => d.statut === 'validée').length}
              </p>
              <p className="text-xs text-green-600">Validées</p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-2">
              <p className="text-lg font-bold text-yellow-600">
                {recentDeclarations.filter(d => d.statut === 'en_cours').length}
              </p>
              <p className="text-xs text-yellow-600">En cours</p>
            </div>
            <div className="bg-red-50 rounded-lg p-2">
              <p className="text-lg font-bold text-red-600">
                {recentDeclarations.filter(d => d.statut === 'expirée').length}
              </p>
              <p className="text-xs text-red-600">Expirées</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
