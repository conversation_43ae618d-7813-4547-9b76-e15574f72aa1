'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm as useReactForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useForm } from '../../contexts/FormContext'
import type { PersonalInfo } from '../../types/form'

const personalInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  surname: z.string().min(1, 'Surname is required'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  placeOfBirth: z.string().min(1, 'Place of birth is required'),
  fatherName: z.string().min(1, 'Father\'s name is required'),
  motherName: z.string().min(1, 'Mother\'s name is required'),
  occupation: z.string().min(1, 'Occupation is required'),
  permanentAddress: z.string().min(1, 'Address is required'),
  childrenUnder15: z.number().min(0),
})

export function PersonalInfoStep() {
  const { updateFormData, nextStep } = useForm()
  const form = useReactForm<PersonalInfo>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      childrenUnder15: 0,
    },
  })

  const onSubmit = (data: PersonalInfo) => {
    updateFormData(data)
    nextStep()
  }

  return (
    <div className="p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Prénom *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez votre prénom"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="surname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Nom de famille *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez votre nom de famille"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="dateOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Date de naissance *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="date"
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="placeOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Lieu de naissance *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez votre lieu de naissance"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="fatherName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Nom du père *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez le nom du père"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="motherName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">Nom de la mère *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                      placeholder="Entrez le nom de la mère"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="occupation"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Profession *</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    placeholder="Entrez votre profession"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="permanentAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Adresse permanente *</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    placeholder="Entrez votre adresse permanente"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="childrenUnder15"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Nombre d'enfants de moins de 15 ans</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    className="h-12 border border-gray-300 focus:border-institutional-primary focus:ring-1 focus:ring-institutional-primary rounded-xl transition-colors"
                    placeholder="0"
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end pt-6 border-t border-gray-100">
            <Button
              type="submit"
              className="h-12 px-8 bg-institutional-primary hover:bg-green-600 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 font-semibold"
            >
              Continuer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

