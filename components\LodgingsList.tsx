'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import Image from 'next/image'
import { Edit, Trash2, Eye, Search, Plus, MapPin, Home, Star, MoreVertical, Filter, Calendar } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"

// Enhanced mock data for lodgings
const initialLodgings = [
  {
    id: 1,
    name: "Villa Sunset",
    address: "123 Beach Road",
    city: "Nice",
    type: "Villa",
    status: "active",
    rating: 4.8,
    bookings: 12,
    image: "/placeholder.svg?height=200&width=300&text=Villa+Sunset"
  },
  {
    id: 2,
    name: "Mountain Chalet",
    address: "45 Alpine Way",
    city: "Chamonix",
    type: "Chalet",
    status: "active",
    rating: 4.6,
    bookings: 8,
    image: "/placeholder.svg?height=200&width=300&text=Mountain+Chalet"
  },
  {
    id: 3,
    name: "City Apartment",
    address: "78 Urban Street",
    city: "Paris",
    type: "Appartement",
    status: "maintenance",
    rating: 4.3,
    bookings: 15,
    image: "/placeholder.svg?height=200&width=300&text=City+Apartment"
  },
  {
    id: 4,
    name: "Countryside Cottage",
    address: "12 Rural Lane",
    city: "Provence",
    type: "Maison",
    status: "active",
    rating: 4.9,
    bookings: 6,
    image: "/placeholder.svg?height=200&width=300&text=Countryside+Cottage"
  },
  {
    id: 5,
    name: "Beachfront Studio",
    address: "56 Coastal Avenue",
    city: "Cannes",
    type: "Studio",
    status: "inactive",
    rating: 4.2,
    bookings: 3,
    image: "/placeholder.svg?height=200&width=300&text=Beachfront+Studio"
  },
]

export function LodgingsList() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState('')
  const [lodgings, setLodgings] = useState(initialLodgings)
  const [lodgingToDelete, setLodgingToDelete] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const router = useRouter()
  const isMobile = useIsMobile()

  const filteredLodgings = lodgings.filter(lodging =>
    lodging.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lodging.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lodging.type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'maintenance': return 'bg-yellow-100 text-yellow-800'
      case 'inactive': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Actif'
      case 'maintenance': return 'Maintenance'
      case 'inactive': return 'Inactif'
      default: return status
    }
  }

  const handleView = (id: number) => {
    router.push(`/gestion-logements/${id}`)
  }

  const handleModify = (id: number) => {
    router.push(`/modifier-logement/${id}`)
  }

  const handleDelete = (id: number) => {
    setLodgingToDelete(id)
  }

  const confirmDelete = async () => {
    if (lodgingToDelete) {
      setIsLoading(true)
      try {
        // In a real application, you would make an API call here
        await new Promise(resolve => setTimeout(resolve, 1000))
        setLodgings(lodgings.filter(lodging => lodging.id !== lodgingToDelete))
        toast({
          title: "Logement supprimé",
          description: "Le logement a été supprimé avec succès.",
        })
      } catch (error) {
        toast({
          title: "Erreur",
          description: "Une erreur est survenue lors de la suppression.",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }
    setLodgingToDelete(null)
  }

  const cancelDelete = () => {
    setLodgingToDelete(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background-content">
      <div className="mobile-container py-4 sm:py-6 space-y-6">

        {/* Enhanced Mobile-First Header */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 sm:items-center sm:justify-between">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                className="mobile-input mobile-card pl-10 pr-4"
                placeholder={isMobile ? "Rechercher..." : "Rechercher par nom, ville ou type..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isLoading}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 sm:gap-3">
              <Button
                variant="outline"
                size={isMobile ? "default" : "sm"}
                className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
                disabled={isLoading}
              >
                <Filter className="w-4 h-4 mr-2" />
                {isMobile ? 'Filtres' : 'Filtrer'}
              </Button>

              <Link href="/ajouter-logement">
                <Button
                  size={isMobile ? "default" : "sm"}
                  className="mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target"
                  disabled={isLoading}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {isMobile ? 'Ajouter' : 'Ajouter un logement'}
                </Button>
              </Link>
            </div>
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {filteredLodgings.length} logement{filteredLodgings.length !== 1 ? 's' : ''} trouvé{filteredLodgings.length !== 1 ? 's' : ''}
            </span>
            {!isMobile && (
              <div className="flex items-center gap-2">
                <span className="text-xs">Vue:</span>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 px-3"
                >
                  Grille
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 px-3"
                >
                  Liste
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Mobile-First Property Grid */}
        <div className={`grid gap-4 sm:gap-6 ${
          isMobile
            ? 'grid-cols-1'
            : viewMode === 'grid'
              ? 'md:grid-cols-2 lg:grid-cols-3'
              : 'grid-cols-1 max-w-4xl mx-auto'
        }`}>
          {filteredLodgings.map((lodging, index) => (
            <Card
              key={lodging.id}
              className="property-card group animate-slide-up-from-bottom"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-0">
                {/* Enhanced Property Image */}
                <div className="relative overflow-hidden">
                  <Image
                    src={lodging.image}
                    alt={lodging.name}
                    width={isMobile ? 400 : 300}
                    height={isMobile ? 200 : 200}
                    className="w-full h-48 sm:h-52 object-cover transition-transform duration-500 group-hover:scale-110"
                  />

                  {/* Enhanced Status Badge */}
                  <Badge
                    className={`absolute top-3 left-3 border-0 shadow-lg backdrop-blur-sm ${
                      lodging.status === 'active'
                        ? 'status-badge-active'
                        : lodging.status === 'maintenance'
                        ? 'status-badge-pending'
                        : 'status-badge-inactive'
                    }`}
                  >
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        lodging.status === 'active' ? 'bg-green-500' :
                        lodging.status === 'maintenance' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      {getStatusText(lodging.status)}
                    </div>
                  </Badge>

                  {/* Enhanced Rating Badge */}
                  <div className="absolute top-3 right-3 bg-gradient-to-r from-amber-500 to-orange-500 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-bold flex items-center gap-1 shadow-lg">
                    <Star className="w-3 h-3 fill-white text-white" />
                    {lodging.rating}
                  </div>

                  {/* Property Type Badge */}
                  <div className="absolute bottom-3 left-3">
                    <Badge variant="secondary" className="bg-white/90 text-gray-800 border-0 shadow-md">
                      {lodging.type}
                    </Badge>
                  </div>
                </div>

                {/* Enhanced Property Details */}
                <div className="p-4 sm:p-6 space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between gap-3">
                      <h3 className="font-bold text-xl text-gray-900 line-clamp-1 group-hover:text-institutional-primary transition-colors duration-300">
                        {lodging.name}
                      </h3>
                      <div className="flex items-center gap-1 text-amber-500">
                        <Star className="w-4 h-4 fill-current" />
                        <span className="text-sm font-semibold">{lodging.rating}</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-gray-600">
                        <MapPin className="w-4 h-4 text-institutional-primary shrink-0" />
                        <p className="text-sm line-clamp-1 font-medium">{lodging.address}</p>
                      </div>

                      <div className="flex items-center gap-2 text-gray-600">
                        <Home className="w-4 h-4 text-institutional-primary shrink-0" />
                        <p className="text-sm font-medium">{lodging.city}</p>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Stats */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                    <div className="text-center space-y-1">
                      <div className="w-8 h-8 bg-institutional-primary/10 rounded-full flex items-center justify-center mx-auto">
                        <Calendar className="w-4 h-4 text-institutional-primary" />
                      </div>
                      <p className="text-lg font-bold text-institutional-primary">{lodging.bookings}</p>
                      <p className="text-xs text-gray-500 font-medium">Réservations</p>
                    </div>
                    <div className="text-center space-y-1">
                      <div className="w-8 h-8 bg-green-500/10 rounded-full flex items-center justify-center mx-auto">
                        <Star className="w-4 h-4 text-green-600" />
                      </div>
                      <p className="text-lg font-bold text-green-600">{lodging.rating}</p>
                      <p className="text-xs text-gray-500 font-medium">Note moyenne</p>
                    </div>
                    <div className="text-center space-y-1">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto ${
                        lodging.status === 'active' ? 'bg-green-500/10' : 'bg-red-500/10'
                      }`}>
                        <div className={`w-3 h-3 rounded-full ${
                          lodging.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                        }`}></div>
                      </div>
                      <p className={`text-lg font-bold ${
                        lodging.status === 'active' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {lodging.status === 'active' ? '✓' : '○'}
                      </p>
                      <p className="text-xs text-gray-500 font-medium">Statut</p>
                    </div>
                  </div>
                </div>
              </CardContent>

              {/* Enhanced Mobile-First Actions */}
              <CardFooter className="p-4 sm:p-5 pt-0 flex gap-2">
                {isMobile ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleView(lodging.id)}
                      className="flex-1 mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200 touch-target"
                      disabled={isLoading}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Voir
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target"
                          disabled={isLoading}
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={() => handleModify(lodging.id)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Modifier
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(lodging.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Supprimer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleView(lodging.id)}
                      className="flex-1 border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200"
                      disabled={isLoading}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Voir
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleModify(lodging.id)}
                      className="flex-1 border-blue-300 text-blue-700 hover:bg-blue-50 transition-all duration-200"
                      disabled={isLoading}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Modifier
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(lodging.id)}
                      className="flex-1 border-red-300 text-red-700 hover:bg-red-50 transition-all duration-200"
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Supprimer
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredLodgings.length === 0 && (
          <div className="text-center py-12">
            <Home className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Aucun logement trouvé</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm
                ? 'Aucun logement ne correspond à votre recherche.'
                : 'Vous n\'avez pas encore ajouté de logement.'
              }
            </p>
            <Link href="/ajouter-logement">
              <Button className="mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target">
                <Plus className="w-4 h-4 mr-2" />
                Ajouter votre premier logement
              </Button>
            </Link>
          </div>
        )}

        {/* Enhanced Mobile-First Delete Dialog */}
        <AlertDialog open={lodgingToDelete !== null} onOpenChange={cancelDelete}>
          <AlertDialogContent className="mobile-card max-w-md mx-auto">
            <AlertDialogHeader className="text-center sm:text-left">
              <AlertDialogTitle className="text-lg font-bold text-gray-900">
                Supprimer ce logement ?
              </AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 mt-2">
                Cette action ne peut pas être annulée. Le logement et toutes ses données associées seront définitivement supprimés.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2 mt-6">
              <AlertDialogCancel
                onClick={cancelDelete}
                className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 touch-target order-2 sm:order-1"
                disabled={isLoading}
              >
                Annuler
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="mobile-button bg-red-600 hover:bg-red-700 text-white touch-target order-1 sm:order-2"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Suppression...
                  </>
                ) : (
                  'Confirmer la suppression'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  )
}

