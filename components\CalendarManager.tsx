import React from 'react'
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"

const reservations = [
  { id: 1, startDate: new Date(2023, 6, 5), endDate: new Date(2023, 6, 10), guest: '<PERSON>' },
  { id: 2, startDate: new Date(2023, 6, 15), endDate: new Date(2023, 6, 20), guest: '<PERSON>' },
  { id: 3, startDate: new Date(2023, 6, 25), endDate: new Date(2023, 6, 30), guest: '<PERSON>' },
]

export function CalendarManager() {
  const [date, setDate] = React.useState<Date | undefined>(new Date())

  const reservationDates = reservations.reduce((acc, reservation) => {
    const dates = []
    let currentDate = new Date(reservation.startDate)
    while (currentDate <= reservation.endDate) {
      dates.push(new Date(currentDate))
      currentDate.setDate(currentDate.getDate() + 1)
    }
    return [...acc, ...dates]
  }, [] as Date[])

  return (
    <div className="space-y-4">
      <Calendar
        mode="single"
        selected={date}
        onSelect={setDate}
        className="rounded-md border shadow"
        modifiers={{
          booked: reservationDates,
        }}
        modifiersStyles={{
          booked: { backgroundColor: 'rgba(239, 68, 68, 0.1)' },
        }}
      />
      <div className="space-y-2">
        <h3 className="font-medium">Réservations:</h3>
        {reservations.map((reservation) => (
          <div key={reservation.id} className="flex items-center justify-between p-2 bg-accent rounded-lg">
            <div>
              <p className="font-medium">{reservation.guest}</p>
              <p className="text-sm text-muted-foreground">
                {reservation.startDate.toLocaleDateString()} - {reservation.endDate.toLocaleDateString()}
              </p>
            </div>
            <Badge>Réservé</Badge>
          </div>
        ))}
      </div>
    </div>
  )
}

