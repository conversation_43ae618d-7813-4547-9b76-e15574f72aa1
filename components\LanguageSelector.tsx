'use client'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export function LanguageSelector() {
  return (
    <Select defaultValue="french">
      <SelectTrigger className="w-full max-w-xs bg-white border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors duration-200 shadow-sm hover:shadow-md">
        <SelectValue placeholder="Sélectionner la langue" />
      </SelectTrigger>
      <SelectContent className="bg-white border-2 border-gray-200 shadow-xl">
        <SelectItem value="english" className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
          <div className="flex items-center gap-3">
            <span className="text-lg">🇺🇸</span>
            <span className="font-medium">English</span>
          </div>
        </SelectItem>
        <SelectItem value="french" className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
          <div className="flex items-center gap-3">
            <span className="text-lg">🇫🇷</span>
            <span className="font-medium">Français</span>
          </div>
        </SelectItem>
        <SelectItem value="arabic" className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
          <div className="flex items-center gap-3">
            <span className="text-lg">🇸🇦</span>
            <span className="font-medium">العربية</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  )
}

