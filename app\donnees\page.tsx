'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  Database, Download, Upload, RefreshCw, FileText, BarChart3,
  Calendar, Clock, CheckCircle, AlertTriangle, Search, Filter
} from 'lucide-react'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock data for exports
const mockExports = [
  {
    id: 1,
    name: "Rapport mensuel - Janvier 2024",
    type: "PDF",
    size: "2.4 MB",
    date: "2024-01-20 14:30",
    status: "completed"
  },
  {
    id: 2,
    name: "Données locataires - Export CSV",
    type: "CSV",
    size: "156 KB",
    date: "2024-01-19 16:45",
    status: "completed"
  },
  {
    id: 3,
    name: "Statistiques revenus - Q1 2024",
    type: "Excel",
    size: "3.1 MB",
    date: "2024-01-18 09:15",
    status: "processing"
  }
]

// Mock data for imports
const mockImports = [
  {
    id: 1,
    name: "Import locataires - Booking.com",
    source: "Booking.com",
    records: 45,
    date: "2024-01-20 10:30",
    status: "success"
  },
  {
    id: 2,
    name: "Import réservations - Airbnb",
    source: "Airbnb",
    records: 23,
    date: "2024-01-19 14:20",
    status: "success"
  },
  {
    id: 3,
    name: "Import propriétés - Manuel",
    source: "Manuel",
    records: 12,
    date: "2024-01-18 11:45",
    status: "error"
  }
]

export default function DonneesPage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Terminé</Badge>
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">En cours</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Erreur</Badge>
      default:
        return <Badge variant="outline">Inconnu</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Gestion des Données
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Importez, exportez et gérez vos données
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Importer
                  </Button>
                  <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                    <Download className="w-4 h-4 mr-2" />
                    Exporter
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container mobile-gap space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-4 mobile-gap">
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Database className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">1.2K</p>
                        <p className="text-sm text-gray-600">Enregistrements</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Download className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">24</p>
                        <p className="text-sm text-gray-600">Exports</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Upload className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">18</p>
                        <p className="text-sm text-gray-600">Imports</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <RefreshCw className="w-5 h-5 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900">3</p>
                        <p className="text-sm text-gray-600">En cours</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Main Content */}
              <Card className="enhanced-card">
                <CardContent className="p-0">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <div className="border-b border-gray-200 px-6 pt-6">
                      <TabsList className="grid w-full grid-cols-4 bg-gray-100 rounded-lg p-1">
                        <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
                        <TabsTrigger value="exports">Exports</TabsTrigger>
                        <TabsTrigger value="imports">Imports</TabsTrigger>
                        <TabsTrigger value="backup">Sauvegarde</TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="overview" className="p-6 space-y-6">
                      {/* Data Summary */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <BarChart3 className="w-5 h-5" />
                              Utilisation des Données
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div>
                              <div className="flex justify-between text-sm mb-2">
                                <span>Stockage utilisé</span>
                                <span>2.4 GB / 10 GB</span>
                              </div>
                              <Progress value={24} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between text-sm mb-2">
                                <span>Locataires</span>
                                <span>156 enregistrements</span>
                              </div>
                              <Progress value={78} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between text-sm mb-2">
                                <span>Propriétés</span>
                                <span>12 enregistrements</span>
                              </div>
                              <Progress value={60} className="h-2" />
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Clock className="w-5 h-5" />
                              Activité Récente
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                  <Download className="w-4 h-4 text-green-600" />
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm font-medium">Export réussi</p>
                                  <p className="text-xs text-gray-500">Il y a 2 heures</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                  <Upload className="w-4 h-4 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm font-medium">Import terminé</p>
                                  <p className="text-xs text-gray-500">Il y a 5 heures</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                  <RefreshCw className="w-4 h-4 text-purple-600" />
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm font-medium">Sauvegarde automatique</p>
                                  <p className="text-xs text-gray-500">Il y a 1 jour</p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Quick Actions */}
                      <Card>
                        <CardHeader>
                          <CardTitle>Actions Rapides</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <Button variant="outline" className="h-20 flex-col">
                              <Download className="w-6 h-6 mb-2" />
                              <span className="text-sm">Export CSV</span>
                            </Button>
                            <Button variant="outline" className="h-20 flex-col">
                              <FileText className="w-6 h-6 mb-2" />
                              <span className="text-sm">Rapport PDF</span>
                            </Button>
                            <Button variant="outline" className="h-20 flex-col">
                              <Upload className="w-6 h-6 mb-2" />
                              <span className="text-sm">Import Excel</span>
                            </Button>
                            <Button variant="outline" className="h-20 flex-col">
                              <RefreshCw className="w-6 h-6 mb-2" />
                              <span className="text-sm">Synchroniser</span>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="exports" className="p-6 space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Historique des Exports</h3>
                        <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                          <Download className="w-4 h-4 mr-2" />
                          Nouvel Export
                        </Button>
                      </div>
                      
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Nom</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Taille</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Statut</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {mockExports.map((export_item) => (
                              <TableRow key={export_item.id}>
                                <TableCell className="font-medium">{export_item.name}</TableCell>
                                <TableCell>{export_item.type}</TableCell>
                                <TableCell>{export_item.size}</TableCell>
                                <TableCell>{export_item.date}</TableCell>
                                <TableCell>{getStatusBadge(export_item.status)}</TableCell>
                                <TableCell>
                                  <Button variant="ghost" size="sm">
                                    <Download className="w-4 h-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </TabsContent>

                    <TabsContent value="imports" className="p-6 space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Historique des Imports</h3>
                        <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                          <Upload className="w-4 h-4 mr-2" />
                          Nouvel Import
                        </Button>
                      </div>
                      
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Nom</TableHead>
                              <TableHead>Source</TableHead>
                              <TableHead>Enregistrements</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Statut</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {mockImports.map((import_item) => (
                              <TableRow key={import_item.id}>
                                <TableCell className="font-medium">{import_item.name}</TableCell>
                                <TableCell>{import_item.source}</TableCell>
                                <TableCell>{import_item.records}</TableCell>
                                <TableCell>{import_item.date}</TableCell>
                                <TableCell>{getStatusBadge(import_item.status)}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </TabsContent>

                    <TabsContent value="backup" className="p-6 space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <RefreshCw className="w-5 h-5" />
                            Sauvegarde Automatique
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">Sauvegarde quotidienne</p>
                              <p className="text-sm text-gray-600">Dernière sauvegarde: Aujourd'hui à 03:00</p>
                            </div>
                            <Badge className="bg-green-100 text-green-800 border-green-200">Activée</Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">Rétention</p>
                              <p className="text-sm text-gray-600">30 jours de sauvegarde</p>
                            </div>
                            <Button variant="outline" size="sm">Configurer</Button>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Sauvegarde Manuelle</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <p className="text-sm text-gray-600">
                              Créez une sauvegarde complète de toutes vos données.
                            </p>
                            <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                              <RefreshCw className="w-4 h-4 mr-2" />
                              Créer une Sauvegarde
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </div>
  )
}
