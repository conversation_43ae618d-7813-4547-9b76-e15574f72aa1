'use client'

import { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { 
  Home, 
  Building2, 
  Hotel, 
  BarChart2, 
  FileText, 
  MessageSquare,
  Plus,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  path: string
  badge?: number
}

const navItems: NavItem[] = [
  {
    id: 'home',
    label: 'Accueil',
    icon: Home,
    path: '/dashboard'
  },
  {
    id: 'properties',
    label: 'Logements',
    icon: Building2,
    path: '/gestion-logements'
  },
  {
    id: 'add',
    label: 'Ajouter',
    icon: Plus,
    path: '/ajouter-logement'
  },
  {
    id: 'nights',
    label: 'Nuités',
    icon: Hotel,
    path: '/renseignement-nuites'
  },
  {
    id: 'stats',
    label: 'Stats',
    icon: BarChart2,
    path: '/statistiques-generales'
  }
]

export function MobileBottomNav() {
  const router = useRouter()
  const pathname = usePathname()

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden">
      {/* Safe area for devices with home indicator */}
      <div className="pb-safe">
        <div className="flex items-center justify-around px-2 py-2">
          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.path || 
              (item.path !== '/dashboard' && pathname.startsWith(item.path))
            
            return (
              <Button
                key={item.id}
                variant="ghost"
                size="sm"
                className={cn(
                  "flex flex-col items-center justify-center h-16 w-16 rounded-xl transition-all duration-200 relative",
                  isActive 
                    ? "bg-institutional-primary/10 text-institutional-primary" 
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                )}
                onClick={() => handleNavigation(item.path)}
              >
                {/* Special styling for Add button */}
                {item.id === 'add' ? (
                  <div className="w-10 h-10 bg-institutional-primary rounded-full flex items-center justify-center mb-1 shadow-lg">
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                ) : (
                  <Icon className={cn(
                    "w-5 h-5 mb-1 transition-transform duration-200",
                    isActive && "scale-110"
                  )} />
                )}
                
                <span className={cn(
                  "text-xs font-medium leading-none",
                  item.id === 'add' && "text-gray-700"
                )}>
                  {item.label}
                </span>
                
                {/* Badge for notifications */}
                {item.badge && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-institutional-accent rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">
                      {item.badge > 9 ? '9+' : item.badge}
                    </span>
                  </div>
                )}
                
                {/* Active indicator */}
                {isActive && item.id !== 'add' && (
                  <div className="absolute bottom-1 w-1 h-1 bg-institutional-primary rounded-full" />
                )}
              </Button>
            )
          })}
        </div>
      </div>
    </div>
  )
}
