'use client'

import { useState, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  MoreHorizontal,
  Pencil,
  XCircle,
  FileText,
  ArrowLeft,
  Search,
  Filter,
  Calendar,
  MapPin,
  User,
  Users,
  Eye,
  Download,
  RefreshCw
} from 'lucide-react'
import { RechercheNuitesSidebar } from './RechercheNuitesSidebar'
import { DeclarationSummaryModal } from './DeclarationSummaryModal'
import { mockData } from '@/utils/mockData'
import { useRouter } from 'next/navigation'
import { useIsMobile } from '@/hooks/use-mobile'

type Declaration = {
  id: number;
  nomLogement: string;
  ville: string;
  prenom: string;
  nom: string;
  enfants: number;
  dateArrivee: string;
  dateDepart: string;
  dateTraitement: string;
  dateNaissance?: string;
  numeroPieceIdentite?: string;
  typePieceIdentite?: string;
}

export function RechercheNuites() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [data, setData] = useState<Declaration[]>(mockData)
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [selectedDeclaration, setSelectedDeclaration] = useState<Declaration | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table')

  const filteredData = useMemo(() => {
    return data.filter(item => {
      // Search term filter
      const matchesSearch = !searchTerm ||
        item.nomLogement.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.ville.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.prenom.toLowerCase().includes(searchTerm.toLowerCase())

      // Advanced filters
      const matchesFilters = Object.entries(filters).every(([key, value]) => {
        if (!value) return true
        if (key === 'dateArriveeDebut') {
          return new Date(item.dateArrivee) >= new Date(value)
        }
        if (key === 'dateArriveeFin') {
          return new Date(item.dateArrivee) <= new Date(value)
        }
        return item[key as keyof Declaration].toString().toLowerCase().includes(value.toLowerCase())
      })

      return matchesSearch && matchesFilters
    })
  }, [data, filters, searchTerm])

  const handleFilter = (filterValues: Record<string, string>) => {
    setFilters(filterValues)
  }

  const handleModify = async (id: number) => {
    setIsLoading(true)
    try {
      console.log(`Modify item with id: ${id}`)
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.error('Error modifying declaration:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = async (id: number) => {
    setIsLoading(true)
    try {
      console.log(`Cancel item with id: ${id}`)
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.error('Error canceling declaration:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDetails = (item: Declaration) => {
    setSelectedDeclaration(item)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedDeclaration(null)
  }

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
    } catch (error) {
      console.error('Error refreshing data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 logo-background-content">
      {/* Desktop Sidebar */}
      {!isMobile && <RechercheNuitesSidebar onFilter={handleFilter} />}

      <div className={`${!isMobile ? 'ml-80' : ''} mobile-container py-4 sm:py-6`}>
        {/* Enhanced Mobile-First Header */}
        <div className="space-y-4 sm:space-y-6">
          {/* Back Button and Title */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push('/dashboard')}
                className="touch-target text-gray-600 hover:text-institutional-primary transition-colors duration-200 animate-float-up"
                disabled={isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isMobile ? 'Retour' : 'Retour au dashboard'}
              </Button>

              <div className="animate-float-up-delay-1">
                <h1 className="mobile-text-xl font-bold text-gray-900">
                  {isMobile ? 'Recherche Nuités' : 'Recherche de nuités'}
                </h1>
                <p className="text-gray-600 text-sm sm:text-base mt-1">
                  Consultez et gérez les séjours
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 sm:gap-3 animate-float-up-delay-2">
              <Button
                variant="outline"
                size={isMobile ? "default" : "sm"}
                onClick={handleRefresh}
                disabled={isLoading}
                className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 hover:scale-105 transition-all duration-200 touch-target"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                {isMobile ? 'Actualiser' : 'Actualiser'}
              </Button>

              <Button
                variant="outline"
                size={isMobile ? "default" : "sm"}
                className="mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white hover:scale-105 transition-all duration-200 touch-target"
                disabled={isLoading}
              >
                <Download className="w-4 h-4 mr-2" />
                {isMobile ? 'Export' : 'Exporter'}
              </Button>
            </div>
          </div>

          {/* Enhanced Statistics Cards */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 animate-float-up-delay-2">
            <Card className="enhanced-card p-4 text-center hover:scale-105 transition-all duration-300">
              <div className="space-y-2">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                  <Search className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg sm:text-xl font-bold text-gray-900">{filteredData.length}</p>
                  <p className="text-xs text-gray-600">Résultats</p>
                </div>
              </div>
            </Card>
            
            <Card className="enhanced-card p-4 text-center hover:scale-105 transition-all duration-300">
              <div className="space-y-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto">
                  <Users className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="text-lg sm:text-xl font-bold text-gray-900">{data.length}</p>
                  <p className="text-xs text-gray-600">Total</p>
                </div>
              </div>
            </Card>
            
            <Card className="enhanced-card p-4 text-center hover:scale-105 transition-all duration-300">
              <div className="space-y-2">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto">
                  <MapPin className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-lg sm:text-xl font-bold text-gray-900">
                    {new Set(data.map(item => item.ville)).size}
                  </p>
                  <p className="text-xs text-gray-600">Villes</p>
                </div>
              </div>
            </Card>
            
            <Card className="enhanced-card p-4 text-center hover:scale-105 transition-all duration-300">
              <div className="space-y-2">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mx-auto">
                  <Calendar className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-lg sm:text-xl font-bold text-gray-900">
                    {data.reduce((sum, item) => sum + item.enfants, 0)}
                  </p>
                  <p className="text-xs text-gray-600">Enfants</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Enhanced Search and Filter Bar */}
          <Card className="enhanced-card p-4 sm:p-6 animate-float-up-delay-3">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                {/* Enhanced Search Input */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    className="mobile-input mobile-card pl-10 pr-4 border-gray-200 focus:border-institutional-primary focus:ring-2 focus:ring-institutional-primary/20 transition-all duration-200"
                    placeholder={isMobile ? "Rechercher..." : "Rechercher par nom, logement, ville..."}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    disabled={isLoading}
                  />
                  {searchTerm && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
                      onClick={() => setSearchTerm('')}
                    >
                      <XCircle className="w-3 h-3" />
                    </Button>
                  )}
                </div>

                {/* Mobile Filter Button */}
                {isMobile && (
                  <Button
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className={`mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 hover:scale-105 transition-all duration-200 touch-target ${showFilters ? 'bg-institutional-primary text-white border-institutional-primary' : ''}`}
                    disabled={isLoading}
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    Filtres
                  </Button>
                )}

                {/* Enhanced View Mode Toggle (Desktop) */}
                {!isMobile && (
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-600 font-medium">Vue:</span>
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <Button
                        variant={viewMode === 'table' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('table')}
                        className={`h-8 px-3 text-xs transition-all duration-200 ${viewMode === 'table' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                      >
                        Tableau
                      </Button>
                      <Button
                        variant={viewMode === 'cards' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('cards')}
                        className={`h-8 px-3 text-xs transition-all duration-200 ${viewMode === 'cards' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                      >
                        Cartes
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Quick Filter Tags */}
              {(searchTerm || Object.values(filters).some(v => v)) && (
                <div className="flex flex-wrap gap-2">
                  {searchTerm && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer transition-colors duration-200" onClick={() => setSearchTerm('')}>
                      Recherche: "{searchTerm}"
                      <XCircle className="w-3 h-3 ml-1" />
                    </Badge>
                  )}
                  {Object.entries(filters).map(([key, value]) => value && (
                    <Badge key={key} variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200 cursor-pointer transition-colors duration-200" onClick={() => setFilters(prev => ({ ...prev, [key]: '' }))}>
                      {key}: {value}
                      <XCircle className="w-3 h-3 ml-1" />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* Enhanced Mobile Filters Panel */}
          {isMobile && showFilters && (
            <Card className="enhanced-card animate-slide-up-from-bottom">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900">Filtres avancés</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFilters(false)}
                    className="h-8 w-8 p-0"
                  >
                    <XCircle className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <RechercheNuitesSidebar onFilter={handleFilter} isMobile={true} />
              </CardContent>
            </Card>
          )}

          {/* Enhanced Results Summary */}
          <Card className="enhanced-card p-4 animate-float-up-delay-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="font-medium text-gray-900">
                  {filteredData.length} résultat{filteredData.length !== 1 ? 's' : ''} trouvé{filteredData.length !== 1 ? 's' : ''}
                  {searchTerm && ` pour "${searchTerm}"`}
                </span>
              </div>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                {filteredData.length > 0 && (
                  <>
                    <span className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      Dernière mise à jour: {new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                    </span>
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      En temps réel
                    </Badge>
                  </>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Enhanced Data Display */}
        {viewMode === 'table' && !isMobile ? (
          /* Enhanced Desktop Table View */
          <Card className="enhanced-card overflow-hidden animate-float-up-delay-3">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-gray-200 bg-gray-50/50">
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-institutional-primary" />
                        Logement
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-blue-600" />
                        Ville
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-green-600" />
                        Voyageur
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-purple-600" />
                        Enfants
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-orange-600" />
                        Arrivée
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-red-600" />
                        Départ
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">Traitement</TableHead>
                    <TableHead className="text-right font-semibold text-gray-900 py-4">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((item, index) => (
                    <TableRow
                      key={item.id}
                      className="border-b border-gray-100 hover:bg-institutional-primary/5 transition-all duration-200 animate-fade-in group"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <TableCell className="font-medium text-gray-900 py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          {item.nomLogement}
                        </div>
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {item.ville}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        <div className="font-medium">{item.prenom} {item.nom}</div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          {item.enfants} enfant{item.enfants !== 1 ? 's' : ''}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          {item.dateArrivee}
                        </div>
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          {item.dateDepart}
                        </div>
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {item.dateTraitement}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-institutional-primary/10 hover:scale-110 transition-all duration-200 opacity-0 group-hover:opacity-100"
                              disabled={isLoading}
                            >
                              <span className="sr-only">Ouvrir le menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => handleDetails(item)}>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>Voir détails</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleModify(item.id)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              <span>Modifier</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleCancel(item.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <XCircle className="mr-2 h-4 w-4" />
                              <span>Annuler</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        ) : (
          /* Enhanced Mobile/Card View */
          <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 animate-float-up-delay-3">
            {filteredData.map((item, index) => (
              <Card
                key={item.id}
                className="enhanced-card animate-slide-up-from-bottom group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 cursor-pointer relative overflow-hidden"
                style={{ animationDelay: `${index * 100}ms` }}
                onClick={() => handleDetails(item)}
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-institutional-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

                <CardHeader className="pb-3 relative z-10">
                  <div className="flex items-start justify-between gap-3">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg shadow-green-500/50"></div>
                        <Badge variant="outline" className="text-xs bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 border-blue-200">
                          Actif
                        </Badge>
                        <Badge variant="outline" className="text-xs bg-gradient-to-r from-green-50 to-green-100 text-green-700 border-green-200">
                          Confirmé
                        </Badge>
                      </div>
                      <CardTitle className="text-lg sm:text-xl font-bold text-gray-900 line-clamp-2 group-hover:text-institutional-primary transition-colors duration-300">
                        {item.nomLogement}
                      </CardTitle>
                      <div className="flex items-center gap-2 text-gray-600">
                        <div className="w-8 h-8 bg-institutional-primary/10 rounded-lg flex items-center justify-center">
                          <MapPin className="w-4 h-4 text-institutional-primary" />
                        </div>
                        <span className="text-sm font-medium">{item.ville}</span>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-institutional-primary/10 hover:scale-110 transition-all duration-200 touch-target opacity-0 group-hover:opacity-100"
                          disabled={isLoading}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleDetails(item); }}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>Voir détails</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleModify(item.id); }}>
                          <Pencil className="mr-2 h-4 w-4" />
                          <span>Modifier</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => { e.stopPropagation(); handleCancel(item.id); }}
                          className="text-red-600 focus:text-red-600"
                        >
                          <XCircle className="mr-2 h-4 w-4" />
                          <span>Annuler</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>

                <CardContent className="pt-0 space-y-4 relative z-10">
                  {/* Enhanced Guest Info */}
                  <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-xl border border-gray-100 group-hover:from-institutional-primary/5 group-hover:to-blue-50/50 transition-all duration-300">
                    <div className="w-12 h-12 bg-gradient-to-br from-institutional-primary/10 to-institutional-primary/20 rounded-xl flex items-center justify-center shadow-sm">
                      <User className="w-6 h-6 text-institutional-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="font-bold text-gray-900 text-base">
                        {item.prenom} {item.nom}
                      </p>
                      <p className="text-xs text-gray-500 font-medium">Voyageur principal</p>
                    </div>
                    <Badge variant="outline" className="text-xs bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 border-purple-200 shadow-sm">
                      <Users className="w-3 h-3 mr-1" />
                      {item.enfants} enfant{item.enfants !== 1 ? 's' : ''}
                    </Badge>
                  </div>

                  {/* Enhanced Dates with better visual hierarchy */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2 p-3 bg-gradient-to-br from-green-50 to-green-100/50 rounded-lg border border-green-100">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>
                        <span className="text-xs text-green-700 font-bold tracking-wide">ARRIVÉE</span>
                      </div>
                      <p className="text-sm font-bold text-gray-900">{item.dateArrivee}</p>
                    </div>
                    <div className="space-y-2 p-3 bg-gradient-to-br from-red-50 to-red-100/50 rounded-lg border border-red-100">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm"></div>
                        <span className="text-xs text-red-700 font-bold tracking-wide">DÉPART</span>
                      </div>
                      <p className="text-sm font-bold text-gray-900">{item.dateDepart}</p>
                    </div>
                  </div>

                  {/* Enhanced Processing Info with better styling */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      </div>
                      <span className="text-xs text-gray-600 font-medium">Traité le {item.dateTraitement}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs bg-gradient-to-r from-green-100 to-green-200 text-green-800 border-green-300 shadow-sm">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                        Confirmé
                      </Badge>
                    </div>
                  </div>

                  {/* Quick action buttons */}
                  <div className="flex gap-2 pt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 h-8 text-xs border-institutional-primary/20 text-institutional-primary hover:bg-institutional-primary hover:text-white transition-all duration-200"
                      onClick={(e) => { e.stopPropagation(); handleDetails(item); }}
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Détails
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 h-8 text-xs border-blue-200 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-200"
                      onClick={(e) => { e.stopPropagation(); handleModify(item.id); }}
                    >
                      <Pencil className="w-3 h-3 mr-1" />
                      Modifier
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Enhanced Empty State */}
        {filteredData.length === 0 && (
          <Card className="enhanced-card text-center py-16 animate-float-up-delay-3">
            <CardContent className="space-y-6">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <FileText className="w-12 h-12 text-gray-400" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-institutional-primary/10 rounded-full flex items-center justify-center">
                  <Search className="w-4 h-4 text-institutional-primary" />
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-xl font-bold text-gray-900">Aucun résultat trouvé</h3>
                <p className="text-gray-600 max-w-md mx-auto leading-relaxed">
                  {searchTerm || Object.values(filters).some(v => v)
                    ? 'Aucune déclaration ne correspond à vos critères de recherche. Essayez de modifier vos filtres ou votre terme de recherche.'
                    : 'Aucune déclaration de nuités disponible pour le moment. Les nouvelles déclarations apparaîtront ici automatiquement.'
                  }
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                {(searchTerm || Object.values(filters).some(v => v)) && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('')
                      setFilters({})
                    }}
                    className="mobile-button border-institutional-primary text-institutional-primary hover:bg-institutional-primary hover:text-white hover:scale-105 transition-all duration-200 touch-target"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Effacer les filtres
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 hover:scale-105 transition-all duration-200 touch-target"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Actualiser
                </Button>
              </div>

              {/* Helpful tips */}
              <div className="pt-6 border-t border-gray-100">
                <p className="text-sm text-gray-500 mb-3 font-medium">Conseils de recherche :</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-gray-400">
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-institutional-primary rounded-full"></div>
                    <span>Utilisez des mots-clés simples</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-institutional-primary rounded-full"></div>
                    <span>Vérifiez l'orthographe</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-institutional-primary rounded-full"></div>
                    <span>Essayez des termes plus généraux</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-institutional-primary rounded-full"></div>
                    <span>Utilisez les filtres avancés</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Declaration Summary Modal */}
        <DeclarationSummaryModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          declaration={selectedDeclaration}
        />
      </div>
    </div>
  )
}
