# 🚀 Quick Start Guide - Guest Journey Implementation

## Overview
This guide will help you test the complete guest journey implementation that follows the specified user flow with enhanced security, liveness detection, and data management.

## 🎯 User Journey Flow

```
Start → QR/Code Entry → Form Filling → Enhanced Verification → Summary & PDFs → Add Another Guest
```

## 🧪 Testing the Implementation

### 1. Access the Application
- Navigate to the root URL: `/`
- You'll see the welcome page with accommodation code requirement notice

### 2. Test QR Code Scanning
- Click "Scanner avec QR Code"
- Allow camera permissions when prompted
- In development mode, click "🧪 Test QR Scan" for simulation
- Valid format: `T-XXXXXX` (e.g., T-123456)

### 3. Test Manual Code Entry
- Click "Code d'hébergement"
- Try example codes: `T-123456`, `T-789012`, `T-345678`
- The system validates format and simulates verification

### 4. Fill Guest Information
- Complete the comprehensive form with personal details
- Use the "Scanner Document" button to test OCR functionality
- All data is automatically saved to session storage

### 5. Enhanced Verification Process
- **ID Photos**: Capture recto and verso with document frame overlay
- **NFC Simulation**: Progressive scanning with realistic steps
- **Liveness Detection**: Advanced checks including:
  - Face detection
  - Blink detection
  - Smile detection
  - Head movement
  - Eye tracking
- **Digital Signature**: Canvas-based signature with clear/retry options

### 6. Summary and Document Generation
- Review all collected and verified information
- Generate tele-declaration PDF with retry logic
- Generate rental agreement PDF
- Use "Ajouter un autre voyageur" to test multi-guest flow

## 🔧 Development Testing

### Run Automated Tests
```bash
# Navigate to test page
/test-guest-journey

# Or run in browser console
new GuestJourneyTester().runAllTests()
```

### Test Individual Components
1. **Data Storage**: Check session storage persistence
2. **Encryption**: Verify AES encryption/decryption
3. **Liveness Detection**: Test biometric simulation
4. **PDF Generation**: Validate document creation
5. **Network Retry**: Test failure recovery

## 📱 Mobile Testing

### Camera Features
- Test on actual mobile devices for camera access
- Verify touch interactions for signature
- Check responsive layouts on different screen sizes

### Performance
- Test on slower networks to verify retry logic
- Check offline behavior and data persistence
- Validate touch-friendly interface elements

## 🔐 Security Testing

### Data Encryption
- All guest data is encrypted using AES-GCM
- Verify encryption keys are properly generated
- Test decryption accuracy

### Session Management
- Data persists during the journey
- Proper cleanup after completion
- Secure handling of sensitive information

## 📊 Data Storage Verification

### Check Data Folder Structure
```
data/
├── declarations/pdf/     # Generated declarations
├── declarations/json/    # Raw data
├── images/id_documents/  # ID photos
├── images/selfies/       # Selfie photos
├── images/signatures/    # Digital signatures
├── rental_agreements/    # Rental contracts
├── encrypted/           # Encrypted data
└── temp/               # Temporary files
```

### Verify File Generation
- PDFs are created with proper naming conventions
- Images are stored with unique identifiers
- Encrypted data includes all necessary components

## 🚨 Common Issues & Solutions

### Camera Access Issues
- **Problem**: Camera permission denied
- **Solution**: Guide users to browser settings, provide manual entry fallback

### QR Code Scanning Problems
- **Problem**: QR code not detected
- **Solution**: Use manual accommodation code entry option

### PDF Generation Failures
- **Problem**: Network errors during PDF creation
- **Solution**: Automatic retry with exponential backoff (up to 5 attempts)

### Form Data Loss
- **Problem**: Data lost during navigation
- **Solution**: Session storage automatically saves progress

## 🎮 Demo Scenarios

### Scenario 1: Complete Happy Path
1. Start at welcome page
2. Use QR scanner (or test code T-123456)
3. Fill form with OCR assistance
4. Complete all verification steps
5. Generate both PDFs successfully
6. Add another guest

### Scenario 2: Network Issues
1. Start the journey normally
2. Simulate network failure during PDF generation
3. Observe automatic retry logic
4. Verify successful completion after retry

### Scenario 3: Mobile Experience
1. Test on mobile device
2. Use actual camera for document scanning
3. Test touch signature functionality
4. Verify responsive design

## 📈 Performance Benchmarks

### Expected Performance
- **Form Loading**: < 2 seconds
- **OCR Processing**: 3-5 seconds
- **Liveness Detection**: 10-15 seconds
- **PDF Generation**: 2-4 seconds
- **Data Encryption**: < 1 second

### Optimization Tips
- Use WebP images for better compression
- Implement lazy loading for heavy components
- Cache frequently used data
- Optimize PDF generation for faster processing

## 🔍 Debugging Tools

### Browser Console
- Check for any JavaScript errors
- Monitor network requests
- Verify data storage operations

### Network Tab
- Monitor API calls to PDF generation endpoints
- Check retry attempts and timing
- Verify proper error handling

### Application Tab
- Inspect session storage data
- Verify data persistence
- Check for memory leaks

## 📞 Support Information

### For Developers
- Check `GUEST_JOURNEY_IMPLEMENTATION.md` for detailed technical documentation
- Review component source code for implementation details
- Use the test page for comprehensive validation

### For Users
- Clear instructions provided at each step
- Error messages guide users to solutions
- Fallback options available for technical issues

## 🎯 Success Criteria

### Technical Validation
- [ ] All automated tests pass
- [ ] PDFs generate successfully
- [ ] Data encryption works correctly
- [ ] Network retry logic functions
- [ ] Mobile experience is smooth

### User Experience Validation
- [ ] Journey completion rate > 95%
- [ ] Average completion time < 10 minutes
- [ ] Error recovery rate > 90%
- [ ] User satisfaction feedback positive

## 🚀 Next Steps

1. **Run the automated test suite** to verify all components
2. **Test the complete user journey** manually
3. **Verify mobile compatibility** on actual devices
4. **Check PDF generation** with real data
5. **Validate security features** including encryption
6. **Monitor performance** and optimize as needed

---

**Ready to test?** Start at `/` or run the test suite at `/test-guest-journey`!
