import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'

import { Archive, Search, Download, Filter, Calendar, FileText } from 'lucide-react'
import { Input } from "@/components/ui/input"

export default function ArchivesPage() {
  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          <DashboardSidebar />
          <div className="flex-1 bg-gray-50 min-h-full logo-background-content">
            {/* Header */}
            <div className="bg-white border-b border-gray-200 mobile-container py-6">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Archives
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Consultez vos documents et données archivés
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline">
                    <Filter className="w-4 h-4 mr-2" />
                    Filtrer
                  </Button>
                  <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                    <Download className="w-4 h-4 mr-2" />
                    Exporter
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mobile-container py-6 space-y-6">
              {/* Search and Filters */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Rechercher dans les archives..."
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Calendar className="w-4 h-4 mr-2" />
                        Date
                      </Button>
                      <Button variant="outline" size="sm">
                        <FileText className="w-4 h-4 mr-2" />
                        Type
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Archive Categories */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5 text-blue-600" />
                      Déclarations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">1,247</p>
                    <p className="text-sm text-gray-600">Documents archivés</p>
                    <p className="text-xs text-gray-500 mt-2">Dernière mise à jour: Hier</p>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Archive className="w-5 h-5 text-green-600" />
                      Contrats
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">89</p>
                    <p className="text-sm text-gray-600">Contrats archivés</p>
                    <p className="text-xs text-gray-500 mt-2">Dernière mise à jour: Il y a 2 jours</p>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="w-5 h-5 text-purple-600" />
                      Rapports
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">156</p>
                    <p className="text-sm text-gray-600">Rapports archivés</p>
                    <p className="text-xs text-gray-500 mt-2">Dernière mise à jour: Il y a 1 semaine</p>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Archives */}
              <Card>
                <CardHeader>
                  <CardTitle>Documents Récemment Archivés</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="w-8 h-8 text-blue-600" />
                        <div>
                          <h3 className="font-semibold">Déclarations Juin 2024</h3>
                          <p className="text-sm text-gray-600">45 déclarations • 2.3 MB</p>
                          <p className="text-sm text-gray-500">Archivé le 30 Juin 2024</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Voir
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Archive className="w-8 h-8 text-green-600" />
                        <div>
                          <h3 className="font-semibold">Contrats Q2 2024</h3>
                          <p className="text-sm text-gray-600">23 contrats • 1.8 MB</p>
                          <p className="text-sm text-gray-500">Archivé le 28 Juin 2024</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Voir
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Calendar className="w-8 h-8 text-purple-600" />
                        <div>
                          <h3 className="font-semibold">Rapport Mensuel Mai 2024</h3>
                          <p className="text-sm text-gray-600">Rapport complet • 856 KB</p>
                          <p className="text-sm text-gray-500">Archivé le 25 Juin 2024</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Voir
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      <MobileBottomNav />
    </div>
  )
}
