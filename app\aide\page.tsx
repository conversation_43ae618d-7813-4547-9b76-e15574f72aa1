'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from '@/components/DashboardSidebar'
import { InstitutionalHeader } from '@/components/InstitutionalHeader'
import { MobileBottomNav } from '@/components/MobileBottomNav'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useIsMobile } from '@/hooks/use-mobile'
import {
  HelpCircle, Search, Book, MessageCircle, Phone, Mail,
  FileText, Video, Download, ExternalLink,
  Clock, CheckCircle, AlertCircle
} from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AidePage() {
  const isMobile = useIsMobile()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('faq')
  const [searchTerm, setSearchTerm] = useState('')

  return (
    <div className="min-h-screen bg-gray-50 logo-background">
      <InstitutionalHeader showMobileActions={true} />
      <SidebarProvider>
        <div className="flex min-h-[calc(100vh-80px)]">
          {!isMobile && <DashboardSidebar />}
          <div className={`flex-1 bg-gray-50 min-h-full logo-background-content ${isMobile ? 'pb-20' : ''}`}>
            <div className="bg-white border-b border-gray-200 mobile-container py-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h1 className="mobile-text-xl font-bold text-gray-900">
                    Centre d'Aide
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Trouvez des réponses à vos questions et des guides utiles
                  </p>
                </div>
                <Button 
                  className="bg-institutional-primary hover:bg-green-600 text-white"
                  onClick={() => setActiveTab('contact')}
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Contacter le Support
                </Button>
              </div>
            </div>

            <div className="mobile-container mobile-gap space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="enhanced-card cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Book className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Guides</p>
                        <p className="text-sm text-gray-600">Documentation complète</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Video className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Tutoriels</p>
                        <p className="text-sm text-gray-600">Vidéos explicatives</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="enhanced-card cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <MessageCircle className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">Support</p>
                        <p className="text-sm text-gray-600">Assistance personnalisée</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="enhanced-card">
                <CardContent className="p-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-4 bg-gray-100 rounded-lg p-1 mb-6">
                      <TabsTrigger value="faq">FAQ</TabsTrigger>
                      <TabsTrigger value="guides">Guides</TabsTrigger>
                      <TabsTrigger value="videos">Vidéos</TabsTrigger>
                      <TabsTrigger value="contact">Contact</TabsTrigger>
                    </TabsList>

                    <TabsContent value="faq" className="space-y-6">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Rechercher dans la FAQ..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 border border-gray-200 rounded-lg">
                          <h3 className="font-medium text-gray-900 mb-2">Comment déclarer un nouveau locataire ?</h3>
                          <p className="text-sm text-gray-600">Pour déclarer un nouveau locataire, rendez-vous dans la section Locataires puis cliquez sur Ajouter Locataire.</p>
                        </div>
                        <div className="p-4 border border-gray-200 rounded-lg">
                          <h3 className="font-medium text-gray-900 mb-2">Comment modifier les informations d'un logement ?</h3>
                          <p className="text-sm text-gray-600">Allez dans Gestion des Logements, sélectionnez le logement à modifier, puis cliquez sur Modifier.</p>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="guides" className="space-y-4">
                      <div className="grid gap-4">
                        <div className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium text-gray-900">Guide de démarrage rapide</h3>
                              <p className="text-sm text-gray-600">Apprenez les bases de l'application en 10 minutes</p>
                            </div>
                            <Button variant="outline" size="sm">
                              <Download className="w-4 h-4 mr-2" />
                              Télécharger
                            </Button>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="videos" className="space-y-4">
                      <div className="text-center py-12">
                        <Video className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Tutoriels Vidéo</h3>
                        <p className="text-gray-600 mb-6">
                          Découvrez nos tutoriels vidéo pour apprendre à utiliser l'application
                        </p>
                        <Button className="bg-institutional-primary hover:bg-green-600 text-white">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Voir les Vidéos
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="contact" className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card className="border border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Mail className="w-5 h-5 text-blue-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">Email</p>
                                <p className="text-sm text-gray-600"><EMAIL></p>
                                <p className="text-xs text-gray-500">Réponse sous 24h</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="border border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <Phone className="w-5 h-5 text-green-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">Téléphone</p>
                                <p className="text-sm text-gray-600">+33 1 23 45 67 89</p>
                                <p className="text-xs text-gray-500">Lun-Ven 9h-18h</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SidebarProvider>
      {isMobile && <MobileBottomNav />}
    </div>
  )
}
