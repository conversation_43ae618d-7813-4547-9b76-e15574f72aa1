'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useRouter } from "next/navigation"
import { useIsMobile } from '@/hooks/use-mobile'
import { Camera, CreditCard, Scan, Shield, Pen, Eye, EyeOff, Smartphone, CheckCircle, AlertCircle } from 'lucide-react'
import Image from 'next/image'
import { GuestDataStorage } from '@/utils/dataStorage'
import { LivenessDetector, LivenessResult, performQuickLivenessCheck } from '@/utils/livenessDetection'

type VerificationStep = 'recto' | 'verso' | 'nfc' | 'selfie' | 'signature' | 'complete'

interface LivenessChecks {
  blinkDetected: boolean
  headMovementDetected: boolean
  faceDetected: boolean
  smileDetected?: boolean
  eyeTrackingPassed?: boolean
  overallScore?: number
  confidence?: number
}

export function VerificationSelfCheckIn() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [currentStep, setCurrentStep] = useState<VerificationStep>('recto')
  const [progress, setProgress] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [livenessChecks, setLivenessChecks] = useState<LivenessChecks>({
    blinkDetected: false,
    headMovementDetected: false,
    faceDetected: false,
    smileDetected: false,
    eyeTrackingPassed: false,
    overallScore: 0,
    confidence: 0
  })
  const [livenessDetector, setLivenessDetector] = useState<LivenessDetector | null>(null)
  const [currentInstruction, setCurrentInstruction] = useState<string>('')
  const [nfcProgress, setNfcProgress] = useState(0)
  const [verificationData, setVerificationData] = useState({
    recto: null as string | null,
    verso: null as string | null,
    nfc: false,
    selfie: null as string | null,
    signature: null as string | null
  })

  useEffect(() => {
    const stepOrder: VerificationStep[] = ['recto', 'verso', 'nfc', 'selfie', 'signature', 'complete']
    const currentIndex = stepOrder.indexOf(currentStep)
    setProgress((currentIndex / (stepOrder.length - 1)) * 100)
  }, [currentStep])

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (err) {
      console.error("Error accessing camera:", err)
    }
  }

  const captureImage = async () => {
    if (videoRef.current && canvasRef.current) {
      setIsProcessing(true)
      try {
        const context = canvasRef.current.getContext('2d')
        if (context) {
          // Set canvas dimensions to match video
          canvasRef.current.width = videoRef.current.videoWidth
          canvasRef.current.height = videoRef.current.videoHeight

          context.drawImage(videoRef.current, 0, 0, canvasRef.current.width, canvasRef.current.height)
          const imageDataUrl = canvasRef.current.toDataURL('image/jpeg', 0.9)

          // Save image data using the storage utility
          const imageId = await GuestDataStorage.saveImageData(imageDataUrl, currentStep as 'recto' | 'verso' | 'selfie')

          setVerificationData({ ...verificationData, [currentStep]: imageDataUrl })

          // Add delay for better UX
          setTimeout(() => {
            setIsProcessing(false)
            nextStep()
          }, 1000)
        }
      } catch (error) {
        console.error('Error capturing image:', error)
        setIsProcessing(false)
      }
    }
  }

  const handleNFCScan = async () => {
    setIsProcessing(true)
    setNfcProgress(0)

    try {
      // Simulate progressive NFC scanning with realistic steps
      const steps = [
        { progress: 20, message: 'Détection de la puce NFC...' },
        { progress: 40, message: 'Lecture des données biométriques...' },
        { progress: 60, message: 'Vérification de l\'authenticité...' },
        { progress: 80, message: 'Validation des données...' },
        { progress: 100, message: 'Scan NFC terminé avec succès!' }
      ]

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800))
        setNfcProgress(step.progress)
      }

      // Simulate NFC data extraction
      const nfcData = {
        chipAuthenticated: true,
        documentNumber: 'AB123456',
        expiryDate: '2030-12-31',
        issueDate: '2020-01-01',
        nationality: 'MAR',
        biometricData: 'encrypted_biometric_hash',
        securityFeatures: ['hologram', 'rfid_chip', 'digital_signature']
      }

      // Save NFC data
      GuestDataStorage.updateVerificationData({ nfcData })

      setVerificationData({ ...verificationData, nfc: true })

      setTimeout(() => {
        setIsProcessing(false)
        nextStep()
      }, 1000)

    } catch (error) {
      console.error('NFC scan error:', error)
      setIsProcessing(false)
    }
  }





  const nextStep = () => {
    const stepOrder: VerificationStep[] = ['recto', 'verso', 'nfc', 'selfie', 'signature', 'complete']
    const currentIndex = stepOrder.indexOf(currentStep)
    if (currentIndex < stepOrder.length - 1) {
      setCurrentStep(stepOrder[currentIndex + 1])
    }
  }

  const handleSignatureStart = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true)
    const canvas = signatureCanvasRef.current
    const ctx = canvas?.getContext('2d')
    if (ctx) {
      ctx.beginPath()
      if ('touches' in e) {
        ctx.moveTo(e.touches[0].clientX - canvas!.offsetLeft, e.touches[0].clientY - canvas!.offsetTop)
      } else {
        ctx.moveTo(e.clientX - canvas!.offsetLeft, e.clientY - canvas!.offsetTop)
      }
    }
  }

  const handleSignatureMove = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return
    const canvas = signatureCanvasRef.current
    const ctx = canvas?.getContext('2d')
    if (ctx) {
      if ('touches' in e) {
        ctx.lineTo(e.touches[0].clientX - canvas!.offsetLeft, e.touches[0].clientY - canvas!.offsetTop)
      } else {
        ctx.lineTo(e.clientX - canvas!.offsetLeft, e.clientY - canvas!.offsetTop)
      }
      ctx.stroke()
    }
  }

  const handleSignatureEnd = () => {
    setIsDrawing(false)
  }

  const saveSignature = async () => {
    const canvas = signatureCanvasRef.current
    if (canvas) {
      setIsProcessing(true)
      try {
        const signatureDataUrl = canvas.toDataURL('image/png')

        // Save signature data
        await GuestDataStorage.saveImageData(signatureDataUrl, 'signature')

        setVerificationData({ ...verificationData, signature: signatureDataUrl })

        setTimeout(() => {
          setIsProcessing(false)
          nextStep()
        }, 500)
      } catch (error) {
        console.error('Error saving signature:', error)
        setIsProcessing(false)
      }
    }
  }

  // Enhanced liveness detection for selfie
  const performLivenessCheck = async () => {
    setIsProcessing(true)
    setLivenessChecks({
      blinkDetected: false,
      headMovementDetected: false,
      faceDetected: false,
      smileDetected: false,
      eyeTrackingPassed: false,
      overallScore: 0,
      confidence: 0
    })

    try {
      // Initialize liveness detector
      const detector = new LivenessDetector()
      setLivenessDetector(detector)

      // Start face detection first
      setCurrentInstruction('Positionnez votre visage dans le cadre...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      setLivenessChecks(prev => ({ ...prev, faceDetected: true }))

      // Perform comprehensive liveness detection
      setCurrentInstruction('Début de la vérification de vivacité...')
      const result: LivenessResult = await detector.startDetection()

      // Update UI with progressive results
      const updateInterval = setInterval(() => {
        const progress = detector.getProgress()
        const currentChallenge = detector.getCurrentChallenge()

        if (currentChallenge) {
          setCurrentInstruction(currentChallenge.instruction)
        }

        // Update checks based on progress
        if (progress >= 20) {
          setLivenessChecks(prev => ({ ...prev, blinkDetected: true }))
        }
        if (progress >= 40) {
          setLivenessChecks(prev => ({ ...prev, smileDetected: true }))
        }
        if (progress >= 60) {
          setLivenessChecks(prev => ({ ...prev, headMovementDetected: true }))
        }
        if (progress >= 80) {
          setLivenessChecks(prev => ({ ...prev, eyeTrackingPassed: true }))
        }
        if (progress >= 100) {
          clearInterval(updateInterval)
        }
      }, 500)

      // Wait for completion
      await new Promise(resolve => setTimeout(resolve, 15000)) // Total duration
      clearInterval(updateInterval)

      // Update final results
      setLivenessChecks({
        faceDetected: result.faceDetected,
        blinkDetected: result.blinkDetected,
        headMovementDetected: result.headMovementDetected,
        smileDetected: result.smileDetected,
        eyeTrackingPassed: result.eyeTrackingPassed,
        overallScore: result.overallScore,
        confidence: result.confidence
      })

      // Save comprehensive liveness check results
      GuestDataStorage.updateVerificationData({
        livenessChecks: result
      })

      setCurrentInstruction('Vérification de vivacité terminée!')
      setIsProcessing(false)

      // Validate if liveness check passed
      return LivenessDetector.validateLivenessResult(result)
    } catch (error) {
      console.error('Liveness check error:', error)
      setIsProcessing(false)
      return false
    }
  }

  const captureSelfieWithLiveness = async () => {
    // First perform liveness checks
    const livenessSuccess = await performLivenessCheck()

    if (livenessSuccess) {
      // Then capture the selfie
      await captureImage()
    }
  }



  const renderStepContent = () => {
    switch (currentStep) {
      case 'recto':
      case 'verso':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">
                Photo {currentStep === 'recto' ? 'Recto' : 'Verso'} de la pièce d&apos;identité
              </h2>
              <p className="text-gray-600 text-sm">
                Positionnez votre document dans le cadre et assurez-vous qu&apos;il soit bien éclairé
              </p>
            </div>

            <div className="relative aspect-video bg-gray-900 rounded-xl overflow-hidden">
              <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />

              {/* Document frame overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-4/5 h-3/5 border-2 border-institutional-primary rounded-lg relative">
                  <div className="absolute -top-2 -left-2 w-6 h-6 border-l-4 border-t-4 border-institutional-primary rounded-tl-lg"></div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 border-r-4 border-t-4 border-institutional-primary rounded-tr-lg"></div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 border-l-4 border-b-4 border-institutional-primary rounded-bl-lg"></div>
                  <div className="absolute -bottom-2 -right-2 w-6 h-6 border-r-4 border-b-4 border-institutional-primary rounded-br-lg"></div>
                </div>
              </div>

              {isProcessing && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                    <p className="text-sm">Traitement en cours...</p>
                  </div>
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={startCamera} variant="outline" className="flex-1 sm:flex-initial">
                <Camera className="w-4 h-4 mr-2" />
                Démarrer la caméra
              </Button>
              <Button
                onClick={captureImage}
                disabled={isProcessing}
                className="flex-1 sm:flex-initial bg-institutional-primary hover:bg-green-600"
              >
                {isProcessing ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Capture...
                  </>
                ) : (
                  <>
                    <Camera className="w-4 h-4 mr-2" />
                    Capturer
                  </>
                )}
              </Button>
            </div>
          </div>
        )
      case 'nfc':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Lecture de la puce NFC/RFID</h2>
              <p className="text-gray-600 text-sm">
                Approchez votre document du lecteur NFC pour vérifier son authenticité
              </p>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 text-center">
              <div className="relative">
                <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-institutional-primary to-blue-600 rounded-full flex items-center justify-center">
                  <Smartphone className="w-12 h-12 text-white" />
                </div>

                {isProcessing && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-32 h-32 border-4 border-institutional-primary/30 border-t-institutional-primary rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {isProcessing ? (
                <div className="space-y-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-institutional-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${nfcProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-600">
                    {nfcProgress < 20 ? 'Détection de la puce NFC...' :
                     nfcProgress < 40 ? 'Lecture des données biométriques...' :
                     nfcProgress < 60 ? 'Vérification de l\'authenticité...' :
                     nfcProgress < 80 ? 'Validation des données...' :
                     'Scan NFC terminé avec succès!'}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-700 font-medium">Placez votre document près de l&apos;appareil</p>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <Scan className="w-4 h-4" />
                    <span>Lecture automatique des données sécurisées</span>
                  </div>
                </div>
              )}
            </div>

            <Button
              onClick={handleNFCScan}
              disabled={isProcessing}
              className="w-full bg-institutional-primary hover:bg-blue-600"
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Lecture en cours...
                </>
              ) : (
                <>
                  <Scan className="w-4 h-4 mr-2" />
                  Démarrer la lecture NFC
                </>
              )}
            </Button>
          </div>
        )
      case 'selfie':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Selfie avec vérification de vivacité</h2>
              <p className="text-gray-600 text-sm">
                Regardez la caméra et suivez les instructions pour prouver votre présence
              </p>
            </div>

            <div className="relative aspect-video bg-gray-900 rounded-xl overflow-hidden">
              <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover scale-x-[-1]" />

              {/* Face detection overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-48 h-64 border-2 border-institutional-primary rounded-full relative">
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                    <div className="w-4 h-4 bg-institutional-primary rounded-full"></div>
                  </div>
                </div>
              </div>

              {isProcessing && (
                <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                  <div className="text-center text-white max-w-xs">
                    <div className="w-12 h-12 border-3 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>

                    {/* Current instruction */}
                    <p className="text-sm mb-4 font-medium">{currentInstruction}</p>

                    {/* Progress indicator */}
                    <div className="w-full bg-white/20 rounded-full h-2 mb-4">
                      <div
                        className="bg-gradient-to-r from-green-400 to-blue-400 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${livenessDetector ? livenessDetector.getProgress() : 0}%`
                        }}
                      ></div>
                    </div>

                    {/* Enhanced checks grid */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex items-center gap-1">
                        {livenessChecks.faceDetected ? (
                          <CheckCircle className="w-3 h-3 text-green-400" />
                        ) : (
                          <div className="w-3 h-3 border border-white/50 rounded-full"></div>
                        )}
                        <span>Visage</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {livenessChecks.blinkDetected ? (
                          <CheckCircle className="w-3 h-3 text-green-400" />
                        ) : (
                          <div className="w-3 h-3 border border-white/50 rounded-full"></div>
                        )}
                        <span>Clignement</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {livenessChecks.smileDetected ? (
                          <CheckCircle className="w-3 h-3 text-green-400" />
                        ) : (
                          <div className="w-3 h-3 border border-white/50 rounded-full"></div>
                        )}
                        <span>Sourire</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {livenessChecks.headMovementDetected ? (
                          <CheckCircle className="w-3 h-3 text-green-400" />
                        ) : (
                          <div className="w-3 h-3 border border-white/50 rounded-full"></div>
                        )}
                        <span>Mouvement</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {livenessChecks.eyeTrackingPassed ? (
                          <CheckCircle className="w-3 h-3 text-green-400" />
                        ) : (
                          <div className="w-3 h-3 border border-white/50 rounded-full"></div>
                        )}
                        <span>Suivi oculaire</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        <span>{livenessChecks.overallScore?.toFixed(0) || 0}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Instructions:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Regardez directement la caméra</li>
                <li>• Clignez des yeux naturellement</li>
                <li>• Bougez légèrement la tête</li>
                <li>• Gardez votre visage dans le cadre</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={startCamera} variant="outline" className="flex-1 sm:flex-initial">
                <Camera className="w-4 h-4 mr-2" />
                Démarrer la caméra
              </Button>
              <Button
                onClick={captureSelfieWithLiveness}
                disabled={isProcessing}
                className="flex-1 sm:flex-initial bg-institutional-primary hover:bg-green-600"
              >
                {isProcessing ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Vérification...
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Démarrer la vérification
                  </>
                )}
              </Button>
            </div>
          </div>
        )
      case 'signature':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Signature électronique</h2>
              <p className="text-gray-600 text-sm">
                Signez dans le cadre ci-dessous avec votre doigt ou un stylet
              </p>
            </div>

            <div className="bg-white border-2 border-gray-300 rounded-xl p-4">
              <canvas
                ref={signatureCanvasRef}
                width={isMobile ? 320 : 400}
                height={200}
                className="w-full border border-gray-200 rounded-lg bg-white cursor-crosshair"
                onMouseDown={handleSignatureStart}
                onMouseMove={handleSignatureMove}
                onMouseUp={handleSignatureEnd}
                onMouseLeave={handleSignatureEnd}
                onTouchStart={handleSignatureStart}
                onTouchMove={handleSignatureMove}
                onTouchEnd={handleSignatureEnd}
              />
              <div className="flex justify-between items-center mt-3">
                <p className="text-xs text-gray-500">Signez ici</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const canvas = signatureCanvasRef.current
                    if (canvas) {
                      const context = canvas.getContext('2d')
                      context?.clearRect(0, 0, canvas.width, canvas.height)
                    }
                  }}
                >
                  Effacer
                </Button>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-amber-800 text-sm font-medium">Déclaration légale</p>
                  <p className="text-amber-700 text-xs mt-1">
                    En signant, je certifie que toutes les informations fournies sont exactes et complètes.
                  </p>
                </div>
              </div>
            </div>

            <Button
              onClick={saveSignature}
              disabled={isProcessing}
              className="w-full bg-institutional-primary hover:bg-green-600"
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Enregistrement...
                </>
              ) : (
                <>
                  <Pen className="w-4 h-4 mr-2" />
                  Enregistrer la signature
                </>
              )}
            </Button>
          </div>
        )
      case 'complete':
        return (
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <div className="absolute inset-0 w-20 h-20 mx-auto rounded-full bg-green-500/20 animate-ping"></div>
            </div>

            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Vérification terminée !</h2>
              <p className="text-gray-600">
                Toutes les étapes de vérification ont été complétées avec succès.
              </p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Documents vérifiés</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>NFC authentifié</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Vivacité confirmée</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Signature capturée</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => {
                  // Mark as completed and navigate to summary
                  GuestDataStorage.updateVerificationData({ status: 'completed' })
                  router.push('/summary-self-check-in')
                }}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Voir le résumé complet
              </Button>

              <p className="text-xs text-gray-500">
                Vos données ont été chiffrées et sécurisées
              </p>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        <Card className="w-full max-w-3xl mx-auto enhanced-card animate-slide-up-from-bottom">
          <CardHeader className="mobile-padding pb-4 text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-institutional-primary to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <CardTitle className="mobile-text-xl font-bold text-gray-900">
                  Vérification Self Check-in
                </CardTitle>
                <p className="text-gray-600 text-sm">
                  Suivez les étapes pour vérifier votre identité
                </p>
              </div>
            </div>

            {/* Enhanced Progress */}
            <div className="space-y-3">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Étape {['recto', 'verso', 'nfc', 'selfie', 'signature', 'complete'].indexOf(currentStep) + 1} sur 6</span>
                <span>{Math.round(progress)}% terminé</span>
              </div>
              <div className="relative">
                <Progress value={progress} className="h-3" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer rounded-full"></div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="mobile-padding pt-0">
            <div className="bg-gray-50 mobile-card p-6">
              {renderStepContent()}
            </div>
          </CardContent>

          <CardFooter className="mobile-padding pt-4 border-t border-gray-100">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full">
              <Button
                variant="outline"
                onClick={() => router.back()}
                disabled={currentStep === 'recto'}
                className="mobile-button btn-outline order-2 sm:order-1"
              >
                Précédent
              </Button>
              <Button
                onClick={nextStep}
                disabled={currentStep === 'complete'}
                className="mobile-button btn-primary flex-1 sm:flex-initial order-1 sm:order-2"
              >
                {currentStep === 'complete' ? 'Terminer' : 'Suivant'}
              </Button>
            </div>
          </CardFooter>
          <canvas ref={canvasRef} style={{ display: 'none' }} />
        </Card>
      </div>
    </div>
  )
}

