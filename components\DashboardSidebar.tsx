'use client'

import { useState } from 'react'
import {
  Home, Hotel, BarChart2, Building2, FileText, ChevronDown, Menu,
  Users, Settings, HelpCircle, Database, Calendar, MessageSquare,
  TrendingUp, PlusCircle, Search, Bell, Star, Activity, Shield,
  MapPin, CreditCard, Briefcase, Clock, Archive, Download
} from 'lucide-react'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useRouter, usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

interface MenuItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  path: string
  badge?: string | number
  isNew?: boolean
}

interface MenuSection {
  id: string
  label: string
  items: MenuItem[]
  collapsible?: boolean
}

export function DashboardSidebar() {
  const router = useRouter()
  const pathname = usePathname()
  const [collapsedSections, setCollapsedSections] = useState<string[]>([])

  const toggleSection = (sectionId: string) => {
    setCollapsedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const isActive = (path: string) => pathname === path

  const menuSections: MenuSection[] = [
    {
      id: 'main',
      label: 'Navigation Principale',
      items: [
        { id: 'home', label: 'Démarrage', icon: Home, path: '/' },
        { id: 'dashboard', label: 'Dashboard', icon: BarChart2, path: '/dashboard', badge: '4' },
      ]
    },
    {
      id: 'property',
      label: 'Gestion Immobilière',
      collapsible: true,
      items: [
        { id: 'properties', label: 'Logements', icon: Building2, path: '/gestion-logements', badge: '12' },
        { id: 'nights', label: 'Nuités', icon: Hotel, path: '/renseignement-nuites', badge: '45' },
        { id: 'tenants', label: 'Locataires', icon: Users, path: '/locataires', badge: '8' },
        { id: 'contracts', label: 'Contrats', icon: FileText, path: '/creer-contrat', isNew: true },
      ]
    },
    {
      id: 'operations',
      label: 'Opérations',
      collapsible: true,
      items: [
        { id: 'communication', label: 'Communication', icon: MessageSquare, path: '/communication', badge: '3' },
        { id: 'data', label: 'Données', icon: Database, path: '/donnees' },
        { id: 'reports', label: 'Rapports', icon: TrendingUp, path: '/rapports' },
        { id: 'calendar', label: 'Calendrier', icon: Calendar, path: '/calendrier' },
      ]
    },
    {
      id: 'business',
      label: 'Entreprise',
      collapsible: true,
      items: [
        { id: 'company', label: 'Société', icon: Briefcase, path: '/societe' },
        { id: 'billing', label: 'Facturation', icon: CreditCard, path: '/facturation' },
        { id: 'archive', label: 'Archives', icon: Archive, path: '/archives' },
      ]
    }
  ]

  const bottomMenuItems: MenuItem[] = [
    { id: 'notifications', label: 'Notifications', icon: Bell, path: '/notifications', badge: '2' },
    { id: 'help', label: 'Aide', icon: HelpCircle, path: '/aide' },
    { id: 'settings', label: 'Paramètres', icon: Settings, path: '/parametres' },
  ]

  return (
    <Sidebar className="border-r border-sidebar-border shadow-lg bg-white">
      <SidebarContent className="bg-white font-sans">
        {/* Header */}
        <div className="p-5 border-b border-slate-200 bg-slate-50/50 sidebar-header">
          <div className="flex items-center space-x-3">
            <div className="w-9 h-9 bg-gradient-to-br from-institutional-primary to-institutional-royal rounded-xl flex items-center justify-center shadow-md">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-base font-bold text-slate-800 tracking-tight">TeleDec</h2>
              <p className="text-xs font-medium text-slate-600 tracking-wide">Système de Déclaration</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-4 bg-white border-b border-slate-200">
          <Button
            className="w-full h-11 bg-gradient-to-r from-institutional-primary to-institutional-royal hover:from-institutional-royal hover:to-institutional-primary text-white font-semibold text-sm shadow-lg hover:shadow-xl quick-action-button relative overflow-hidden transition-all duration-300 rounded-xl"
            onClick={() => router.push('/')}
          >
            <PlusCircle className="w-4 h-4 mr-2" />
            Nouvelle Déclaration
          </Button>
        </div>

        {/* Menu Sections */}
        <div className="flex-1 overflow-y-auto">
          {menuSections.map((section) => (
            <SidebarGroup key={section.id} className="px-3 py-2">
              {section.collapsible ? (
                <Collapsible
                  open={!collapsedSections.includes(section.id)}
                  onOpenChange={() => toggleSection(section.id)}
                >
                  <CollapsibleTrigger asChild>
                    <SidebarGroupLabel className="flex items-center justify-between w-full px-3 py-3 text-xs font-bold text-slate-600 uppercase tracking-widest hover:text-slate-700 cursor-pointer group section-label transition-colors duration-200">
                      <span>{section.label}</span>
                      <ChevronDown className={cn(
                        "w-4 h-4 transition-transform duration-300 text-slate-500 group-hover:text-slate-600",
                        collapsedSections.includes(section.id) && "rotate-180"
                      )} />
                    </SidebarGroupLabel>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-1">
                    <SidebarGroupContent>
                      <SidebarMenu>
                        {section.items.map((item) => (
                          <SidebarMenuItem key={item.id}>
                            <SidebarMenuButton
                              className={cn(
                                "w-full justify-start h-11 px-4 rounded-xl transition-all duration-300 group relative sidebar-menu-button sidebar-menu-item font-semibold text-sm",
                                isActive(item.path)
                                  ? "bg-gradient-to-r from-institutional-primary/10 to-institutional-royal/10 text-institutional-primary border-r-3 border-institutional-primary shadow-md sidebar-active-gradient active"
                                  : "text-slate-700 hover:bg-slate-100 hover:text-slate-800"
                              )}
                              onClick={() => router.push(item.path)}
                            >
                              <item.icon className={cn(
                                "mr-3 h-5 w-5 transition-all duration-300 sidebar-icon",
                                isActive(item.path) ? "text-institutional-primary scale-110" : "text-slate-600 group-hover:text-slate-700 group-hover:scale-105"
                              )} />
                              <span className="font-semibold flex-1 tracking-wide">{item.label}</span>
                              {item.badge && (
                                <Badge
                                  variant={isActive(item.path) ? "default" : "secondary"}
                                  className={cn(
                                    "ml-auto text-xs font-bold",
                                    isActive(item.path)
                                      ? "bg-institutional-primary text-white shadow-sm"
                                      : "bg-slate-100 text-slate-600",
                                    item.id === 'notifications' && "badge-pulse animate-pulse"
                                  )}
                                >
                                  {item.badge}
                                </Badge>
                              )}
                              {item.isNew && (
                                <Badge className="ml-auto bg-institutional-accent/20 text-institutional-accent text-xs font-bold badge-new">
                                  Nouveau
                                </Badge>
                              )}
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        ))}
                      </SidebarMenu>
                    </SidebarGroupContent>
                  </CollapsibleContent>
                </Collapsible>
              ) : (
                <>
                  <SidebarGroupLabel className="px-3 py-3 text-xs font-bold text-slate-600 uppercase tracking-widest">
                    {section.label}
                  </SidebarGroupLabel>
                  <SidebarGroupContent>
                    <SidebarMenu className="space-y-1">
                      {section.items.map((item) => (
                        <SidebarMenuItem key={item.id}>
                          <SidebarMenuButton
                            className={cn(
                              "w-full justify-start h-11 px-4 rounded-xl transition-all duration-300 group relative font-semibold text-sm",
                              isActive(item.path)
                                ? "bg-gradient-to-r from-institutional-primary/10 to-institutional-royal/10 text-institutional-primary border-r-3 border-institutional-primary shadow-md"
                                : "text-slate-700 hover:bg-slate-100 hover:text-slate-800"
                            )}
                            onClick={() => router.push(item.path)}
                          >
                            <item.icon className={cn(
                              "mr-3 h-5 w-5 transition-all duration-300",
                              isActive(item.path) ? "text-institutional-primary scale-110" : "text-slate-600 group-hover:text-slate-700 group-hover:scale-105"
                            )} />
                            <span className="font-semibold flex-1 tracking-wide">{item.label}</span>
                            {item.badge && (
                              <Badge
                                variant={isActive(item.path) ? "default" : "secondary"}
                                className={cn(
                                  "ml-auto text-xs font-bold",
                                  isActive(item.path)
                                    ? "bg-institutional-primary text-white shadow-sm"
                                    : "bg-slate-100 text-slate-600"
                                )}
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </>
              )}
            </SidebarGroup>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-auto border-t border-slate-200 bg-slate-50/30">
          {/* Quick Stats */}
          <div className="p-4 border-b border-slate-200 stats-section">
            <div className="flex items-center justify-between text-xs text-slate-600 mb-3">
              <span className="font-bold uppercase tracking-widest">Activité récente</span>
              <Activity className="w-4 h-4" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs font-semibold text-slate-700">Déclarations</span>
                <span className="text-xs font-bold text-institutional-accent animate-pulse">+12%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs font-semibold text-slate-700">Revenus</span>
                <span className="text-xs font-bold text-institutional-primary">€2.6K</span>
              </div>
            </div>
          </div>

          {/* Bottom Menu */}
          <div className="p-4">
            <SidebarMenu className="space-y-1">
              {bottomMenuItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    className={cn(
                      "w-full justify-start h-10 px-4 rounded-xl transition-all duration-300 group font-semibold text-sm",
                      isActive(item.path)
                        ? "bg-gradient-to-r from-institutional-primary/10 to-institutional-royal/10 text-institutional-primary"
                        : "text-slate-700 hover:bg-slate-100 hover:text-slate-800"
                    )}
                    onClick={() => router.push(item.path)}
                  >
                    <item.icon className={cn(
                      "mr-3 h-5 w-5 transition-all duration-300",
                      isActive(item.path) ? "text-institutional-primary scale-110" : "text-slate-600 group-hover:text-slate-700 group-hover:scale-105"
                    )} />
                    <span className="font-semibold flex-1 tracking-wide">{item.label}</span>
                    {item.badge && (
                      <Badge
                        variant="secondary"
                        className="ml-auto bg-red-100 text-red-700 text-xs font-bold"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </div>

          {/* User Profile */}
          <div className="p-4 border-t border-slate-200">
            <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-slate-100 cursor-pointer transition-all duration-300 user-profile">
              <div className="w-10 h-10 bg-gradient-to-br from-institutional-primary to-institutional-royal rounded-full flex items-center justify-center shadow-md">
                <span className="text-sm font-bold text-white">JD</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-bold text-slate-800 truncate">John Doe</p>
                <p className="text-xs font-semibold text-slate-600 truncate">Propriétaire</p>
              </div>
              <Settings className="w-5 h-5 text-slate-500 transition-all duration-300 hover:rotate-90 hover:text-slate-700" />
            </div>
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  )
}

