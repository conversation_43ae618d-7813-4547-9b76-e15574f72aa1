'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useRouter } from 'next/navigation'
import { useIsMobile } from '@/hooks/use-mobile'
import { QrCode, Home, MapPin, Camera, User, Users, UserCheck, CheckCircle, ArrowLeft, ArrowRight, Copy, RefreshCw } from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import Image from 'next/image'
import { generateUniqueAccommodationCode, generateQRCodeData } from '@/utils/accommodationCode'

const steps = [
  { id: 1, title: "Informations de base", shortTitle: "Base", icon: Home },
  { id: 2, title: "Équipements et Photos", shortTitle: "Photos", icon: Camera },
  { id: 3, title: "Le Propriétaire", shortTitle: "Propriétaire", icon: User },
  { id: 4, title: "Le Gestionnaire", shortTitle: "Gestionnaire", icon: Users },
  { id: 5, title: "Le Directeur", shortTitle: "Directeur", icon: UserCheck },
  { id: 6, title: "Récapitulatif", shortTitle: "Résumé", icon: CheckCircle }
]

const equipements = [
  "Wi-Fi",
  "Climatisation",
  "Chauffage",
  "Cuisine équipée",
  "Lave-linge",
  "Sèche-linge",
  "Télévision",
  "Parking",
  "Piscine",
  "Jacuzzi",
  "Barbecue",
  "Terrasse",
  "Balcon",
  "Vue sur mer",
  "Accès handicapé",
]

const documentTypes = [
  "Carte Nationale d'Identité",
  "Passeport",
  "Carte de Séjour",
  "Autre"
]

const educationLevels = [
  "Baccalauréat",
  "Licence",
  "Master",
  "Doctorat",
  "Autre"
]

export function AjouterLogementForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isOwnerManager, setIsOwnerManager] = useState(false)
  const [isOwnerDirector, setIsOwnerDirector] = useState(false)
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [accommodationCode, setAccommodationCode] = useState<string>('')
  const [isGeneratingCode, setIsGeneratingCode] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    nomLogement: '',
    type: '',
    adresse: '',
    prix: '',
    // Add other form fields as needed
  })
  const router = useRouter()
  const isMobile = useIsMobile()

  const generateAccommodationCode = async () => {
    setIsGeneratingCode(true)
    try {
      const newCode = await generateUniqueAccommodationCode()
      setAccommodationCode(newCode)
      return newCode
    } catch (error) {
      console.error('Error generating accommodation code:', error)
      // Fallback to a simple random code
      const fallbackCode = `T-${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`
      setAccommodationCode(fallbackCode)
      return fallbackCode
    } finally {
      setIsGeneratingCode(false)
    }
  }

  const generateQRCode = async () => {
    try {
      // Generate accommodation code if not already generated
      const code = accommodationCode || await generateAccommodationCode()

      // Generate QR code data
      const qrData = generateQRCodeData(code, {
        name: formData.nomLogement,
        address: formData.adresse,
        checkInUrl: `${window.location.origin}/self-check-in`
      })

      // In a real application, you would generate an actual QR code image
      // For this example, we'll use a placeholder that represents the QR code
      const dummyQrCodeUrl = `/placeholder.svg?height=200&width=200&text=${encodeURIComponent(code)}`
      setQrCodeUrl(dummyQrCodeUrl)

      return { code, qrCodeUrl: dummyQrCodeUrl }
    } catch (error) {
      console.error('Error generating QR code:', error)
      throw error
    }
  }

  const copyAccommodationCode = async () => {
    if (accommodationCode) {
      try {
        await navigator.clipboard.writeText(accommodationCode)
        // You could add a toast notification here
        console.log('Accommodation code copied to clipboard')
      } catch (error) {
        console.error('Failed to copy accommodation code:', error)
      }
    }
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Here you would typically:
      // 1. Generate accommodation code and QR code
      // 2. Save the form data to your backend with the codes
      // 3. Redirect to the dashboard
      const { code, qrCodeUrl } = await generateQRCode()

      // Simulate saving to backend
      const accommodationData = {
        ...formData,
        accommodationCode: code,
        qrCodeUrl: qrCodeUrl,
        createdAt: new Date().toISOString()
      }

      console.log('Saving accommodation:', accommodationData)

      // Wait for a moment to simulate saving
      await new Promise(resolve => setTimeout(resolve, 2000))
      router.push('/dashboard')
    } catch (error) {
      console.error('Error saving property:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission logic here
    console.log('Form submitted')
    router.push('/dashboard')
  }

  const renderOperatorFields = (title: string, showAdditionalFields = false, educationLevel = false) => (
    <div className="space-y-4 sm:space-y-6">
      <div className="mobile-text-lg font-semibold mb-4">{title}</div>
      <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
        <div>
          <Label htmlFor="profil" className="text-sm font-medium text-gray-700">Profil</Label>
          <Select>
            <SelectTrigger id="profil" className="mobile-input mobile-card">
              <SelectValue placeholder="Sélectionner le profil" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="profile1">Profile 1</SelectItem>
              <SelectItem value="profile2">Profile 2</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="nationalite" className="text-sm font-medium text-gray-700">Nationalité</Label>
          <Input id="nationalite" placeholder="Entrez la nationalité" className="mobile-input mobile-card" />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="natureDocument">Nature document</Label>
          <Select>
            <SelectTrigger id="natureDocument">
              <SelectValue placeholder="Sélectionner le type" />
            </SelectTrigger>
            <SelectContent>
              {documentTypes.map((type) => (
                <SelectItem key={type} value={type.toLowerCase()}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="numeroDocument">N° document</Label>
          <Input id="numeroDocument" placeholder="Entrez le numéro" />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="nom">Nom</Label>
          <Input id="nom" placeholder="Entrez le nom" />
        </div>
        <div>
          <Label htmlFor="prenom">Prénom</Label>
          <Input id="prenom" placeholder="Entrez le prénom" />
        </div>
        <div>
          <Label htmlFor="sexe">Sexe</Label>
          <Select>
            <SelectTrigger id="sexe">
              <SelectValue placeholder="Sélectionner" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="M">Masculin</SelectItem>
              <SelectItem value="F">Féminin</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="telephone">N° Tél</Label>
          <Input id="telephone" type="tel" placeholder="Entrez le numéro" />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="Entrez l'email" />
        </div>
      </div>
      {showAdditionalFields && (
        <div>
          <Label htmlFor="dateContrat">Date de Contrat de Gestion</Label>
          <Input id="dateContrat" type="date" />
        </div>
      )}
      {educationLevel && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="niveauScolaire">Niveau Scolaire</Label>
            <Select>
              <SelectTrigger id="niveauScolaire">
                <SelectValue placeholder="Sélectionner le niveau" />
              </SelectTrigger>
              <SelectContent>
                {educationLevels.map((level) => (
                  <SelectItem key={level} value={level.toLowerCase()}>
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="dateRecrutement">Date du Recrutement</Label>
            <Input id="dateRecrutement" type="date" />
          </div>
        </div>
      )}
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="mobile-container py-4 sm:py-6">
        {/* Mobile-First Back Button */}
        <div className="mb-4 sm:mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/dashboard')}
            className="touch-target text-gray-600 hover:text-institutional-primary"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {isMobile ? 'Retour' : 'Retour au dashboard'}
          </Button>
        </div>

        <Card className="w-full max-w-4xl mx-auto bg-white/98 backdrop-blur-2xl shadow-2xl border border-white/20 mobile-card animate-slide-up-from-bottom">
          <CardHeader className="mobile-padding pb-4 sm:pb-6">
            <CardTitle className="mobile-text-xl font-bold text-gray-900 text-center">
              {isMobile ? `Étape ${currentStep}/6` : `Ajouter un Logement - Étape ${currentStep}`}
            </CardTitle>
            <p className="text-center text-gray-600 text-sm sm:text-base mt-2">
              {steps[currentStep - 1].title}
            </p>
          </CardHeader>

          <CardContent className="mobile-padding pt-0">
            {/* Enhanced Mobile-First Progress Indicator */}
            <div className="mb-6 sm:mb-8 space-y-4">
              {/* Step Counter */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-institutional-primary rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{currentStep}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-600">
                    Étape {currentStep} sur {steps.length}
                  </span>
                </div>
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                  {Math.round(((currentStep - 1) / (steps.length - 1)) * 100)}% terminé
                </Badge>
              </div>

              {/* Enhanced Steps Display */}
              <div className="flex gap-2 sm:gap-4 mb-4 overflow-x-auto pb-2 mobile-scrollbar">
                {steps.map((step) => {
                  const Icon = step.icon
                  const isActive = step.id === currentStep
                  const isCompleted = step.id < currentStep

                  return (
                    <div
                      key={step.id}
                      className={`progress-step ${isMobile ? 'w-16' : 'flex-1'}`}
                    >
                      <div
                        className={`progress-step-icon ${
                          isActive
                            ? 'progress-step-active'
                            : isCompleted
                            ? 'progress-step-completed'
                            : 'progress-step-inactive'
                        }`}
                      >
                        {isCompleted ? (
                          <CheckCircle className="w-5 h-5" />
                        ) : (
                          <Icon className="w-5 h-5" />
                        )}
                      </div>
                      <span className={`progress-step-title ${
                        isActive ? 'progress-step-title-active' : 'progress-step-title-inactive'
                      }`}>
                        {isMobile ? step.shortTitle : step.title}
                      </span>
                      {isCompleted && (
                        <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 mt-1">
                          ✓
                        </Badge>
                      )}
                    </div>
                  )
                })}
              </div>

              {/* Enhanced Progress Bar */}
              <div className="relative">
                <div className="h-3 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-institutional-primary via-green-500 to-green-600 rounded-full transition-all duration-700 ease-out relative overflow-hidden"
                    style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Début</span>
                  <span>Terminé</span>
                </div>
              </div>
            </div>

            {/* Enhanced Mobile-First Form */}
            <form onSubmit={(e) => e.preventDefault()} className="animate-float-up-delay-1">
              {currentStep === 1 && (
                <div className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                    <div className="sm:col-span-2">
                      <Label htmlFor="nom" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Home className="w-4 h-4 text-institutional-primary" />
                        Nom du logement
                      </Label>
                      <Input
                        id="nom"
                        placeholder="Entrez le nom du logement"
                        className="mobile-input mobile-card"
                        disabled={isLoading}
                      />
                    </div>

                    <div>
                      <Label htmlFor="type" className="text-sm font-medium text-gray-700">Type de logement</Label>
                      <Select disabled={isLoading}>
                        <SelectTrigger id="type" className="mobile-input mobile-card">
                          <SelectValue placeholder="Sélectionnez le type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="appartement">Appartement</SelectItem>
                          <SelectItem value="maison">Maison</SelectItem>
                          <SelectItem value="villa">Villa</SelectItem>
                          <SelectItem value="riad">Riad</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="region" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-institutional-primary" />
                        Région
                      </Label>
                      <Input
                        id="region"
                        placeholder="Entrez la région"
                        className="mobile-input mobile-card"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="adresse" className="text-sm font-medium text-gray-700">Adresse du logement</Label>
                    <Textarea
                      id="adresse"
                      placeholder="Entrez l'adresse complète"
                      className="mobile-input mobile-card resize-none"
                      rows={isMobile ? 3 : 4}
                      disabled={isLoading}
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                    <div>
                      <Label htmlFor="code-postal" className="text-sm font-medium text-gray-700">Code postal</Label>
                      <Input
                        id="code-postal"
                        placeholder="Entrez le code postal"
                        className="mobile-input mobile-card"
                        disabled={isLoading}
                      />
                    </div>

                    <div>
                      <Label htmlFor="chambres" className="text-sm font-medium text-gray-700">Nombre de chambres</Label>
                      <Input
                        id="chambres"
                        type="number"
                        min="1"
                        placeholder="Ex: 2"
                        className="mobile-input mobile-card"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 mobile-gap">
                    <div>
                      <Label htmlFor="lits" className="text-sm font-medium text-gray-700">Nombre de lits</Label>
                      <Input
                        id="lits"
                        type="number"
                        min="1"
                        placeholder="Ex: 4"
                        className="mobile-input mobile-card"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Décrivez votre logement..."
                      className="mobile-input mobile-card resize-none"
                      rows={isMobile ? 4 : 6}
                      disabled={isLoading}
                    />
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6 sm:space-y-8">
                  {/* Enhanced Equipment Selection */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700 flex items-center gap-2 mb-4">
                      <Camera className="w-4 h-4 text-institutional-primary" />
                      Équipements disponibles
                    </Label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mobile-gap">
                      {equipements.map((item) => (
                        <div key={item} className="flex items-center space-x-3 p-3 bg-gray-50 mobile-card hover:bg-gray-100 transition-colors">
                          <Checkbox
                            id={item}
                            className="touch-target"
                            disabled={isLoading}
                          />
                          <Label
                            htmlFor={item}
                            className="text-sm font-medium text-gray-700 cursor-pointer flex-1"
                          >
                            {item}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Photo Upload */}
                  <div className="space-y-4 sm:space-y-6">
                    <div>
                      <Label htmlFor="photos" className="text-sm font-medium text-gray-700 mb-2 block">
                        Ajouter des photos
                      </Label>
                      <div className="border-2 border-dashed border-gray-300 mobile-card p-6 sm:p-8 text-center hover:border-institutional-primary transition-colors">
                        <Camera className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400 mx-auto mb-4" />
                        <Input
                          id="photos"
                          type="file"
                          multiple
                          accept="image/*"
                          className="hidden"
                          disabled={isLoading}
                        />
                        <Label
                          htmlFor="photos"
                          className="cursor-pointer text-sm text-gray-600 hover:text-institutional-primary transition-colors"
                        >
                          Cliquez pour ajouter des photos ou glissez-déposez
                        </Label>
                        <p className="text-xs text-gray-500 mt-2">
                          PNG, JPG jusqu'à 10MB chacune
                        </p>
                      </div>
                    </div>

                    {/* Photo Preview Grid */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700 mb-3 block">
                        Photos téléchargées
                      </Label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 mobile-gap">
                        {[1, 2, 3].map((index) => (
                          <div
                            key={index}
                            className="aspect-square bg-gray-100 mobile-card flex items-center justify-center hover:bg-gray-200 transition-colors group cursor-pointer"
                          >
                            <div className="text-center">
                              <Camera className="w-6 h-6 text-gray-400 mx-auto mb-2 group-hover:text-gray-600 transition-colors" />
                              <span className="text-xs text-gray-500">Image {index}</span>
                            </div>
                          </div>
                        ))}

                        {/* Add More Button */}
                        <div className="aspect-square border-2 border-dashed border-gray-300 mobile-card flex items-center justify-center hover:border-institutional-primary transition-colors cursor-pointer group">
                          <div className="text-center">
                            <div className="w-8 h-8 bg-institutional-primary/10 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-institutional-primary/20 transition-colors">
                              <Camera className="w-4 h-4 text-institutional-primary" />
                            </div>
                            <span className="text-xs text-gray-600">Ajouter</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

          {currentStep === 3 && renderOperatorFields("Le Propriétaire")}

          {currentStep === 4 && (
            <>
              <div className="mb-4 flex items-center space-x-2">
                <Checkbox
                  id="ownerIsManager"
                  checked={isOwnerManager}
                  onCheckedChange={(checked) => setIsOwnerManager(checked as boolean)}
                />
                <Label htmlFor="ownerIsManager">Le Propriétaire est Gestionnaire</Label>
              </div>
              {!isOwnerManager && renderOperatorFields("Le Gestionnaire", true)}
            </>
          )}

          {currentStep === 5 && (
            <>
              <div className="mb-4 flex items-center space-x-2">
                <Checkbox
                  id="ownerIsDirector"
                  checked={isOwnerDirector}
                  onCheckedChange={(checked) => setIsOwnerDirector(checked as boolean)}
                />
                <Label htmlFor="ownerIsDirector">Le Propriétaire est Directeur</Label>
              </div>
              {!isOwnerDirector && renderOperatorFields("Le Directeur", false, true)}
            </>
          )}

          {currentStep === 6 && (
            <div className="space-y-6">
              {/* Accommodation Code Section */}
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center gap-2">
                  <Home className="w-5 h-5" />
                  Code d'Hébergement
                </h3>
                {accommodationCode ? (
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                      <code className="text-xl font-mono font-bold text-institutional-primary flex-1">
                        {accommodationCode}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyAccommodationCode}
                        className="flex items-center gap-2"
                      >
                        <Copy className="w-4 h-4" />
                        Copier
                      </Button>
                    </div>
                    <p className="text-sm text-blue-700">
                      Ce code unique permettra aux clients d'accéder au self check-in.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Button
                      onClick={generateAccommodationCode}
                      disabled={isGeneratingCode}
                      className="bg-institutional-primary hover:bg-green-600 text-white"
                    >
                      {isGeneratingCode ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Génération...
                        </>
                      ) : (
                        <>
                          <Home className="w-4 h-4 mr-2" />
                          Générer le Code
                        </>
                      )}
                    </Button>
                    <p className="text-sm text-blue-700">
                      Générez un code unique pour ce logement.
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Informations du Logement</h3>
                  <div className="grid gap-2">
                    <div>
                      <span className="font-medium">Nom:</span> {formData.nomLogement || 'Villa Example'}
                    </div>
                    <div>
                      <span className="font-medium">Type:</span> {formData.type || 'Villa'}
                    </div>
                    <div>
                      <span className="font-medium">Adresse:</span> {formData.adresse || '123 Rue Example'}
                    </div>
                    {accommodationCode && (
                      <div>
                        <span className="font-medium">Code:</span>
                        <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-sm font-mono">
                          {accommodationCode}
                        </code>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">QR Code</h3>
                  {qrCodeUrl ? (
                    <div className="flex flex-col items-center gap-4">
                      <Image
                        src={qrCodeUrl}
                        alt="QR Code du logement"
                        width={200}
                        height={200}
                        className="border rounded-lg p-2"
                      />
                      <Button variant="outline" onClick={() => window.open(qrCodeUrl, '_blank')}>
                        Télécharger le QR Code
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-[200px] h-[200px] border rounded-lg flex items-center justify-center">
                        <QrCode className="w-16 h-16 text-muted-foreground" />
                      </div>
                      <Button variant="outline" onClick={generateQRCode}>
                        Générer le QR Code
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              <div className="pt-6 border-t">
                <h3 className="text-lg font-semibold mb-4">Confirmation</h3>
                <p className="text-muted-foreground mb-4">
                  Veuillez vérifier toutes les informations ci-dessus. Une fois confirmé, le logement sera ajouté à votre tableau de bord et un QR code unique sera généré.
                </p>
                <div className="flex items-center space-x-2">
                  <Checkbox id="confirm" />
                  <Label htmlFor="confirm">
                    Je confirme que toutes les informations saisies sont correctes
                  </Label>
                </div>
              </div>
            </div>
          )}
            </form>
          </CardContent>

          {/* Enhanced Mobile-First Footer */}
          <CardFooter className="mobile-padding pt-4 sm:pt-6 border-t border-gray-100">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full">
              {/* Previous Button */}
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1 || isLoading}
                className="mobile-button border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 touch-target order-2 sm:order-1"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Précédent
              </Button>

              {/* Next/Save Button */}
              {currentStep < steps.length ? (
                <Button
                  onClick={handleNext}
                  disabled={isLoading}
                  className="mobile-button bg-gradient-to-r from-institutional-primary to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target flex-1 sm:flex-initial order-1 sm:order-2"
                >
                  Suivant
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="mobile-button bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 touch-target flex-1 sm:flex-initial order-1 sm:order-2 relative overflow-hidden group"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                      <QrCode className="w-5 h-5 mr-2 relative z-10" />
                      <span className="relative z-10">Enregistrer et Générer QR</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

