'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, Search, Filter, Download, Eye, Calendar, 
  MapPin, User, Phone, CheckCircle, Clock, AlertTriangle,
  ChevronLeft, ChevronRight
} from 'lucide-react'
import { adminDeclarations, moroccanCities } from '@/utils/mockData'
import { AdminDeclaration } from '@/types/admin'

interface AdminDeclarationsListProps {
  onViewDetails?: (declaration: AdminDeclaration) => void
}

export function AdminDeclarationsList({ onViewDetails }: AdminDeclarationsListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [cityFilter, setCityFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(20)

  const filteredDeclarations = useMemo(() => {
    return adminDeclarations.filter(declaration => {
      const matchesSearch = !searchQuery ||
        declaration.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
        declaration.prenom.toLowerCase().includes(searchQuery.toLowerCase()) ||
        declaration.guestId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        declaration.numeroPieceIdentite.toLowerCase().includes(searchQuery.toLowerCase()) ||
        declaration.telephone.includes(searchQuery) ||
        declaration.nomLogement.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesStatus = statusFilter === 'all' || declaration.statut === statusFilter
      const matchesCity = cityFilter === 'all' || declaration.ville === cityFilter

      return matchesSearch && matchesStatus && matchesCity
    })
  }, [searchQuery, statusFilter, cityFilter])

  const paginatedDeclarations = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredDeclarations.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredDeclarations, currentPage, itemsPerPage])

  const totalPages = Math.ceil(filteredDeclarations.length / itemsPerPage)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'validée': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'en_cours': return <Clock className="w-4 h-4 text-yellow-500" />
      case 'expirée': return <AlertTriangle className="w-4 h-4 text-red-500" />
      default: return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'validée': return 'bg-green-50 text-green-700 border-green-200'
      case 'en_cours': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'expirée': return 'bg-red-50 text-red-700 border-red-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  return (
    <Card className="bg-white border-0 shadow-sm">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-institutional-primary" />
            Liste des Déclarations
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {filteredDeclarations.length} déclaration{filteredDeclarations.length !== 1 ? 's' : ''}
            </Badge>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="md:col-span-2">
            <Input
              placeholder="Rechercher par nom, ID, CIN, téléphone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Tous les statuts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="validée">Validée</SelectItem>
              <SelectItem value="en_cours">En cours</SelectItem>
              <SelectItem value="expirée">Expirée</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={cityFilter} onValueChange={setCityFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Toutes les villes" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les villes</SelectItem>
              {moroccanCities.map(city => (
                <SelectItem key={city.name} value={city.name}>
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Declarations List */}
        <div className="space-y-3">
          {paginatedDeclarations.map((declaration) => (
            <div
              key={declaration.id}
              className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {/* Status */}
              <div className="flex items-center gap-2">
                {getStatusIcon(declaration.statut)}
                <Badge variant="outline" className={`text-xs ${getStatusColor(declaration.statut)}`}>
                  {declaration.statut}
                </Badge>
              </div>

              {/* Guest Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-gray-900 truncate">
                    {declaration.prenom} {declaration.nom}
                  </h3>
                  <span className="text-xs text-gray-500 font-mono">
                    {declaration.guestId}
                  </span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <User className="w-3 h-3" />
                    <span className="truncate">{declaration.numeroPieceIdentite}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="w-3 h-3" />
                    <span>{declaration.telephone}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{declaration.ville}</span>
                  </div>
                </div>
              </div>

              {/* Accommodation Info */}
              <div className="hidden lg:block min-w-0 flex-1">
                <p className="font-medium text-gray-900 truncate mb-1">
                  {declaration.nomLogement}
                </p>
                <p className="text-sm text-gray-600 truncate">
                  {declaration.adresseLogement}
                </p>
              </div>

              {/* Stay Dates */}
              <div className="text-center min-w-0">
                <div className="flex items-center gap-1 text-sm font-medium text-gray-900 mb-1">
                  <Calendar className="w-3 h-3" />
                  <span>{formatDate(declaration.dateArrivee)}</span>
                </div>
                <p className="text-xs text-gray-500">
                  au {formatDate(declaration.dateDepart)}
                </p>
              </div>

              {/* Declaration Date */}
              <div className="text-center min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {formatDate(declaration.dateDeclaration)}
                </p>
                <p className="text-xs text-gray-500">
                  {declaration.heureDeclaration}
                </p>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewDetails?.(declaration)}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {paginatedDeclarations.length === 0 && (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {filteredDeclarations.length === 0 
                ? 'Aucune déclaration trouvée avec les filtres sélectionnés'
                : 'Aucune déclaration sur cette page'
              }
            </p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Page {currentPage} sur {totalPages} ({filteredDeclarations.length} résultats)
            </p>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Précédent
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Suivant
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
