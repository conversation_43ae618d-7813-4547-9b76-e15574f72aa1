'use client'

import { useState } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { accommodations } from "@/utils/mockData"
import { ChevronLeft, ChevronRight, Filter } from 'lucide-react'
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"

const filterSchema = z.object({
  nomLogement: z.string().optional(),
  ville: z.string().optional(),
  dateArriveeDebut: z.string().optional(),
  dateArriveeFin: z.string().optional(),
})

type FilterValues = z.infer<typeof filterSchema>

interface RechercheNuitesSidebarProps {
  onFilter: (values: FilterValues) => void
}

export function RechercheNuitesSidebar({ onFilter }: RechercheNuitesSidebarProps) {
  const [isOpen, setIsOpen] = useState(true)

  const form = useForm<FilterValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      nomLogement: "",
      ville: "",
      dateArriveeDebut: "",
      dateArriveeFin: "",
    },
  })

  const onSubmit = (values: FilterValues) => {
    onFilter(values)
  }

  return (
    <>
      <div 
        className={`fixed top-0 right-0 h-full bg-background transition-all duration-300 ease-in-out transform
          ${isOpen ? 'translate-x-0 shadow-lg' : 'translate-x-full'}
          border-l z-20`}
        style={{ width: '320px' }}
      >
        <div className="h-full flex flex-col">
          <div className="p-4 border-b flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8"
              onClick={() => setIsOpen(false)}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <h2 className="font-semibold text-lg">Filtres</h2>
            </div>
          </div>
          
          <ScrollArea className="flex-1 p-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="nomLogement"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Nom du logement</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="bg-background">
                            <SelectValue placeholder="Sélectionner un logement" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {accommodations.map((acc) => (
                            <SelectItem key={acc.id} value={acc.name}>
                              {acc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ville"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Ville</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Rechercher par ville" 
                          {...field}
                          className="bg-background" 
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />


                <Separator />

                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Période d'arrivée</h3>
                  <div className="grid gap-4">
                    <FormField
                      control={form.control}
                      name="dateArriveeDebut"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Date de début</FormLabel>
                          <FormControl>
                            <Input 
                              type="date" 
                              {...field}
                              className="bg-background" 
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="dateArriveeFin"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Date de fin</FormLabel>
                          <FormControl>
                            <Input 
                              type="date" 
                              {...field}
                              className="bg-background" 
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </form>
            </Form>
          </ScrollArea>

          <div className="p-4 border-t bg-background">
            <Button 
              onClick={form.handleSubmit(onSubmit)}
              className="w-full"
            >
              Appliquer les filtres
            </Button>
          </div>
        </div>
      </div>

      {/* Toggle button when sidebar is closed */}
      <Button
        variant="outline"
        size="sm"
        className={`fixed top-4 right-4 z-30 transition-opacity duration-300 ${
          isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}
        onClick={() => setIsOpen(true)}
      >
        <Filter className="h-4 w-4 mr-2" />
        Filtres
      </Button>
    </>
  )
}

