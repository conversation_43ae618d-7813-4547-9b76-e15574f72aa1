/**
 * AES Encryption utilities for securing guest data
 */

// Note: In a production environment, you would use a proper encryption library
// and secure key management. This is a simplified implementation for demonstration.

export class DataEncryption {
  private static readonly ALGORITHM = 'AES-GCM'
  private static readonly KEY_LENGTH = 256

  /**
   * Generate a random encryption key
   */
  static async generateKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true,
      ['encrypt', 'decrypt']
    )
  }

  /**
   * Encrypt data using AES-GCM
   */
  static async encryptData(data: string, key: CryptoKey): Promise<{
    encryptedData: ArrayBuffer
    iv: Uint8Array
  }> {
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    
    // Generate a random initialization vector
    const iv = crypto.getRandomValues(new Uint8Array(12))
    
    const encryptedData = await crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      dataBuffer
    )

    return { encryptedData, iv }
  }

  /**
   * Decrypt data using AES-GCM
   */
  static async decryptData(
    encryptedData: ArrayBuffer,
    key: CryptoKey,
    iv: Uint8Array
  ): Promise<string> {
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      encryptedData
    )

    const decoder = new TextDecoder()
    return decoder.decode(decryptedBuffer)
  }

  /**
   * Convert CryptoKey to exportable format
   */
  static async exportKey(key: CryptoKey): Promise<ArrayBuffer> {
    return await crypto.subtle.exportKey('raw', key)
  }

  /**
   * Import key from ArrayBuffer
   */
  static async importKey(keyData: ArrayBuffer): Promise<CryptoKey> {
    return await crypto.subtle.importKey(
      'raw',
      keyData,
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH,
      },
      true,
      ['encrypt', 'decrypt']
    )
  }

  /**
   * Encrypt guest data for secure storage
   */
  static async encryptGuestData(guestData: any): Promise<{
    encryptedData: string
    keyData: string
    iv: string
  }> {
    const key = await this.generateKey()
    const dataString = JSON.stringify(guestData)
    
    const { encryptedData, iv } = await this.encryptData(dataString, key)
    const exportedKey = await this.exportKey(key)

    // Convert to base64 for storage
    const encryptedDataB64 = btoa(String.fromCharCode(...new Uint8Array(encryptedData)))
    const keyDataB64 = btoa(String.fromCharCode(...new Uint8Array(exportedKey)))
    const ivB64 = btoa(String.fromCharCode(...iv))

    return {
      encryptedData: encryptedDataB64,
      keyData: keyDataB64,
      iv: ivB64
    }
  }

  /**
   * Decrypt guest data from storage
   */
  static async decryptGuestData(
    encryptedDataB64: string,
    keyDataB64: string,
    ivB64: string
  ): Promise<any> {
    // Convert from base64
    const encryptedData = new Uint8Array(
      atob(encryptedDataB64).split('').map(char => char.charCodeAt(0))
    ).buffer
    
    const keyData = new Uint8Array(
      atob(keyDataB64).split('').map(char => char.charCodeAt(0))
    ).buffer
    
    const iv = new Uint8Array(
      atob(ivB64).split('').map(char => char.charCodeAt(0))
    )

    const key = await this.importKey(keyData)
    const decryptedString = await this.decryptData(encryptedData, key, iv)
    
    return JSON.parse(decryptedString)
  }
}

/**
 * Generate unique identifier for guest data
 */
export function generateGuestId(): string {
  const timestamp = Date.now().toString(36)
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `guest_${timestamp}_${randomStr}`
}

/**
 * Generate filename with timestamp and guest ID
 */
export function generateFileName(type: string, guestId: string, extension: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')
  const date = timestamp[0]
  const time = timestamp[1].split('.')[0]
  return `${type}_${date}_${time}_${guestId}.${extension}`
}
